# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; For devices where a 'Line' element exists
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 81

[Jack Line]
required-any = any

[Jack Line Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

[Jack Line - Input]
required-any = any

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Line Boost]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Line]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Input Source]
enumeration = select

[Option Input Source:Line]
name = analog-input-linein
required-any = any

[Element Capture Source]
enumeration = select

[Option Capture Source:Line]
name = analog-input-linein
required-any = any

[Element PCM Capture Source]
enumeration = select

[Option PCM Capture Source:Line]
name = analog-input-linein
required-any = any

[Option PCM Capture Source:Line In]
name = analog-input-linein
required-any = any

[Element Mic]
switch = off
volume = off

[Element Dock Mic]
switch = off
volume = off

[Element Internal Mic]
switch = off
volume = off

[Element Front Mic]
switch = off
volume = off

[Element Rear Mic]
switch = off
volume = off

[Element Mic Boost]
switch = off
volume = off

[Element Dock Mic Boost]
switch = off
volume = off

[Element Internal Mic Boost]
switch = off
volume = off

[Element Front Mic Boost]
switch = off
volume = off

[Element Rear Mic Boost]
switch = off
volume = off

[Element Aux]
switch = off
volume = off

[Element Video]
switch = off
volume = off

[Element Mic/Line]
switch = off
volume = off

[Element TV Tuner]
switch = off
volume = off

[Element FM]
switch = off
volume = off

[Element Mic Jack Mode]
enumeration = select

[Option Mic Jack Mode:Line In]
priority = 19
name = input-linein
