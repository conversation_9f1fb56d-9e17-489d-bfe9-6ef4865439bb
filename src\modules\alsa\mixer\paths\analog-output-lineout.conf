# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

[General]
priority = 90
description-key = analog-output-lineout

[Jack Line Out]
required-any = any

[Jack Line Out Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Front Line Out]
required-any = any

[Jack Front Line Out Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Rear Line Out]
required-any = any

[Jack Rear Line Out Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Line Out Front]
required-any = any

[Jack Line Out Front Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Line Out CLFE]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Line Out CLFE Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Line Out Surround]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Line Out Surround Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Line Out Side]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Line Out Side Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Dock Line Out]
required-any = any

[Jack Dock Line Out Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Element Hardware Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Speaker+LO]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right
required-any = any

[Element Headphone+LO]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right
required-any = any

[Element Master Mono]
switch = off
volume = off

[Element Line HP Swap]
switch = off
required-any = any

; This profile path is intended to control line out, let's mute headphones
; else there will be a spike when plugging in headphones
[Element Headphone]
switch = off
volume = off

[Element Headphone,1]
switch = off
volume = off

[Element Headphone2]
switch = off
volume = off

[Element Speaker]
switch = off
volume = off

[Element Desktop Speaker]
switch = off
volume = off

[Element Front]
switch = mute
volume = merge
override-map.1 = all-front
override-map.2 = front-left,front-right

[Element Rear]
switch = mute
volume = merge
override-map.1 = all-rear
override-map.2 = rear-left,rear-right

[Element Surround]
switch = mute
volume = merge
override-map.1 = all-rear
override-map.2 = rear-left,rear-right

[Element Side]
switch = mute
volume = merge
override-map.1 = all-side
override-map.2 = side-left,side-right

[Element Center]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,all-center

[Element LFE]
switch = mute
volume = merge
override-map.1 = lfe
override-map.2 = lfe,lfe

[Element CLFE]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,lfe

[Element Center/LFE]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,lfe

[Element Bass Speaker]
switch = off
volume = off

[Element Speaker Front]
switch = off
volume = off

[Element Speaker Surround]
switch = off
volume = off

[Element Speaker Side]
switch = off
volume = off

[Element Speaker CLFE]
switch = off
volume = off

.include analog-output.conf.common
