EXPORTS
pa_ascii_filter
pa_ascii_valid
pa_bytes_per_second
pa_bytes_snprint
pa_bytes_to_usec
pa_channel_map_can_balance
pa_channel_map_can_fade
pa_channel_map_can_lfe_balance
pa_channel_map_compatible
pa_channel_map_equal
pa_channel_map_has_position
pa_channel_map_init
pa_channel_map_init_auto
pa_channel_map_init_extend
pa_channel_map_init_mono
pa_channel_map_init_stereo
pa_channel_map_mask
pa_channel_map_parse
pa_channel_map_snprint
pa_channel_map_superset
pa_channel_map_to_name
pa_channel_map_to_pretty_name
pa_channel_map_valid
pa_channel_position_from_string
pa_channel_position_to_pretty_string
pa_channel_position_to_string
pa_channels_valid
pa_context_add_autoload
pa_context_connect
pa_context_disconnect
pa_context_drain
pa_context_errno
pa_context_exit_daemon
pa_context_get_autoload_info_by_index
pa_context_get_autoload_info_by_name
pa_context_get_autoload_info_list
pa_context_get_card_info_by_index
pa_context_get_card_info_by_name
pa_context_get_card_info_list
pa_context_get_client_info
pa_context_get_client_info_list
pa_context_get_index
pa_context_get_module_info
pa_context_get_module_info_list
pa_context_get_protocol_version
pa_context_get_sample_info_by_index
pa_context_get_sample_info_by_name
pa_context_get_sample_info_list
pa_context_get_server
pa_context_get_server_info
pa_context_get_server_protocol_version
pa_context_get_sink_info_by_index
pa_context_get_sink_info_by_name
pa_context_get_sink_info_list
pa_context_get_sink_input_info
pa_context_get_sink_input_info_list
pa_context_get_source_info_by_index
pa_context_get_source_info_by_name
pa_context_get_source_info_list
pa_context_get_source_output_info
pa_context_get_source_output_info_list
pa_context_get_state
pa_context_get_tile_size
pa_context_is_local
pa_context_is_pending
pa_context_kill_client
pa_context_kill_sink_input
pa_context_kill_source_output
pa_context_load_cookie_from_file
pa_context_load_module
pa_context_move_sink_input_by_index
pa_context_move_sink_input_by_name
pa_context_move_source_output_by_index
pa_context_move_source_output_by_name
pa_context_new
pa_context_new_with_proplist
pa_context_play_sample
pa_context_play_sample_with_proplist
pa_context_proplist_remove
pa_context_proplist_update
pa_context_ref
pa_context_remove_autoload_by_index
pa_context_remove_autoload_by_name
pa_context_remove_sample
pa_context_rttime_new
pa_context_rttime_restart
pa_context_send_message_to_object
pa_context_set_card_profile_by_index
pa_context_set_card_profile_by_name
pa_context_set_default_sink
pa_context_set_default_source
pa_context_set_event_callback
pa_context_set_name
pa_context_set_port_latency_offset
pa_context_set_sink_input_mute
pa_context_set_sink_input_volume
pa_context_set_sink_mute_by_index
pa_context_set_sink_mute_by_name
pa_context_set_sink_port_by_index
pa_context_set_sink_port_by_name
pa_context_set_sink_volume_by_index
pa_context_set_sink_volume_by_name
pa_context_set_source_mute_by_index
pa_context_set_source_mute_by_name
pa_context_set_source_output_mute
pa_context_set_source_output_volume
pa_context_set_source_port_by_index
pa_context_set_source_port_by_name
pa_context_set_source_volume_by_index
pa_context_set_source_volume_by_name
pa_context_set_state_callback
pa_context_set_subscribe_callback
pa_context_stat
pa_context_subscribe
pa_context_suspend_sink_by_index
pa_context_suspend_sink_by_name
pa_context_suspend_source_by_index
pa_context_suspend_source_by_name
pa_context_unload_module
pa_context_unref
pa_cvolume_avg
pa_cvolume_avg_mask
pa_cvolume_channels_equal_to
pa_cvolume_compatible
pa_cvolume_compatible_with_channel_map
pa_cvolume_dec
pa_cvolume_equal
pa_cvolume_get_balance
pa_cvolume_get_fade
pa_cvolume_get_lfe_balance
pa_cvolume_get_position
pa_cvolume_inc
pa_cvolume_inc_clamp
pa_cvolume_init
pa_cvolume_max
pa_cvolume_max_mask
pa_cvolume_merge
pa_cvolume_min
pa_cvolume_min_mask
pa_cvolume_remap
pa_cvolume_scale
pa_cvolume_scale_mask
pa_cvolume_set
pa_cvolume_set_balance
pa_cvolume_set_fade
pa_cvolume_set_lfe_balance
pa_cvolume_set_position
pa_cvolume_snprint
pa_cvolume_snprint_verbose
pa_cvolume_valid
pa_direction_to_string
pa_direction_valid
pa_encoding_from_string
pa_encoding_to_string
pa_ext_device_manager_delete
pa_ext_device_manager_enable_role_device_priority_routing
pa_ext_device_manager_read
pa_ext_device_manager_reorder_devices_for_role
pa_ext_device_manager_set_device_description
pa_ext_device_manager_set_subscribe_cb
pa_ext_device_manager_subscribe
pa_ext_device_manager_test
pa_ext_device_restore_read_formats
pa_ext_device_restore_read_formats_all
pa_ext_device_restore_save_formats
pa_ext_device_restore_set_subscribe_cb
pa_ext_device_restore_subscribe
pa_ext_device_restore_test
pa_ext_stream_restore_delete
pa_ext_stream_restore_read
pa_ext_stream_restore_set_subscribe_cb
pa_ext_stream_restore_subscribe
pa_ext_stream_restore_test
pa_ext_stream_restore_write
pa_format_info_copy
pa_format_info_free
pa_format_info_free_string_array
pa_format_info_from_sample_spec
pa_format_info_from_string
pa_format_info_get_channel_map
pa_format_info_get_channels
pa_format_info_get_prop_int
pa_format_info_get_prop_int_array
pa_format_info_get_prop_int_range
pa_format_info_get_prop_string
pa_format_info_get_prop_string_array
pa_format_info_get_prop_type
pa_format_info_get_rate
pa_format_info_get_sample_format
pa_format_info_is_compatible
pa_format_info_is_pcm
pa_format_info_new
pa_format_info_set_channel_map
pa_format_info_set_channels
pa_format_info_set_prop_int
pa_format_info_set_prop_int_array
pa_format_info_set_prop_int_range
pa_format_info_set_prop_string
pa_format_info_set_prop_string_array
pa_format_info_set_rate
pa_format_info_set_sample_format
pa_format_info_snprint
pa_format_info_to_sample_spec
pa_format_info_valid
pa_frame_size
pa_get_binary_name
pa_get_fqdn
pa_get_home_dir
pa_get_host_name
pa_get_library_version
pa_get_user_name
pa_gettimeofday
pa_locale_to_utf8
pa_mainloop_api_once
pa_mainloop_dispatch
pa_mainloop_free
pa_mainloop_get_api
pa_mainloop_get_retval
pa_mainloop_iterate
pa_mainloop_new
pa_mainloop_poll
pa_mainloop_prepare
pa_mainloop_quit
pa_mainloop_run
pa_mainloop_set_poll_func
pa_mainloop_wakeup
pa_msleep
pa_operation_cancel
pa_operation_get_state
pa_operation_ref
pa_operation_set_state_callback
pa_operation_unref
pa_parse_sample_format
pa_path_get_filename
pa_proplist_clear
pa_proplist_contains
pa_proplist_copy
pa_proplist_equal
pa_proplist_free
pa_proplist_from_string
pa_proplist_get
pa_proplist_gets
pa_proplist_isempty
pa_proplist_iterate
pa_proplist_key_valid
pa_proplist_new
pa_proplist_set
pa_proplist_setf
pa_proplist_setp
pa_proplist_sets
pa_proplist_size
pa_proplist_to_string
pa_proplist_to_string_sep
pa_proplist_unset
pa_proplist_unset_many
pa_proplist_update
pa_rtclock_now
pa_sample_format_is_be
pa_sample_format_is_le
pa_sample_format_to_string
pa_sample_format_valid
pa_sample_rate_valid
pa_sample_size
pa_sample_size_of_format
pa_sample_spec_equal
pa_sample_spec_init
pa_sample_spec_snprint
pa_sample_spec_valid
pa_signal_done
pa_signal_free
pa_signal_init
pa_signal_new
pa_signal_set_destroy
pa_stream_begin_write
pa_stream_cancel_write
pa_stream_connect_playback
pa_stream_connect_record
pa_stream_connect_upload
pa_stream_cork
pa_stream_disconnect
pa_stream_drain
pa_stream_drop
pa_stream_finish_upload
pa_stream_flush
pa_stream_get_buffer_attr
pa_stream_get_channel_map
pa_stream_get_context
pa_stream_get_device_index
pa_stream_get_device_name
pa_stream_get_format_info
pa_stream_get_index
pa_stream_get_latency
pa_stream_get_monitor_stream
pa_stream_get_sample_spec
pa_stream_get_state
pa_stream_get_time
pa_stream_get_timing_info
pa_stream_get_underflow_index
pa_stream_is_corked
pa_stream_is_suspended
pa_stream_new
pa_stream_new_extended
pa_stream_new_with_proplist
pa_stream_peek
pa_stream_prebuf
pa_stream_proplist_remove
pa_stream_proplist_update
pa_stream_readable_size
pa_stream_ref
pa_stream_set_buffer_attr
pa_stream_set_buffer_attr_callback
pa_stream_set_event_callback
pa_stream_set_latency_update_callback
pa_stream_set_monitor_stream
pa_stream_set_moved_callback
pa_stream_set_name
pa_stream_set_overflow_callback
pa_stream_set_read_callback
pa_stream_set_started_callback
pa_stream_set_state_callback
pa_stream_set_suspended_callback
pa_stream_set_underflow_callback
pa_stream_set_write_callback
pa_stream_trigger
pa_stream_unref
pa_stream_update_sample_rate
pa_stream_update_timing_info
pa_stream_writable_size
pa_stream_write
pa_stream_write_ext_free
pa_strerror
pa_sw_cvolume_divide
pa_sw_cvolume_divide_scalar
pa_sw_cvolume_multiply
pa_sw_cvolume_multiply_scalar
pa_sw_cvolume_snprint_dB
pa_sw_volume_divide
pa_sw_volume_from_dB
pa_sw_volume_from_linear
pa_sw_volume_multiply
pa_sw_volume_snprint_dB
pa_sw_volume_to_dB
pa_sw_volume_to_linear
pa_thread_make_realtime
pa_threaded_mainloop_accept
pa_threaded_mainloop_free
pa_threaded_mainloop_get_api
pa_threaded_mainloop_get_retval
pa_threaded_mainloop_in_thread
pa_threaded_mainloop_lock
pa_threaded_mainloop_new
pa_threaded_mainloop_once_unlocked
pa_threaded_mainloop_set_name
pa_threaded_mainloop_signal
pa_threaded_mainloop_start
pa_threaded_mainloop_stop
pa_threaded_mainloop_unlock
pa_threaded_mainloop_wait
pa_timeval_add
pa_timeval_age
pa_timeval_cmp
pa_timeval_diff
pa_timeval_load
pa_timeval_store
pa_timeval_sub
pa_usec_to_bytes
pa_utf8_filter
pa_utf8_to_locale
pa_utf8_valid
pa_volume_snprint
pa_volume_snprint_verbose
pa_xfree
pa_xmalloc
pa_xmalloc0
pa_xmemdup
pa_xrealloc
pa_xstrdup
pa_xstrndup
