# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; USB gaming headset.
; These headsets usually have two output devices. The first one is meant
; for voice audio, and the second one is meant for everything else.
; The purpose of this unusual design is to provide separate volume
; controls for voice and other audio, which can be useful in gaming.
;
; Works with:
; Steelseries Arctis 7
; Steelseries Arctis Pro Wireless.
; Lucidsound LS31
; Astro A50
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = yes

[Mapping mono-chat]
description-key = gaming-headset-chat
device-strings = hw:%f,0,0
channel-map = mono
paths-output = usb-gaming-headset-output-mono
paths-input = usb-gaming-headset-input
intended-roles = phone

[Mapping stereo-chat]
description-key = gaming-headset-chat
device-strings = hw:%f,0,0
channel-map = left,right
paths-output = usb-gaming-headset-output-stereo
paths-input = usb-gaming-headset-input
intended-roles = phone

[Mapping stereo-game]
description-key = gaming-headset-game
device-strings = hw:%f,1,0
channel-map = left,right
paths-output = usb-gaming-headset-output-stereo
direction = output

[Profile output:mono-chat+output:stereo-game+input:mono-chat]
output-mappings = mono-chat stereo-game
input-mappings = mono-chat
priority = 5100

[Profile output:stereo-game+output:stereo-chat+input:mono-chat]
output-mappings = stereo-game stereo-chat
input-mappings = mono-chat
priority = 5100
