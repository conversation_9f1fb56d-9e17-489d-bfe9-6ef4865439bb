#ifndef footypedefshfoo
#define footypedefshfoo

/***
  This file is part of PulseAudio.

  Copyright 2015 Canonical Ltd.
  Written by <PERSON> <david.he<PERSON><PERSON>@canonical.com>

  PulseAudio is free software; you can redistribute it and/or modify
  it under the terms of the GNU Lesser General Public License as published
  by the Free Software Foundation; either version 2.1 of the License,
  or (at your option) any later version.

  PulseAudio is distributed in the hope that it will be useful, but
  WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
  General Public License for more details.

  You should have received a copy of the GNU Lesser General Public License
  along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.
***/

typedef struct pa_card pa_card;
typedef struct pa_card_profile pa_card_profile;
typedef struct pa_client pa_client;
typedef struct pa_core pa_core;
typedef struct pa_device_port pa_device_port;
typedef struct pa_sink pa_sink;
typedef struct pa_sink_volume_change pa_sink_volume_change;
typedef struct pa_sink_input pa_sink_input;
typedef struct pa_source pa_source;
typedef struct pa_source_volume_change pa_source_volume_change;
typedef struct pa_source_output pa_source_output;


#endif
