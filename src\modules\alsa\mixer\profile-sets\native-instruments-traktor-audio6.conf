# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Native Instruments Audio 6 DJ
;
; This card has three stereo pairs of input and three stereo pairs of
; output
;
; We knowingly only define a subset of the theoretically possible
; mapping combinations as profiles here.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-out-main]
description = Analog Stereo Main
device-strings = hw:%f,0,0
channel-map = left,right

[Mapping analog-stereo-out-a]
description = Analog Stereo Channel A
device-strings = hw:%f,0,1
channel-map = left,right
direction = output

[Mapping analog-stereo-out-b]
description = Analog Stereo Channel B
device-strings = hw:%f,0,1
channel-map = left,right
direction = output

[Mapping analog-stereo-in-main]
description = Analog Stereo Main
device-strings = hw:%f,0,0
channel-map = left,right

[Mapping analog-stereo-in-a]
description = Analog Stereo Channel A
device-strings = hw:%f,0,1
channel-map = left,right
direction = input

[Mapping analog-stereo-in-b]
description = Analog Stereo Channel B
device-strings = hw:%f,0,1
channel-map = left,right
direction = input



[Profile output:analog-stereo-all+input:analog-stereo-all]
description = Analog Stereo Duplex Channels A, B (Headphones)
output-mappings = analog-stereo-out-main analog-stereo-out-a analog-stereo-out-b
input-mappings = analog-stereo-in-main analog-stereo-in-a analog-stereo-in-b
priority = 100
skip-probe = yes

[Profile output:analog-stereo-main+input:analog-stereo-main]
description = Analog Stereo Duplex Channel Main
output-mappings = analog-stereo-out-main
input-mappings = analog-stereo-in-main
priority = 50
skip-probe = yes

[Profile output:analog-stereo-a+input:analog-stereo-a]
description = Analog Stereo Duplex Channel A
output-mappings = analog-stereo-out-a
input-mappings = analog-stereo-in-a
priority = 40
skip-probe = yes

[Profile output:analog-stereo-b+input:analog-stereo-b]
description = Analog Stereo Duplex Channel B
output-mappings = analog-stereo-out-b
input-mappings = analog-stereo-in-b
priority = 30
skip-probe = yes
