# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Path for the second headphone output on dual-headphone machines.
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 98

[Properties]
device.icon_name = audio-headphones

; HP EliteDesk 800 SFF Headphone
[Jack Front Headphone,1]
required-any = any

; HP EliteDesk 800 DM Headphone
[Jack Front Headphone Surround]
required-any = any

[Element Hardware Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master Mono]
switch = off
volume = off

; This profile path is intended to control the second headphones, not
; the first headphones. But it should not hurt if we leave the
; headphone jack enabled nonetheless.
[Element Headphone]
switch = mute
volume = zero

[Element Headphone,1]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Headphone+LO]
switch = mute
volume = zero

[Element Speaker+LO]
switch = off
volume = off

[Element Headphone2]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Speaker]
switch = off
volume = off

[Element Desktop Speaker]
switch = off
volume = off

; On some machines, the Front Volume Control is shared by Headphone and Lineout,
; or Headphone and Speaker, but they have independent Volume Switch. Here only
; use switch to mute Lineout or Speaker.
[Element Front]
switch = off
volume = zero

[Element Rear]
switch = off
volume = off

[Element Surround]
switch = off
volume = off

[Element Side]
switch = off
volume = off

[Element Center]
switch = off
volume = off

[Element LFE]
switch = off
volume = off

[Element Bass Speaker]
switch = off
volume = off

.include analog-output.conf.common
