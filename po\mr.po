# translation of pulseaudio.master-tx.po to Marathi
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2009, 2012.
#
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio.master-tx\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2012-01-30 09:54+0000\n"
"Last-Translator: <PERSON><PERSON> Shedmake <<EMAIL>>\n"
"Language-Team: Marathi <<EMAIL>>\n"
"Language: mr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: KBabel 1.11.4\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#: src/daemon/cmdline.c:113
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow module user requested "
"module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --module-idle-time=SECS           Unload autoloaded modules when idle "
"and\n"
"                                        this time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v                                    Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr} Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level यास लॉग स्तरीय बाब अपेक्षीत आहे (एकतर क्षेत्र 0..4 अंतर्गत संख्यायी किंवा "
"debug, info, notice, warn, error पैकी एक)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:328
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr "अवैध लॉग लक्ष्य: 'syslog', 'stderr' किंवा 'auto' पैकी एक वापरा."

#: src/daemon/cmdline.c:330
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr "अवैध लॉग लक्ष्य: 'syslog', 'stderr' किंवा 'auto' पैकी एक वापरा."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "अवैध पुन्ह सॅम्पल पद्धत '%s'."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/cmdline.c:397
#, fuzzy
msgid "--enable-memfd expects boolean argument"
msgstr "--realtime यास बूलीयन बाब अपेक्षीत आहे"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] अवैध लॉग लक्ष्य '%s'."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] अवैध लॉग स्तर '%s'."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] अवैध पुन्ह सॅम्पल पद्धत '%s'."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] अवैध rlimit '%s'."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] अवैध सॅम्पल स्वरूप '%s'."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] अवैध सॅम्पल दर '%s'."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] अवैध सॅम्पल मार्ग '%s'."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] अवैध मार्ग मॅप '%s'."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] अवैध तुकडे '%s' यांची एकूण संख्या."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] अवैध तुकड्याचे आकार '%s'."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] अवैध nice स्तर '%s'."

#: src/daemon/daemon-conf.c:552
#, fuzzy, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] अवैध सॅम्पल दर '%s'."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "संयोजना फाइल उघडण्यास अपयशी: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"निश्चित मुलभूत वाहिनी मॅपकडे निश्चित एकूण मुलभूत वाहिनी पेक्षा वेगळे वाहिनी संख्या "
"समाविष्टीत आहे."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### संयोजना फाइल: %s पासून वाचा ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "नाव: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "घटक माहिती उपलब्ध नाही\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "आवृत्ती: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "वर्णन: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "लेखक: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "वापरणी: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "एकदा दाखल करा: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "DEPRECATION WARNING: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "मार्ग: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, fuzzy, c-format
msgid "Failed to open module %s: %s"
msgstr "संयोजना फाइल '%s' उघडण्यास अपयशी: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "मूळ lt_dlopen दाखलकर्ता शोधण्यास अपयशी."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "नवीन dl दाखलकर्ता वाटप करण्यास अपयशी."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "bind-now-loader समावेष करण्यास अपयशी."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "वापरकर्ता '%s' शोधणे अशक्य."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "गट '%s' शोधण्यास अपयशी."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "वापरकर्ता '%s' व गट '%s' चे GID जुळत नाही."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "वापरकर्ता '%s' ची मुख्य डिरेक्ट्री '%s' नाही, दुर्लक्ष करत आहे."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "'%s' बनवण्यास अपयशी: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "गट यादी बदलवण्यास अपयशी: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "GID बदलवण्यास अपयशी: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "UID बदलवण्यास अपयशी: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "प्रणाली भर पद्धत या प्लॅटफॉर्म करीता समर्थीत नाही."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "आदेश ओळ वाचण्यास अपयशी."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "डिमन नष्ट करण्यास अपयशी: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr "हा कार्यक्रम रूट नुरूप चालविण्याकरीता नाही (जोपर्यंत --system निश्चित नाही)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "रूट परवानगी आवश्यक."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "प्रणाली घटनांकरीता --start समर्थीत नाही."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""

#: src/daemon/main.c:878
#, fuzzy
msgid "Running in system mode, but --disallow-exit not set."
msgstr "प्रणाली पद्धती अंतर्गत कार्यरत, परंतु --disallow-exit निश्चित केले नाही!"

#: src/daemon/main.c:881
#, fuzzy
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"प्रणाली पद्धती अंतर्गत कार्यरत, परंतु --disallow-module-loading निश्चित केले नाही!"

#: src/daemon/main.c:884
#, fuzzy
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "प्रणाली पद्धती अंतर्गत कार्यरत, SHM पद्धत जबरनरित्या अकार्यान्वीत करत आहे!"

#: src/daemon/main.c:889
#, fuzzy
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr "प्रणाली पद्धती अंतर्गत कार्यरत, रिकामे वेळ जबरनरित्या अकार्यान्वीत करत आहे!"

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "stdio प्राप्त करण्यास अपयशी."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, fuzzy, c-format
msgid "pipe() failed: %s"
msgstr "पाइप अपयशी: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() अपयशी: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() अपयशी: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "डिमन स्टार्टअप अपयशी."

#: src/daemon/main.c:987
#, fuzzy, c-format
msgid "setsid() failed: %s"
msgstr "read() अपयशी: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "मशीन ID प्राप्त करण्यास अपयशी"

#: src/daemon/main.c:1145
#, fuzzy
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"ठिक आहे, तुम्ही PA प्रणाली मोडमध्ये चालवत आहात. कृपया लक्षात ठेवा असे करण्यास फारशी "
"आवश्यकता नाही.\n"
"असे कार्यान्वीत केल्यास, काहिक घटक योग्यप्रकारे कार्य नसेल करत असल्यास त्याला तुम्हीच "
"जबाबदार राहणार.\n"
"प्रणाली मोड दोकादायक आहे यासाठी कृपया http://www.freedesktop.org/wiki/Software/"
"PulseAudio/Documentation/User/WhatIsWrongWithSystemWide/ वाचा."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() अपयशी."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() अपयशी."

#: src/daemon/main.c:1268
#, fuzzy
msgid "command line arguments"
msgstr "खूप जास्त बाब."

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "विना विभाग दाखल केल्यास डिमन प्रारंभ झाले, कार्य करण्यास नकार."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "PulseAudio आवाज प्रणाली"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "PulseAudio आवाज प्रणाली सुरू करा"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "इंपुट"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "डॉकिंग स्टेशन इंपुट"

#: src/modules/alsa/alsa-mixer.c:2710
#, fuzzy
msgid "Docking Station Microphone"
msgstr "डॉकिंग स्टेशन माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2711
#, fuzzy
msgid "Docking Station Line In"
msgstr "डॉकिंग स्टेशन इंपुट"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "लाइन-इन"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
#, fuzzy
msgid "Front Microphone"
msgstr "डॉकिंग स्टेशन माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
#, fuzzy
msgid "Rear Microphone"
msgstr "माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "बाहेरील माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "आंतरीक माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "रेडिओ"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "विडिओ"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "स्वयं गैन कंट्रोल"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "स्वयं गैन कंट्रोल अशक्य"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "बूस्ट"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "बूस्ट अशक्य"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "ऍमप्लिफायर"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "ऍमप्लिफायर अशक्य"

#: src/modules/alsa/alsa-mixer.c:2726
#, fuzzy
msgid "Bass Boost"
msgstr "बूस्ट"

#: src/modules/alsa/alsa-mixer.c:2727
#, fuzzy
msgid "No Bass Boost"
msgstr "बूस्ट अशक्य"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "ऍनलॉग हेडफोन्स्"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "ऍनलॉग इंपुट"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "डॉकिंग स्टेशन माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2802
#, fuzzy
msgid "Headset Microphone"
msgstr "माइक्रोफोन"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "ऍनलॉग आऊटपुट"

#: src/modules/alsa/alsa-mixer.c:2808
#, fuzzy
msgid "Headphones 2"
msgstr "ऍनलॉग हेडफोन्स्"

#: src/modules/alsa/alsa-mixer.c:2809
#, fuzzy
msgid "Headphones Mono Output"
msgstr "ऍनलॉग मोनो आऊटपुट"

#: src/modules/alsa/alsa-mixer.c:2810
#, fuzzy
msgid "Line Out"
msgstr "लाइन-इन"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "ऍनलॉग मोनो आऊटपुट"

#: src/modules/alsa/alsa-mixer.c:2812
#, fuzzy
msgid "Speakers"
msgstr "ऍनलॉग स्टिरीओ"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2814
#, fuzzy
msgid "Digital Output (S/PDIF)"
msgstr "डिजीटल स्टिरीओ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2815
#, fuzzy
msgid "Digital Input (S/PDIF)"
msgstr "डिजीटल स्टिरीओ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2817
#, fuzzy
msgid "Multichannel Output"
msgstr "Null आऊटपुट"

#: src/modules/alsa/alsa-mixer.c:2818
#, fuzzy
msgid "Game Output"
msgstr "Null आऊटपुट"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
#, fuzzy
msgid "Chat Output"
msgstr "Null आऊटपुट"

#: src/modules/alsa/alsa-mixer.c:2821
#, fuzzy
msgid "Chat Input"
msgstr "इंपुट"

#: src/modules/alsa/alsa-mixer.c:2822
#, fuzzy
msgid "Virtual Surround 7.1"
msgstr "ऍनलॉग सर्राउंड 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "ऍनलॉग मोनो"

#: src/modules/alsa/alsa-mixer.c:4564
#, fuzzy
msgid "Analog Mono (Left)"
msgstr "ऍनलॉग मोनो"

#: src/modules/alsa/alsa-mixer.c:4565
#, fuzzy
msgid "Analog Mono (Right)"
msgstr "ऍनलॉग मोनो"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "ऍनलॉग स्टिरीओ"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "मोनो"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "स्टिरीओ"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
#, fuzzy
msgid "Speakerphone"
msgstr "ऍनलॉग स्टिरीओ"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "ऍनलॉग सर्राउंड 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "ऍनलॉग सर्राउंड 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "ऍनलॉग सर्राउंड 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "ऍनलॉग सर्राउंड 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "ऍनलॉग सर्राउंड 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "ऍनलॉग सर्राउंड 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "ऍनलॉग सर्राउंड 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "ऍनलॉग सर्राउंड 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "ऍनलॉग सर्राउंड 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "ऍनलॉग सर्राउंड 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "ऍनलॉग सर्राउंड 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "डिजीटल स्टिरीओ (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "डिजीटल सर्राउंड 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "डिजीटल सर्राउंड 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
#, fuzzy
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "डिजीटल सर्राउंड 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "डिजीटल स्टिरीओ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
#, fuzzy
msgid "Digital Surround 5.1 (HDMI)"
msgstr "डिजीटल सर्राउंड 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "ऍनलॉग मोनो ड्युप्लेक्स्"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "ऍनलॉग स्टिरीओ ड्युप्लेक्स्"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "डिजीटल स्टिरीओ ड्युप्लेक्स् (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4738
#, fuzzy
msgid "Stereo Duplex"
msgstr "ऍनलॉग स्टिरीओ ड्युप्लेक्स्"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "बंद करा"

#: src/modules/alsa/alsa-mixer.c:4840
#, fuzzy, c-format
msgid "%s Output"
msgstr "Null आऊटपुट"

#: src/modules/alsa/alsa-mixer.c:4848
#, fuzzy, c-format
msgid "%s Input"
msgstr "इंपुट"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, fuzzy, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA साधनवर नवीन डेटा लिहण्याकरीता सज्ज झाले, परंतु लिहण्याकरीता काहीच आढळले नाही!\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग आहे. कृपया ही अडचण ALSA डेव्हलपर करीता कळवा.\n"
"POLLOUT द्वारे सज्ज होणे शक्य आहे -- तरी परस्पर snd_pcm_avail() ने 0 पूरविले किंवा इतर "
"मूल्य < min_avail असावे."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, fuzzy, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA साधनवर नवीन डेटा लिहण्याकरीता सज्ज झाले, परंतु लिहण्याकरीता काहीच आढळले नाही!\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग आहे. कृपया ही अडचण ALSA डेव्हलपर करीता कळवा.\n"
"POLLIN द्वारे सज्ज होणे शक्य आहे -- तरी परस्पर snd_pcm_avail() ने 0 पूरविले किंवा इतर "
"मूल्य < min_avail असावे."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, fuzzy, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() ने अपेक्षा पेक्षा मोठे मूल्य पूरवले: %lu बाईटस् (%lu ms).\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग अशू शकते. कृपया या अडचणीस ALSA डेव्हलपर करीता "
"कळवा."
msgstr[1] ""
"snd_pcm_avail() ने अपेक्षा पेक्षा मोठे मूल्य पूरवले: %lu बाईटस् (%lu ms).\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग अशू शकते. कृपया या अडचणीस ALSA डेव्हलपर करीता "
"कळवा."

#: src/modules/alsa/alsa-util.c:1249
#, fuzzy, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() ने अपेक्षा पेक्षा मोठे मूल्य पूरवले: %li बाईटस् (%s% lu ms).\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग अशू शकते. कृपया या अडचणीस ALSA डेव्हलपर करीता "
"कळवा."
msgstr[1] ""
"snd_pcm_delay() ने अपेक्षा पेक्षा मोठे मूल्य पूरवले: %li बाईटस् (%s% lu ms).\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग अशू शकते. कृपया या अडचणीस ALSA डेव्हलपर करीता "
"कळवा."

#: src/modules/alsa/alsa-util.c:1296
#, fuzzy, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail() ने अपेक्षा पेक्षा मोठे मूल्य पूरवले: %lu बाईटस् (%lu ms).\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग अशू शकते. कृपया या अडचणीस ALSA डेव्हलपर करीता "
"कळवा."

#: src/modules/alsa/alsa-util.c:1339
#, fuzzy, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_nmap_begin() ने अपेक्षा पेक्षा मोठे मूल्य पूरवले: %lu बाईटस् (%lu ms).\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग अशू शकते. कृपया या अडचणीस ALSA डेव्हलपर करीता "
"कळवा."
msgstr[1] ""
"snd_pcm_nmap_begin() ने अपेक्षा पेक्षा मोठे मूल्य पूरवले: %lu बाईटस् (%lu ms).\n"
"हे सहसा ALSA ड्राइवर '%s' अंतर्गत बग अशू शकते. कृपया या अडचणीस ALSA डेव्हलपर करीता "
"कळवा."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
#, fuzzy
msgid "Bluetooth Output"
msgstr "ऍनलॉग आऊटपुट"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1971
#, fuzzy
msgid "Headphone"
msgstr "ऍनलॉग हेडफोन्स्"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2042
#, fuzzy
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "हाय फिडेलिटी प्लेबॅक (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
#, fuzzy
msgid "High Fidelity Capture (A2DP Source)"
msgstr "हाय फिडीलीटी कॅपचर (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr ""

#: src/modules/echo-cancel/module-echo-cancel.c:59
#, fuzzy
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"sink_name=<सींक करीता नाव> sink_properties=<सींक करीता गुणधर्म> "
"master=<फिल्टरजोगी सींकचे नाव> format=<चाचणी रूपण> rate=<चाचणी दर> "
"channels=<वाहिनींची संख्या> channel_map=<वाहिनी नकाशा> plugin=<ladspa प्लगइन "
"नाव> label=<ladspa प्लगइन लेबल> control=<इंपुट कंट्रोल मुल्यांची स्वल्पविराम विभाजीत "
"सूची>"

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr ""

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "डम्मी आऊटपुट"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "नल्ल असल्यावरही नेहमी किमान एक सींक लोड करून ठेवा"

#: src/modules/module-always-source.c:35
#, fuzzy
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "नल्ल असल्यावरही नेहमी किमान एक सींक लोड करून ठेवा"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr ""

#: src/modules/module-equalizer-sink.c:72
#, fuzzy
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<सींक करीता नाव> sink_properties=<सींक करीता गुणधर्म> "
"master=<फिल्टरजोगी सींकचे नाव> format=<चाचणी रूपण> rate=<चाचणी दर> "
"channels=<वाहिनींची संख्या> channel_map=<वाहिनी नकाशा> plugin=<ladspa प्लगइन "
"नाव> label=<ladspa प्लगइन लेबल> control=<इंपुट कंट्रोल मुल्यांची स्वल्पविराम विभाजीत "
"सूची>"

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr ""

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr ""

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "आभासी LADSPA सींक"

#: src/modules/module-ladspa-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<सींक करीता नाव> sink_properties=<सींक करीता गुणधर्म> "
"master=<फिल्टरजोगी सींकचे नाव> format=<चाचणी रूपण> rate=<चाचणी दर> "
"channels=<वाहिनींची संख्या> channel_map=<वाहिनी नकाशा> plugin=<ladspa प्लगइन "
"नाव> label=<ladspa प्लगइन लेबल> control=<इंपुट कंट्रोल मुल्यांची स्वल्पविराम विभाजीत "
"सूची>"

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "क्लॉक्ड् NULL सींक"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Null आऊटपुट"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, fuzzy, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "स्रोत माहिती प्राप्त करण्यास अपयशी: %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "आऊट साधणे"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "इंपुट साधणे"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "@HOSTNAME@ वरील ऑडिओ"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr ""

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr ""

#: src/modules/module-virtual-surround-sink.c:50
#, fuzzy
msgid "Virtual surround sink"
msgstr "आभासी LADSPA सींक"

#: src/modules/module-virtual-surround-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<सींक करीता नाव> sink_properties=<सींक करीता गुणधर्म> "
"master=<फिल्टरजोगी सींकचे नाव> format=<चाचणी रूपण> rate=<चाचणी दर> "
"channels=<वाहिनींची संख्या> channel_map=<वाहिनी नकाशा> plugin=<ladspa प्लगइन "
"नाव> label=<ladspa प्लगइन लेबल> control=<इंपुट कंट्रोल मुल्यांची स्वल्पविराम विभाजीत "
"सूची>"

#: src/modules/raop/module-raop-discover.c:295
#, fuzzy
msgid "Unknown device model"
msgstr "अपरिचीत त्रुटी कोड"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr ""

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "PulseAudio आवाज सर्वर"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "समोर मध्यभागी"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "समोर डावीकडे"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "समोर उजवीकडे"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "पाठीमागे भध्यभागी"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "पाठीमागे डावीकडे"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "पाठीमागे उजवीकडे"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr ""

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "समोर डावी-कडील-मध्यभागी"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "समोर उजवी-कडील-मध्यभागी"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "डावी बाजू"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "उजवी बाजू"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "ऑक्जीलरी 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "ऑक्जीलरी 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "ऑक्जीलरी 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "ऑक्जीलरी 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "ऑक्जीलरी 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "ऑक्जीलरी 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "ऑक्जीलरी 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "ऑक्जीलरी 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "ऑक्जीलरी 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "ऑक्जीलरी 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "ऑक्जीलरी 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "ऑक्जीलरी 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "ऑक्जीलरी 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "ऑक्जीलरी 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "ऑक्जीलरी 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "ऑक्जीलरी 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "ऑक्जीलरी 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "ऑक्जीलरी 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "ऑक्जीलरी 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "ऑक्जीलरी 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "ऑक्जीलरी 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "ऑक्जीलरी 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "ऑक्जीलरी 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "ऑक्जीलरी 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "ऑक्जीलरी 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "ऑक्जीलरी 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "ऑक्जीलरी 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "ऑक्जीलरी 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "ऑक्जीलरी 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "ऑक्जीलरी 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "ऑक्जीलरी 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "ऑक्जीलरी 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "वरील मध्य"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "वरील समोरचे मध्य"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "वरील समोरचे डावे"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "वरील समोरचे उजवे"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "वरील पाठीमागचे मध्य"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "वरील पाठीमागचे डावे"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "वरील पाठीमागचे उजवे"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(अवैध)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "सराऊन्ड 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "सराऊन्ड 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "सराऊन्ड 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "सराऊन्ड 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "सराऊन्ड 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
#, fuzzy
msgid "xcb_connect() failed"
msgstr "pa_context_connect() अपयशी: %s"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr ""

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "कुकी डेटा वाचण्यास अपयशी"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "अपरिचीत वाढ '%s' करीता संदेश प्राप्त झाले"

#: src/pulse/direction.c:37
#, fuzzy
msgid "input"
msgstr "इंपुट"

#: src/pulse/direction.c:39
#, fuzzy
msgid "output"
msgstr "Null आऊटपुट"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr ""

#: src/pulse/direction.c:43
#, fuzzy
msgid "invalid"
msgstr "(अवैध)"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr ""

#: src/pulsecore/core-util.h:97
#, fuzzy
msgid "no"
msgstr "मोनो"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "autospawn कुलूप करीता प्रवेश प्राप्य अशक्य."

#: src/pulsecore/log.c:165
#, fuzzy, c-format
msgid "Failed to open target file '%s'."
msgstr "आवाज फाइल उघडण्यास अपयशी."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""

#: src/pulsecore/log.c:651
#, fuzzy
msgid "Invalid log target."
msgstr "[%s:%u] अवैध लॉग लक्ष्य '%s'."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "आंतरीक ऑडिओ"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "मोडेम"

#: src/pulse/error.c:38
msgid "OK"
msgstr "ठिक"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "प्रवेश नकारले"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "अपरिचीत आदेश"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "अवैध बाब"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "घटक अस्तित्वात आहे"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "घटक आढळले नाही"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "जुळवणी नकारली"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "प्रोटोकॉल त्रुटी"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "वेळसमाप्ती"

#: src/pulse/error.c:47
#, fuzzy
msgid "No authentication key"
msgstr "ओळख पटवण्याकरीता कि आढळली नाही"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "आंतरीक त्रुटी"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "वेळसमाप्ती नष्ट झाली"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "घटक नष्ट झाले"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "अवैध सर्वर"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "घटक प्रारंभ अपयशी"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "अयोग्य स्तर"

#: src/pulse/error.c:54
msgid "No data"
msgstr "डेटा आढळला नाही"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "असहत्व प्रोटोकॉल आवृत्ती"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "खूप मोठे"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "समर्थीत नाही"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "अपरिचीत त्रुटी कोड"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "यानुरूप वाढ आढळली नाही"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "जुणी कार्यपद्धत"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "लागू केले आहे असे आढळले नाही"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "क्लाऐंट विभाजीत केले"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "इंपुट/आऊटपुट त्रुटी"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "उपकरन किंव स्रोत व्यस्थ"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "स्ट्रीम रिकामे करण्यास अपयशी: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "प्लेबॅक स्ट्रीम रिकामे झाले."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "सर्व्हर करीता जुळवणी ड्रेन केली."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() अपयशी: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() अपयशी: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "स्ट्रीम यशस्वीरित्या निर्माण केले."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() अपयशी: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "बफर मेट्रीक्स्: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "बफर मेट्रीक्स्: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "उदाहरणार्थ spec '%s', वाहिनी नकाशा '%s' वापरत आहे."

#: src/utils/pacat.c:342
#, fuzzy, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "साधन %s शी जुळले (%u, %s सस्पेंड केले)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "स्ट्रीम त्रुटी: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "स्ट्रीम साधन सस्पेंड केले.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "स्ट्रीम साधन पुनः सुरू केले.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "स्ट्रीम underrun.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "स्ट्रीम overrun.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "स्ट्रीम started.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "स्ट्रीम साधन %s कडे स्थानांतरीत केले (%u, %ssuspended).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "नाही "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "स्ट्रीम बफर गुणधर्म बदलले.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr ""

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr ""

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "जुळवणी स्थापीत केली.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() अपयशी: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() अपयशी: %s"

#: src/utils/pacat.c:497
#, fuzzy, c-format
msgid "Failed to set monitor stream: %s"
msgstr "स्ट्रीम रिकामे करण्यास अपयशी: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() अपयशी: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "जुळवणी अपयशी: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF प्राप्त झाले."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() अपयशी: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() अपयशी: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "संकेत प्राप्त झाले, बाहेर पडत आहे."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "विलंब प्राप्त करण्यास अपयशी: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "वेळ: %0.3f sec; विलंब: %0.0f usec."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() अपयशी: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [options]\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample type, one of s16le, "
"s16be, u8, float32le,\n"
"                                        float32be, ulaw, alaw, s32le, s32be, "
"s24le, s24be,\n"
"                                        s24-32le, s24-32be (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink "
"the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink "
"the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink the stream is being "
"connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --file-format=FFORMAT             Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s शी कंपाई केले\n"
"libpulse %s शी लिंक केले\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "अवैध क्लाएंटचे नाव '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "अवैध स्ट्रीमचे नाव '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "अवैध वाहिनी नकाशा '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "अवैध विलंब संयोजना '%s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "अवैध कार्य वेळ संयोजना '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "अवैध गुणधर्म '%s'"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "अपरिचीत फाइल रूपण %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr ""

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "अवैध सॅम्पल संयोजना"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "खूप जास्त बाब."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "फाइलसाठी सॅम्पल माहिती प्राप्त करण्यास अपयशी."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "आवाज फाइल उघडण्यास अपयशी."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr "सावधानता: निर्देशीत चाचणी संयोजना फाइलमधील संयोजनाशी खोडून पुनः लिहीली जाईल."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "फाइलपासून चाचणी संयोजना माहिती प्राप्त करण्यास अपयशी."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "सावधानता: फाइलपासून वाहिनी नकाशा ओळखण्यास अपयशी."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "वाहिनी नकाशा चाचणी संयोजनाशी जुळत नाही"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "सावधानता: वाहिनी नकाशा फाइलमध्ये लिहण्यास अपयशी."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr "%s स्ट्रीम चाचणी संयोजना '%s' व वाहिनी नकाशा '%s' सह उघडत आहे."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "रेकॉर्डींग"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "प्लेबॅक"

#: src/utils/pacat.c:1162
#, fuzzy
msgid "Failed to set media name."
msgstr "आदेश ओळ वाचण्यास अपयशी."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() अपयशी."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() अपयशी."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() अपयशी."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() अपयशी: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rrttime_new() अपयशी."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() अपयशी."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr ""

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr ""

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr ""

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr ""

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr ""

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:61
msgid "#N"
msgstr ""

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr ""

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr ""

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr ""

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr ""

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr ""

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr ""

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr ""

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr ""

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr ""

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr ""

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr ""

#: src/utils/pacmd.c:82
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"

#: src/utils/pacmd.c:129
#, fuzzy, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s शी कंपाई केले\n"
"libpulse %s शी लिंक केले\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr "PulseAudio डिमन कार्यरत नाही, किंवा सत्र डिमन नुरूप कार्यरत नाही."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "PulseAudio डिमन पूर्णपणे नष्ट करण्यास अपयशी."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "डिमन प्रतिसाद देत नाही."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "आकडेवारी प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:199
#, fuzzy, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "वर्तमानक्षणी वापरणीत आहे: %2$s बाईटस् समाविष्टीत एकूण %1$u ब्लॉक्स् .\n"
msgstr[1] "वर्तमानक्षणी वापरणीत आहे: %2$s बाईटस् समाविष्टीत एकूण %1$u ब्लॉक्स् .\n"

#: src/utils/pactl.c:205
#, fuzzy, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "संपूर्ण कार्यकाळवेळी लागू केले: %2$s बाईटस् समाविष्टीत एकूण %1$u ब्लॉक्स् .\n"
msgstr[1] "संपूर्ण कार्यकाळवेळी लागू केले: %2$s बाईटस् समाविष्टीत एकूण %1$u ब्लॉक्स् .\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "सॅपल कॅशे आकार: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "सर्वर माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, fuzzy, c-format
msgid "%s\n"
msgstr "%s %s"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""

#: src/utils/pactl.c:294
#, fuzzy, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"वापरकर्ता नाव: %s\n"
"आयोजक नाव: %s\n"
"सर्वर नाव: %s\n"
"सर्वर आवृत्ती: %s\n"
"मुलभूत सॅम्पल संयोनजा: %s\n"
"मुलभूत वाहिनी नकाशा: %s\n"
"मुलभूत सींक: %s\n"
"मुलभूत स्रोत: %s\n"
"कुकीज: %08x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr ""

#: src/utils/pactl.c:321
msgid "available"
msgstr ""

#: src/utils/pactl.c:322
msgid "not available"
msgstr ""

#: src/utils/pactl.c:331 src/utils/pactl.c:355
#, fuzzy
msgid "Unknown"
msgstr "अपरिचीत आदेश"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr ""

#: src/utils/pactl.c:335
#, fuzzy
msgid "Line"
msgstr "लाइन-इन"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr ""

#: src/utils/pactl.c:338
msgid "Handset"
msgstr ""

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr ""

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr ""

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr ""

#: src/utils/pactl.c:342
msgid "TV"
msgstr ""

#: src/utils/pactl.c:345
msgid "USB"
msgstr ""

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr ""

#: src/utils/pactl.c:352
msgid "Network"
msgstr ""

#: src/utils/pactl.c:353
#, fuzzy
msgid "Analog"
msgstr "ऍनलॉग मोनो"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "sink माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:664
#, fuzzy, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s%s%s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s%s%s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tपोर्टस्:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ""

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tसक्रीय पोर्ट: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, fuzzy, c-format
msgid "\tFormats:\n"
msgstr "\tपोर्टस्:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "स्रोत माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:849
#, fuzzy, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s%s%s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s%s%s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "n/a"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "विभाग माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "क्लाऐंट माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "कार्ड माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tसंक्षिप्त माहिती:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tसक्रीय संक्षिप्त माहिती: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr ""

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "सींक इंपुट माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:1366
#, fuzzy, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "स्रोत आऊटपुट माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:1489
#, fuzzy, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "सॅम्पल माहिती प्राप्त करण्यास अपयशी: %s"

#: src/utils/pactl.c:1604
#, fuzzy, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "अपयशी: %s"

#: src/utils/pactl.c:1667
#, fuzzy, c-format
msgid "Send message failed: %s"
msgstr "read() अपयशी: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr ""

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr ""

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, fuzzy, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "सॅम्पल अपलोड करण्यास अपयशी: %s"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
msgstr[1] ""

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "सॅम्पल अपलोड करण्यास अपयशी: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "फाइलची अयोग्य समाप्ती"

#: src/utils/pactl.c:2144
msgid "new"
msgstr ""

#: src/utils/pactl.c:2147
msgid "change"
msgstr ""

#: src/utils/pactl.c:2150
msgid "remove"
msgstr ""

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr ""

#: src/utils/pactl.c:2161
msgid "sink"
msgstr ""

#: src/utils/pactl.c:2164
msgid "source"
msgstr ""

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr ""

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr ""

#: src/utils/pactl.c:2173
msgid "module"
msgstr ""

#: src/utils/pactl.c:2176
msgid "client"
msgstr ""

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr ""

#: src/utils/pactl.c:2182
#, fuzzy
msgid "server"
msgstr "अवैध सर्वर"

#: src/utils/pactl.c:2185
msgid "card"
msgstr ""

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr ""

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT प्राप्त झाले, बाहेर पडत आहे."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "अवैध खंडाची संयोजना"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr ""

#: src/utils/pactl.c:2594
#, fuzzy
msgid "Invalid number of volume specifications.\n"
msgstr "अवैध खंडाची संयोजना"

#: src/utils/pactl.c:2606
#, fuzzy
msgid "Inconsistent volume specification.\n"
msgstr "अवैध खंडाची संयोजना"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr ""

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr ""

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr ""

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr ""

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr ""

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"libpulse %s सह कंपाईल केले\n"
"libpulse %s सह जुळले\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "अवैध स्ट्रीमचे नाव '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr ""

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "कृपया दाखल करण्याजोगी तात्पूर्ती फाइल निर्देशीत करा"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "आवाज फाइल उघडण्यास अपयशी."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "सावधानता: फाइलपासून चाचणी संयोजना ओळखण्यास अपयशी."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "चालवण्याकरीता तुम्हाला तात्पूर्ते नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "काढून टाकण्याकरीता तुम्हाला तात्पूर्ते नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "तुम्हाला सींक इंपुट निर्देशांक व सींक निश्चित करावे लागेल"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "तुम्हाला आऊटपुट इंडेक्स स्रोत व स्रोत निश्चित करावे लागेल"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "तुम्हाला विभागाचे नाव व बाब निश्चित करावे लागेल."

#: src/utils/pactl.c:2889
#, fuzzy
msgid "You have to specify a module index or name"
msgstr "तुम्हाला विभाग इंडेक्स् निश्चित करावे लागेल"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"तुम्ही एकापेक्षा जास्त सींक निश्चित करू शकत नाही. तुम्हाला बूलीयन मूल्य निश्चित करावे लागेल."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
#, fuzzy
msgid "Invalid suspend specification."
msgstr "अवैध सॅम्पल संयोजना"

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"तुम्ही एकापेक्षा जास्त स्रोत निश्चित करू शकत नाही. तुम्हाला बूलीयन मूल्य निश्चित करावे लागेल."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "तुम्हाला कार्डचे नाव/इंडेक्स् व प्रोफाइल नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "तुम्हाला सींक नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:2961
#, fuzzy
msgid "You have to specify a sink name"
msgstr "चालवण्याकरीता तुम्हाला तात्पूर्ते नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "तुम्हाला स्रोत नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:2985
#, fuzzy
msgid "You have to specify a source name"
msgstr "तुम्हाला विभाग इंडेक्स् निश्चित करावे लागेल"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
#, fuzzy
msgid "You have to specify a sink name/index"
msgstr "चालवण्याकरीता तुम्हाला तात्पूर्ते नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "तुम्हाला सींक नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
#, fuzzy
msgid "You have to specify a source name/index"
msgstr "तुम्हाला विभाग इंडेक्स् निश्चित करावे लागेल"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "तुम्हाला स्रोत नाव/इंडेक्स् व खंडाचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "तुम्हाला सींक इंपुट इंडेक्स् व सींक निश्चित करावे लागेल"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "अवैध सींक इंपुट इंडेक्स्"

#: src/utils/pactl.c:3060
#, fuzzy
msgid "You have to specify a source output index and a volume"
msgstr "तुम्हाला आऊटपुट इंडेक्स स्रोत व स्रोत निश्चित करावे लागेल"

#: src/utils/pactl.c:3065
#, fuzzy
msgid "Invalid source output index"
msgstr "अवैध सींक इंपुट इंडेक्स्"

#: src/utils/pactl.c:3086
#, fuzzy
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "तुम्हाला सींक नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
#, fuzzy
msgid "Invalid mute specification"
msgstr "अवैध सॅम्पल संयोजना"

#: src/utils/pactl.c:3111
#, fuzzy
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "तुम्हाला स्रोत नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3126
#, fuzzy
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr "तुम्हाला सींक इंपुट निर्देशांक व सींक निश्चित करावे लागेल"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "अवैध सींक इंपुट इंडेक्स् संयोजना"

#: src/utils/pactl.c:3144
#, fuzzy
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "तुम्हाला स्रोत नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3149
#, fuzzy
msgid "Invalid source output index specification"
msgstr "अवैध सींक इंपुट इंडेक्स् संयोजना"

#: src/utils/pactl.c:3162
#, fuzzy
msgid "You have to specify at least an object path and a message name"
msgstr "तुम्हाला सींक नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""

#: src/utils/pactl.c:3182
#, fuzzy
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr "तुम्हाला सींक नाव/इंडेक्स् व पोर्टचे नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3194
#, fuzzy
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "तुम्हाला कार्डचे नाव/इंडेक्स् व प्रोफाइल नाव निश्चित करावे लागेल"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr ""

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "वैध आदेश निश्चित केले नाही."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "पुन्हा चालू करण्यास अपयशी: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "सस्पेंड करण्यास अपयशी: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "सावधानता: आवाज सर्वर स्थानीय नाही, सस्पेंड करत नाही.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "जुळवणी अपयशी: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT प्राप्त झाले, बाहेर पडत आहे.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "सावधानता: उप कार्य संकेत %u द्वारे नष्ट करण्यात आले\n"

#: src/utils/pasuspender.c:228
#, fuzzy, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"libpulse %s शी कंपाई केले\n"
"libpulse %s शी लिंक केले\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() अपयशी.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() अपयशी.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() अपयशी.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "आदेश ओळ वाचण्यास अपयशी.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "सर्वर: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "स्रोत: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "सींक: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "कुकीज: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "कुकीज माहिती वाचण्यास अपयशी\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "कुकी डेटा साठवण्यास अपयशी\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "FQDN प्राप्त करण्यास अपयशी.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "कुकी डेटा दाखल करण्यास अपयशी\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "अजूनही लागू केले नाही.\n"

#~ msgid "Got signal %s."
#~ msgstr "संकेत %s प्राप्त झाले."

#~ msgid "Exiting."
#~ msgstr "बाहेर पडत आहे."

#~ msgid "Found user '%s' (UID %lu) and group '%s' (GID %lu)."
#~ msgstr "वापरकर्ता '%s' (UID %lu) व गट '%s' (GID %lu) आढळले."

#~ msgid "Successfully dropped root privileges."
#~ msgstr "रूट परवानगी यशस्वीरित्या वगळले."

#~ msgid "setrlimit(%s, (%u, %u)) failed: %s"
#~ msgstr "setrlimit(%s, (%u, %u)) अपयशी: %s"

#~ msgid "Daemon not running"
#~ msgstr "डिमन कार्यरत नाही"

#~ msgid "Daemon running as PID %u"
#~ msgstr "डिमन PID %u नुरूप कार्यरत आहे"

#~ msgid "Daemon startup successful."
#~ msgstr "डिमन स्टार्टअप यशस्वी."

#~ msgid "This is PulseAudio %s"
#~ msgstr "हे PulseAudio %s आहे"

#~ msgid "Compilation host: %s"
#~ msgstr "कंपाइलेशन यजमान: %s"

#~ msgid "Compilation CFLAGS: %s"
#~ msgstr "कंपाइलेशन CFLAGS: %s"

#~ msgid "Running on host: %s"
#~ msgstr "यजमान वर कार्यरत: %s"

#~ msgid "Found %u CPUs."
#~ msgstr "%u CPUs आढळले."

#~ msgid "Page size is %lu bytes"
#~ msgstr "पान आकार %lu बाईटस् आहे"

#~ msgid "Compiled with Valgrind support: yes"
#~ msgstr "Valgrind समर्थनशी कंपाईल केले: होय"

#~ msgid "Compiled with Valgrind support: no"
#~ msgstr "Valgrind समर्थनशी कंपाईल केले: नाही"

#~ msgid "Running in valgrind mode: %s"
#~ msgstr "valgrind पद्धतीत कार्यरत: %s"

#, fuzzy
#~ msgid "Running in VM: %s"
#~ msgstr "यजमान वर कार्यरत: %s"

#~ msgid "Optimized build: yes"
#~ msgstr "अनुकूल बिल्ड: होय"

#~ msgid "Optimized build: no"
#~ msgstr "अनुकूल बिल्ड: नाही"

#~ msgid "NDEBUG defined, all asserts disabled."
#~ msgstr "NDEBUG वर्णीकृत, सर्व asserts अकार्यान्वीत."

#~ msgid "FASTPATH defined, only fast path asserts disabled."
#~ msgstr "FASTPATH वर्णीकृत, फक्त जलद मार्गीय asserts अकार्यान्वीत केले."

#~ msgid "All asserts enabled."
#~ msgstr "सर्व asserts कार्यान्वीत केले."

#~ msgid "Machine ID is %s."
#~ msgstr "मशीन ID %s आहे."

#~ msgid "Session ID is %s."
#~ msgstr "सत्र ID %s आहे."

#~ msgid "Using runtime directory %s."
#~ msgstr "रनटाईम डिरेक्ट्री %s वापरत आहे."

#~ msgid "Using state directory %s."
#~ msgstr "स्थिती डिरेक्ट्री %s वापरत आहे."

#~ msgid "Using modules directory %s."
#~ msgstr "घटक डिरेक्ट्री %s वापरत आहे."

#~ msgid "Running in system mode: %s"
#~ msgstr "प्रणाली पद्धतीत कार्यरत: %s"

#~ msgid "Fresh high-resolution timers available! Bon appetit!"
#~ msgstr "नवीन उच्च-बिंदूता टाइमर उपलब्ध! Bon appetit!"

#~ msgid ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"
#~ msgstr ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"

#~ msgid "Failed to initialize daemon."
#~ msgstr "डिमन प्रारंभ करण्यास अपयशी."

#~ msgid "Daemon startup complete."
#~ msgstr "डिमन स्टार्टअप पूर्ण झाले."

#~ msgid "Daemon shutdown initiated."
#~ msgstr "डिमन पूर्णपणे बंद करण्यास प्रारंभ केले."

#~ msgid "Daemon terminated."
#~ msgstr "डिमन नष्ट केले."

#~ msgid "Cleaning up privileges."
#~ msgstr "परवानगी वगळत आहे."

#, fuzzy
#~ msgid "PulseAudio Sound System KDE Routing Policy"
#~ msgstr "PulseAudio आवाज प्रणाली"

#, fuzzy
#~ msgid "Start the PulseAudio Sound System with KDE Routing Policy"
#~ msgstr "PulseAudio आवाज प्रणाली सुरू करा"

#~ msgid "No cookie loaded. Attempting to connect without."
#~ msgstr "कुकी दाखल केले नाही. जुळवणीचा प्रयत्न करत आहे."

#~ msgid "Failed to load client configuration file.\n"
#~ msgstr "क्लाऐंट संयोजना फाइल दाखल करण्यास अपयशी.\n"

#~ msgid "Failed to read environment configuration data.\n"
#~ msgstr "वातावरण संयोजना डेटा वाचण्यास अपयशी.\n"

#~ msgid "Telephony Duplex (HSP/HFP)"
#~ msgstr "टेलिफोनी ड्युप्लेक्स् (HSP/HFP)"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "ऍनलॉग आऊटपुट (LFE)"

#, fuzzy
#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "डिजीटल स्टिरीओ (HDMI)"

#, fuzzy
#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "डिजीटल स्टिरीओ (IEC958)"

#~ msgid "[%s:%u] rlimit not supported on this platform."
#~ msgstr "[%s:%u] rlimit या प्लॅटफॉर्म वर समर्थीत नाही."

#~ msgid "XOpenDisplay() failed"
#~ msgstr "XOpenDisplay() अपयशी"

#~ msgid ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"
#~ msgstr ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"

#, fuzzy
#~ msgid ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "%s [options] subscribe\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ msgstr ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"

#~ msgid "%s+%s"
#~ msgstr "%s+%s"

#~ msgid "%s / %s"
#~ msgstr "%s / %s"

#~ msgid "Digital Surround 4.0 (IEC958)"
#~ msgstr "डिजीटल सर्राउंड 4.0 (IEC958)"

#~ msgid "Low Frequency Emmiter"
#~ msgstr "कमी क्रिक्वेन्सी स्रोत"
