#!/bin/sh -e
[ -z "$DEBUG" ] || set -x
echo "########## pulseaudio-17.0: staging install ##########"
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
PATH="/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin"  DESTDIR=/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/aarch64-buildroot-linux-gnu/sysroot PYTHONNOUSERSITE=y /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/ninja  -C /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build install
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -f -rf  /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/aarch64-buildroot-linux-gnu/sysroot/share/info/dir
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
if test -n "" ; then echo "[7m>>> pulseaudio 17.0 Fixing package configuration files[27m" ; /usr/bin/sed -i -e  "s,/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host,@HOST_DIR@,g" -e "s,/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562,@BASE_DIR@,g" -e "s,^\(exec_\)\?prefix=.*,\1prefix=@STAGING_DIR@/usr,g" -e "s,-I/usr/,-I@STAGING_DIR@/usr/,g" -e "s,-L/usr/,-L@STAGING_DIR@/usr/,g" -e 's,@STAGING_DIR@,$(dirname $(readlink -e $0))/../..,g' -e 's,@HOST_DIR@,$(dirname $(readlink -e $0))/../../../..,g' -e "s,@BASE_DIR@,/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562,g"  ; fi
echo "[7m>>> pulseaudio 17.0 Fixing libtool files[27m"
for la in $(find /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/aarch64-buildroot-linux-gnu/sysroot/usr/lib* -name "*.la"); do cp -a "${la}" "${la}.fixed" && /usr/bin/sed -i -e "s:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562:@BASE_DIR@:g" -e "s:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/aarch64-buildroot-linux-gnu/sysroot:@STAGING_DIR@:g"  -e "s:\(['= ]\)/usr:\\1@STAGING_DIR@/usr:g" -e "s:\(['= ]\)/lib:\\1@STAGING_DIR@/lib:g"  -e "s:@STAGING_DIR@:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/aarch64-buildroot-linux-gnu/sysroot:g" -e "s:@BASE_DIR@:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562:g" "${la}.fixed" && if cmp -s "${la}" "${la}.fixed"; then rm -f "${la}.fixed"; else mv "${la}.fixed" "${la}"; fi || exit 1; done
