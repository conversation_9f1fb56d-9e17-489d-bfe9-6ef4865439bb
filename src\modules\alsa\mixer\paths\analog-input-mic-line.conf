# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; For devices where a 'Mic/Line' element exists
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 85
description-key = analog-input

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Mic]
switch = off
volume = off

[Element Internal Mic]
switch = off
volume = off

[Element Line]
switch = off
volume = off

[Element Aux]
switch = off
volume = off

[Element Video]
switch = off
volume = off

[Element Mic/Line]
required = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element TV Tuner]
switch = off
volume = off

[Element FM]
switch = off
volume = off

.include analog-input.conf.common
.include analog-input-mic.conf.common
