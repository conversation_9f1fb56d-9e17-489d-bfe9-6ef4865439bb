#!@PA_BINARY@ -nF
#
# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify it
# under the terms of the GNU Lesser General Public License as published by
# the Free Software Foundation; either version 2 of the License, or
# (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

# This startup script is used only if PulseAudio is started per-user
# (i.e. not in system mode)
changequote(`[', `]')dnl Set up m4 quoting

.fail

### Automatically restore the volume of streams and devices
load-module module-device-restore
load-module module-stream-restore
load-module module-card-restore

### Automatically augment property information from .desktop files
### stored in /usr/share/application
load-module module-augment-properties

### Should be after module-*-restore but before module-*-detect
load-module module-switch-on-port-available

### Load audio drivers statically
### (it's probably better to not load these drivers manually, but instead
### use module-udev-detect -- see below -- for doing this automatically)
ifelse(@HAVE_ALSA@, 1, [dnl
#load-module module-alsa-sink
#load-module module-alsa-source device=hw:1,0
])dnl
ifelse(@HAVE_OSS_OUTPUT@, 1, [dnl
#load-module module-oss device="/dev/dsp" sink_name=output source_name=input
#load-module module-oss-mmap device="/dev/dsp" sink_name=output source_name=input
])dnl
ifelse(@HAVE_WAVEOUT@, 1, [dnl
load-module module-waveout sink_name=output source_name=input
])dnl
#load-module module-null-sink
ifelse(@HAVE_MKFIFO@, 1, [dnl
#load-module module-pipe-sink
])dnl

### Automatically load driver modules depending on the hardware available
ifelse(@HAVE_UDEV@, 1, [dnl
.ifexists module-udev-detect@PA_SOEXT@
load-module module-udev-detect
.else
], @HAVE_COREAUDIO@, 1, [dnl
.ifexists module-coreaudio-detect@PA_SOEXT@
load-module module-coreaudio-detect
.else
], [dnl
.ifexists module-detect@PA_SOEXT@
])dnl
### Use the static hardware detection module (for systems that lack udev support)
load-module module-detect
.endif
ifelse(@OS_IS_FREEBSD@, 1, [dnl
### FreeBSD devd is used in addition to static detection (only handles hotplug)
.ifexists module-devd-detect@PA_SOEXT@
load-module module-devd-detect
.endif
])dnl

### Automatically connect sink and source if JACK server is present
.ifexists module-jackdbus-detect@PA_SOEXT@
.nofail
load-module module-jackdbus-detect channels=2
.fail
.endif

ifelse(@HAVE_BLUEZ@, 1, [dnl
### Automatically load driver modules for Bluetooth hardware
.ifexists module-bluetooth-policy@PA_SOEXT@
load-module module-bluetooth-policy
.endif

.ifexists module-bluetooth-discover@PA_SOEXT@
load-module module-bluetooth-discover
.endif
])dnl

ifelse(@HAVE_AF_UNIX@, 1, [dnl
### Load several protocols
.ifexists module-esound-protocol-unix@PA_SOEXT@
load-module module-esound-protocol-unix
.endif
load-module module-native-protocol-unix
])dnl

### Network access (may be configured with paprefs, so leave this commented
### here if you plan to use paprefs)
#load-module module-esound-protocol-tcp
#load-module module-native-protocol-tcp
ifelse(@HAVE_AVAHI@, 1, [dnl
#load-module module-zeroconf-publish
])dnl

ifelse(@OS_IS_WIN32@, 0, [dnl
### Load the RTP receiver module (also configured via paprefs, see above)
#load-module module-rtp-recv

### Load the RTP sender module (also configured via paprefs, see above)
#load-module module-null-sink sink_name=rtp format=s16be channels=2 rate=44100 sink_properties="device.description='RTP Multicast Sink'"
#load-module module-rtp-send source=rtp.monitor

ifelse(@HAVE_GSETTINGS@, 1, [dnl
### Load additional modules from GSettings. This can be configured with the paprefs tool.
### Please keep in mind that the modules configured by paprefs might conflict with manually
### loaded modules.
.ifexists module-gsettings@PA_SOEXT@
.nofail
load-module module-gsettings
.fail
.endif
])dnl

### Automatically restore the default sink/source when changed by the user
### during runtime
### NOTE: This should be loaded as early as possible so that subsequent modules
### that look up the default sink/source get the right value
load-module module-default-device-restore

### Make sure we always have a sink around, even if it is a null sink.
load-module module-always-sink

### Honour intended role device property
load-module module-intended-roles

### Automatically suspend sinks/sources that become idle for too long
load-module module-suspend-on-idle

### If autoexit on idle is enabled we want to make sure we only quit
### when no local session needs us anymore.
.ifexists module-console-kit@PA_SOEXT@
load-module module-console-kit
.endif
.ifexists module-systemd-login@PA_SOEXT@
load-module module-systemd-login
.endif

### Enable positioned event sounds
load-module module-position-event-sounds

### Cork music/video streams when a phone stream is active
load-module module-role-cork

### Modules to allow autoloading of filters (such as echo cancellation)
### on demand. module-filter-heuristics tries to determine what filters
### make sense, and module-filter-apply does the heavy-lifting of
### loading modules and rerouting streams.
load-module module-filter-heuristics
load-module module-filter-apply
])dnl

### Make some devices default
#set-default-sink output
#set-default-source input

### Allow including a default.pa.d directory, which if present, can be used
### for additional configuration snippets.
### Note that those snippet files must have a .pa file extension, not .conf
.nofail
.include @PA_DEFAULT_CONFIG_DIR_UNQUOTED@/default.pa.d
