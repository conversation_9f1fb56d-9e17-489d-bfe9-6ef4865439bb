#ifndef foocpuorchfoo
#define foocpuorchfoo

/***
  This file is part of PulseAudio.

  Copyright 2010 <PERSON><PERSON> <<EMAIL>>

  PulseAudio is free software; you can redistribute it and/or modify
  it under the terms of the GNU Lesser General Public License as published
  by the Free Software Foundation; either version 2.1 of the License,
  or (at your option) any later version.

  PulseAudio is distributed in the hope that it will be useful, but
  WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
  General Public License for more details.

  You should have received a copy of the GNU Lesser General Public License
  along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.
***/

#include <pulsecore/cpu.h>

/* Orc-optimised bits */

bool pa_cpu_init_orc(pa_cpu_info cpu_info);

void pa_volume_func_init_orc(void);

#endif /* foocpuorchfoo */
