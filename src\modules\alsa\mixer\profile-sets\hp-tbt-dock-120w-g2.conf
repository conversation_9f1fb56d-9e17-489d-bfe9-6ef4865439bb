# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; HP Thunderbolt Dock 120W G2
;
; This dock has a 3.5mm headset connector. Both input and output are stereo.
;
; There's a separate speakerphone module called "HP Thunderbolt Dock Audio
; Module", which can be attached to this dock. The module will appear in ALSA
; as a separate USB sound card, configuration for it is in
; hp-tbt-dock-audio-module.conf.

[General]
auto-profiles = no

[Mapping analog-stereo-headset]
device-strings = hw:%f,0,0
channel-map = left,right

[Profile output:analog-stereo-headset+input:analog-stereo-headset]
output-mappings = analog-stereo-headset
input-mappings = analog-stereo-headset
skip-probe = yes
