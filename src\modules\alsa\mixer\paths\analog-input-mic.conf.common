# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Common element for all microphone inputs
;
; See analog-output.conf.common for an explanation on the directives

[Properties]
device.icon_name = audio-input-microphone

[Element Line]
switch = off
volume = off

[Element Line Boost]
switch = off
volume = off

[Element Aux]
switch = off
volume = off

[Element Video]
switch = off
volume = off

[Element Mic/Line]
switch = off
volume = off

[Element TV Tuner]
switch = off
volume = off

[Element FM]
switch = off
volume = off

[Element Inverted Internal Mic]
switch = off
volume = off

[Element Mic Jack Mode]
enumeration = select

[Option Mic Jack Mode:Mic In]
priority = 19
name = input-microphone
