# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; For devices where a 'Headset Mic' or 'Headset Mic Boost' element exists
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 88
description-key = analog-input-microphone-headset

[Jack Headset Mic]
required-any = any

[Jack Headset Mic Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Jack Headphone]
state.plugged = unknown

[Jack Front Headphone]
state.plugged = unknown

[Jack Headphone Mic]
state.plugged = unknown

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Headset Mic Boost]
required-any = any
switch = select
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Headset Mic]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Headset]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Input Source]
enumeration = select

[Option Input Source:Headset Mic]
name = Headset Microphone
required-any = any

[Element Capture Source]
enumeration = select

[Option Capture Source:Headset Mic]
name = Headset Microphone
required-any = any

[Element Front Mic]
switch = off
volume = off

[Element Internal Mic]
switch = off
volume = off

[Element Rear Mic]
switch = off
volume = off

[Element Dock Mic]
switch = off
volume = off

[Element Dock Mic Boost]
switch = off
volume = off

[Element Internal Mic Boost]
switch = off
volume = off

[Element Front Mic Boost]
switch = off
volume = off

[Element Rear Mic Boost]
switch = off
volume = off

.include analog-input-mic.conf.common
