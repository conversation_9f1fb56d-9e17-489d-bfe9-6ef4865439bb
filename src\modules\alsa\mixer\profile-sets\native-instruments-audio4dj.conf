# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Native Instruments Audio 4 DJ
;
; This card has two stereo pairs of input and two stereo pairs of
; output, named channels A and B. Channel B has an additional
; Headphone connector.
;
; We knowingly only define a subset of the theoretically possible
; mapping combinations as profiles here.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-a]
description = Analog Stereo Channel A
device-strings = hw:%f,0,0
channel-map = left,right

[Mapping analog-stereo-b-output]
description = Analog Stereo Channel B (Headphones)
device-strings = hw:%f,0,1
channel-map = left,right
direction = output

[Mapping analog-stereo-b-input]
description = Analog Stereo Channel B
device-strings = hw:%f,0,1
channel-map = left,right
direction = input

[Profile output:analog-stereo-all+input:analog-stereo-all]
description = Analog Stereo Duplex Channels A, B (Headphones)
output-mappings = analog-stereo-a analog-stereo-b-output
input-mappings = analog-stereo-a analog-stereo-b-input
priority = 100
skip-probe = yes

[Profile output:analog-stereo-a+input:analog-stereo-a]
description = Analog Stereo Duplex Channel A
output-mappings = analog-stereo-a
input-mappings = analog-stereo-a
priority = 40
skip-probe = yes

[Profile output:analog-stereo-b+input:analog-stereo-b]
description = Analog Stereo Duplex Channel B (Headphones)
output-mappings = analog-stereo-b-output
input-mappings = analog-stereo-b-input
priority = 50
skip-probe = yes

[Profile output:analog-stereo-a]
description = Analog Stereo Output Channel A
output-mappings = analog-stereo-a
priority = 5
skip-probe = yes

[Profile output:analog-stereo-b]
description = Analog Stereo Output Channel B (Headphones)
output-mappings = analog-stereo-b-output
priority = 6
skip-probe = yes

[Profile input:analog-stereo-a]
description = Analog Stereo Input Channel A
input-mappings = analog-stereo-a
priority = 2
skip-probe = yes

[Profile input:analog-stereo-b]
description = Analog Stereo Input Channel B
input-mappings = analog-stereo-b-input
priority = 1
skip-probe = yes
