Build System:
- Use own name mangling scheme instead of ltdl's, which will eliminate the
  need for .la files or extra trickery.

Porting:
- rtp module ported to Win32 (sendmsg/recvmsg emulation)

I18N:
- iconv stuff sent from utils to server (UTF-8)
- iconv sample loading in server
- gettextify pulseaudio

Cleanups:
- drop dependency of libpulse on libX11, instead use an external mini binary

Network:
- module-tunnel: improve latency calculation
- module-tunnel: more reliable audio streaming over wifi
- Compressed network streams for tunnels/rtp streams. (Might be a good GSoC project)
  This builds on passthrough support. A good candidate codec would be CELT.

Test:
- autoload

Auth/Crypto:
- ssl
- key rings for auth
- challenge response auth
- sasl auth 

Features:
- use scatter/gather io for sockets
- examine if it is possible to mimic esd's handling of half duplex cards
  (switch to capture when a recording client connects and drop playback during
  that time)
- add an API to libpulse for allocating memory from the pa_context memory pool
- configuration file syntax:
  - multiline configuration statements
  - recursive .if

Long term:
- pass meta info for hearing impaired
- X11: support for the X11 synchronization extension

Backends for:
- portaudio  (semi-done)
