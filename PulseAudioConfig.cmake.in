set(PULSEAUDIO_FOUND TRUE)

set(PULS<PERSON>UDIO_VERSION_MAJOR @PA_MAJOR@)
set(PULSEAUDIO_VERSION_MINOR @PA_MINOR@)
set(PULSEAUDIO_VERSION @PA_MAJOR@.@PA_MINOR@)
set(PU<PERSON><PERSON>UDIO_VERSION_STRING "@PA_MAJOR@.@PA_MINOR@")

find_path(PULSEAUDIO_INCLUDE_DIR pulse/pulseaudio.h HINTS "@PA_INCDIR@")
find_library(PULSEAUDIO_LIBRARY NAMES pulse libpulse HINTS "@PA_LIBDIR@")
ifelse(@HAVE_GLIB20@, 1, dnl
find_library(PULSEAUDIO_MAINLOOP_LIBRARY NAMES pulse-mainloop-glib libpulse-mainloop-glib HINTS "@PA_LIBDIR@")
)dnl
