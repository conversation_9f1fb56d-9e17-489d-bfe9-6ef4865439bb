# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; For devices where a 'Mic' or 'Mic Boost' element exists
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 87
description-key = analog-input-microphone

[Jack <PERSON>]
required-any = any

[Jack Mic Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

[Jack Mic - Input]
required-any = any

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Mic <PERSON>]
required-any = any
switch = select
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Option Mic Boost:on]
name = input-boost-on

[Option Mic Boost:off]
name = input-boost-off

[Element Mic]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Input Source]
enumeration = select

[Option Input Source:Mic]
name = analog-input-microphone
required-any = any

[Element Capture Source]
enumeration = select

[Option Capture Source:Mic]
name = analog-input-microphone
required-any = any

[Element PCM Capture Source]
enumeration = select

[Option PCM Capture Source:Mic]
name = analog-input-microphone
required-any = any

[Option PCM Capture Source:Mic-In/Mic Array]
name = analog-input-microphone
required-any = any

;;; Some AC'97s have "Mic Select" and "Mic Boost (+20dB)"

[Element Mic Select]
enumeration = select

[Option Mic Select:Mic1]
name = input-microphone
priority = 20

[Option Mic Select:Mic2]
name = input-microphone
priority = 19

[Element Mic Boost (+20dB)]
switch = select
volume = merge

[Option Mic Boost (+20dB):on]
name = input-boost-on

[Option Mic Boost (+20dB):off]
name = input-boost-off

[Element Front Mic]
switch = off
volume = off

[Element Internal Mic]
switch = off
volume = off

[Element Rear Mic]
switch = off
volume = off

[Element Dock Mic]
switch = off
volume = off

[Element Dock Mic Boost]
switch = off
volume = off

[Element Internal Mic Boost]
switch = off
volume = off

[Element Front Mic Boost]
switch = off
volume = off

[Element Rear Mic Boost]
switch = off
volume = off

.include analog-input-mic.conf.common
