# Swedish translation for pulseaudio.
# Copyright © 2008-2017 Free Software Foundation, Inc.
# This file is distributed under the same license as the pulseaudio package.
# <PERSON> <<EMAIL>>, 2008, 2012.
# <PERSON> <josef.and<PERSON><PERSON>@fripost.org>, 2014, 2017.
#
#
# Termer:
# input/output: ingång/utgång (det handlar om ljud)
# latency: latens
# delay: fördröjning
# boost: öka
# gain: förstärkning
# channel map: kanalmappning
# passthrough: genomströmning
# och en hel del termer som inte översätts inom ljuddomänen, ex. surround.
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2022-05-18 22:27+0000\n"
"Last-Translator: <PERSON> <and<PERSON>.j<PERSON><PERSON>@norsjovallen.se>\n"
"Language-Team: Swedish <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/sv/>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.12.2\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [flaggor]\n"
"\n"
"KOMMANDON:\n"
"  -h, --help                            Visa denna hjälp\n"
"      --version                         Visa version\n"
"      --dump-conf                       Dumpa standardkonfiguration\n"
"      --dump-modules                    Dumpa lista över tillgängliga "
"moduler\n"
"      --dump-resample-methods           Dumpa tillgängliga "
"omsamplingsmetoder\n"
"      --cleanup-shm                     Städa upp utgångna delade "
"minnessegment\n"
"      --start                           Starta demonen om den inte redan "
"körs\n"
"  -k  --kill                            Döda en körande demon\n"
"      --check                           Kontrollera om det finns någon "
"körande demon (returnerar bara returkod)\n"
"\n"
"FLAGGOR:\n"
"      --system[=BOOL]                   Kör som en systemomfattande instans\n"
"  -D, --daemonize[=BOOL]                Demonisera efter start\n"
"      --fail[=BOOL]                     Avsluta om start misslyckas\n"
"      --high-priority[=BOOL]            Försök att sätta en hög nice-nivå\n"
"                                        (bara tillgängligt som root, när "
"SUID eller\n"
"                                        med förhöjd RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Försök att aktivera "
"realtidsschemaläggning\n"
"                                        (bara tillgängligt som root, när "
"SUID eller\n"
"                                        med förhöjd RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Förbjud av användaren begärd "
"laddning/urladdning\n"
"                                        av moduler efter start\n"
"      --disallow-exit[=BOOL]            Förbjud användarbegärt avslut\n"
"      --exit-idle-time=SEK              Avsluta demonen vid inaktivitet och "
"efter\n"
"                                        denna tid\n"
"      --scache-idle-time=SEK            Inaktivera autoinlästa samplingar "
"vid inaktivitet och\n"
"                                        efter denna tid\n"
"      --log-level[=NIVÅ]                Öka eller bestäm informativa nivån\n"
"  -v  --verbose                         Öka informativa nivån\n"
"      --log-target={auto,syslog,stderr,file:SÖKVÄG,newfile:SÖKVÄG}\n"
"                                        Ange mål för loggen\n"
"      --log-meta[=BOOL]                 Inkludera kodplats i "
"loggmeddelanden\n"
"      --log-time[=BOOL]                 Inkludera tidsstämpel i "
"loggmeddelanden\n"
"      --log-backtrace=RAMAR             Inkluderar bakåtspårning i "
"loggmeddelanden\n"
"  -p, --dl-search-path=SÖKVÄG           Ange sökvägen för dynamiskt delade\n"
"                                        objekt (insticksmoduler)\n"
"      --resample-method=METOD           Använd den angivna "
"omsamplingsmetoden\n"
"                                        (Se --dump-resample-methods för\n"
"                                        möjliga värden)\n"
"      --use-pid-file[=BOOL]             Skapa en PID-fil\n"
"      --no-cpu-limit[=BOOL]             Installera inte en CPU-"
"belastningsbegränsare\n"
"                                        på plattformar som stödjer det.\n"
"      --disable-shm[=BOOL]              Inaktivera stöd för delat minne.\n"
"      --enable-memfd[=BOOL]             Aktivera stöd för memfd-delat "
"minne.\n"
"\n"
"STARTSKRIPT:\n"
"  -L, --load=”MODUL ARGUMENT”           Läs in den angivna insticksmodulen "
"med\n"
"                                        det specificerade argumentet\n"
"  -F, --file=FILNAMN                    Kör det angivna skriptet\n"
"  -C                                    Öppna en kommandorad på körande TTY\n"
"                                        efter start\n"
"\n"
"  -n                                    Läs inte in standardskriptfil\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:265
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level förväntar sig loggnivåargument (antingen numeriska i intervallet "
"0..4 eller en av error, warn, notice, info, debug)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Ogiltigt mål för loggen: använd antingen ”syslog”, ”journal”, ”stderr” eller "
"”auto” eller ett giltigt filnamn ”file:<sökväg>”, ”newfile:<sökväg>”."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Ogiltigt mål för loggen: använd ”syslog”, ”journal”, ”stderr”, ”auto” eller "
"ett giltigt filnamn ”file:<sökväg>”, ”newfile:<sökväg>”."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Ogiltig omsamplingsmetod ”%s”."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm förväntar sig ett booleskt argument"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd förväntar sig ett booleskt argument"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Ogiltigt mål för loggen ”%s”."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Ogiltig loggnivå ”%s”."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Ogiltig omsamplingsmetod ”%s”."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] Ogiltig rlimit ”%s”."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Ogiltigt samplingsformat ”%s”."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Ogiltig samplingsfrekvens ”%s”."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Ogiltiga samplingskanaler ”%s”."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Ogiltig kanalmappning ”%s”."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Ogiltigt antal fragment ”%s”."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Ogiltig fragmentstorlek ”%s”."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Ogiltig nice-nivå ”%s”."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Ogiltig servertyp ”%s”."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Misslyckades med att öppna konfigurationsfil: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"Den angivna standardkanalmappningen har ett annat antal kanaler än den "
"angivna standardkanalmappningens antal."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Läs från konfigurationsfilen: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Namn: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Ingen modulinformation tillgänglig\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Version: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Beskrivning: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Upphovsman: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Användning: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Läs in en gång: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "FÖRÅLDRADVARNING: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Sökväg: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "Misslyckades med att öppna modulen %s: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Misslyckades med att hitta original-lt_dlopen loader."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Misslyckades med att allokera en ny dl loader."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Misslyckades med att lägga till bind-now-loader."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Misslyckades med att hitta användaren ”%s”."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Misslyckades med att hitta gruppen ”%s”."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "GID för användare ”%s” och för grupp ”%s” stämmer inte överens."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "Hemkatalogen för användaren ”%s” är inte ”%s”, ignorerar."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Misslyckades med att skapa ”%s”: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Misslyckades med att ändra grupplista: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Misslyckades med att ändra GID: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Misslyckades med att ändra UID: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "Systemomfattande läge stöds inte på denna plattform."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Misslyckades med att tolka kommandoraden."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Systemläge vägrades för icke-rootanvändare. Startar bara D-Bus-"
"serveruppslagningstjänsten."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Misslyckades med att döda demonen: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Detta program är inte tänkt att köras som root (såvida inte --system har "
"angivits)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Root-behörighet krävs."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start stöds inte för systeminstanser."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr "Användaranpassad server på %s, vägrar starta/autostarta."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Användaranpassad server på %s, som ser ut att vara lokal. Undersöker djupare."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "Kör i systemläge, men --disallow-exit är inte angett."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr "Kör i systemläge, men --disallow-module-loading är inte angett."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "Kör i systemläge, tvingar fram inaktivering av SHM-läge."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr "Kör i systemläge, tvingar fram avslut vid inaktivitet."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Misslyckades med att få stdio."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() misslyckades: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() misslyckades: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() misslyckades: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Demonstart misslyckades."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() misslyckades: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Misslyckades med att hämta maskin-ID"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"Ok, du kör PA i systemläge. Försäkra dig om att du verkligen vill göra "
"detta.\n"
"Läs http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/User/"
"WhatIsWrongWithSystemWide/ för en förklaring till varför systemläge "
"vanligtvis är en dålig idé."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() misslyckades."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() misslyckades."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "kommandoradsargument"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"Misslyckades att initiera demonen på grund av fel vid körning av "
"uppstartskommandon. Källa till kommandon: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "Demonen startade utan inlästa moduler, fungerar inte."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "PulseAudio ljudsystem"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Starta ljudsystemet PulseAudio"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Ingång"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Ingång för dockningsstation"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Mikrofon för dockningsstation"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "Linje in för dockningsstation"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "Linje in"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Frontmikrofon"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Bakre mikrofon"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Extern mikrofon"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Intern mikrofon"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Radio"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Video"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Automatisk förstärkningskontroll"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Ingen automatisk förstärkningskontroll"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Ökning"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Ingen ökning"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Förstärkare"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Ingen förstärkare"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Basökning"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Ingen basökning"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Högtalare"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Hörlurar"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Analog ingång"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Dockmikrofon"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Headset-mikrofon"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Analog utgång"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "Hörlurar 2"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "Monoutgång för hörlurar"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "Linje ut"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Analog monoutgång"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Högtalare"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Digital utgång (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Digital ingång (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "Multikanalingång"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "Multikanalutgång"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "Spelutgång"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "Chatt-utgång"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "Chatt-ingång"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "Virtual surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Analog mono"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "Analog mono (vänster)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "Analog mono (höger)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Analog stereo"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Mono"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Stereo"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Headset"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "Högtalartelefon"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Multikanal"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Analog surround 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Analog surround 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Analog surround 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Analog surround 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Analog surround 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Analog surround 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Analog surround 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Analog surround 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Analog surround 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Analog surround 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Analog surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Digital stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Digital surround 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Digital surround 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Digital surround 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Digital stereo (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Digital surround 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "Chatt"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "Spel"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Analog mono duplex"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Analog stereo duplex"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Digital stereo duplex (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "Multikanalduplex"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "Stereo duplex"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "Mono Chatt + 7.1 Surround"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Av"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "%s utgång"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "%s ingång"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA väckte oss för att skriva ny data till enheten, men det fanns inget att "
"skriva.\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"detta problem till ALSA-utvecklarna.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA väckte oss för att läsa ny data från enheten, men det fanns inget att "
"läsa.\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"detta problem till ALSA-utvecklarna.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() returnerade ett värde som är exceptionellt stort: %lu byte "
"(%lu ms).\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"problemet till ALSA-utvecklarna."
msgstr[1] ""
"snd_pcm_avail() returnerade ett värde som är exceptionellt stort: %lu byte "
"(%lu ms).\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"problemet till ALSA-utvecklarna."

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() returnerade ett värde som är exceptionellt stort: %li byte "
"(%s%lu ms).\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"problemet till ALSA-utvecklarna."
msgstr[1] ""
"snd_pcm_delay() returnerade ett värde som är exceptionellt stort: %li byte "
"(%s%lu ms).\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"problemet till ALSA-utvecklarna."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() returnerade konstiga värden: fördröjningen %lu är "
"mindre än tillgängliga %lu.\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"problemet till ALSA-utvecklarna."

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() returnerade ett värde som är exceptionellt stort: %lu "
"byte (%lu ms).\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"problemet till ALSA-utvecklarna."
msgstr[1] ""
"snd_pcm_mmap_begin() returnerade ett värde som är exceptionellt stort: %lu "
"byte (%lu ms).\n"
"Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
"problemet till ALSA-utvecklarna."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Bluetooth-ingång"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Bluetooth-utgång"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Handsfree"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Hörlurar"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Bärbar"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Bil"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "HiFi"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Telefon"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "High fidelity playback (A2DP Sink)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "High fidelity capture (A2DP Source)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "Headset-huvudenhet (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "Headset audio gateway (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "Handsfree-huvudenhet (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Handsfree audio gateway (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<namn på källa> source_properties=<egenskaper för källa> "
"source_master=<namn på källa att filtrera> sink_name=<namn på mottagare> "
"sink_properties=<egenskaper för mottagare> sink_master=<namn på mottagare "
"att filtrera> adjust_time=<hur ofta frekvens ska justeras i s> "
"adjust_threshold=<hur mycket avsteg ska justeras i ms> "
"format=<samplingsformat> rate=<samplingshastighet> channels=<antal kanaler> "
"channel_map=<kanalmappning> aec_method=<implementation att använda> "
"aec_args=<parametrar för AEC-motorn> save_aec=<spara AEC-data i /tmp> "
"autoloaded=<ange om denna modul läses in automatiskt> use_volume_sharing=<ja "
"eller nej> use_master_format=<ja eller nej> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "På"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Attrapputgång"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "Håll alltid minst en mottagare inläst även om den är null"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "Håll alltid minst en källa inläst även om den är null"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "Allmän equalizer"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<namn på mottagare> sink_properties=<egenskaper för mottagare> "
"sink_master=<mottagare att ansluta till> format=<samplingsformat> "
"rate=<samplingshastighet> channels=<antal kanaler> "
"channel_map=<kanalmappning> autoloaded=<om denna modul läses in automatiskt> "
"use_volume_sharing=<ja eller nej> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "FFT-baserad equalizer på %s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<automatiskt inaktivera oanvända filter?>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Virtual LADSPA-mottagare"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<namn på mottagare> sink_properties=<egenskaper för mottagare> "
"sink_input_properties=<egenskaper för mottagaringång> master=<namn på "
"mottagare att filtrera> sink_master=<namn på mottagare att filtrera> "
"format=<samplingsformat> rate=<samplingshastighet> channels=<antal kanaler> "
"channel_map=<ingångskanalmappning> plugin=<namn på ladspa-instick> "
"label=<etikett för ladspa-instick> control=<kommaseparerad lista över "
"ingångskontrollvärden> input_ladspaport_map=<kommaseparerad lista över "
"LADSPA-portnamn för ingång> output_ladspaport_map=<kommaseparerad lista över "
"LADSPA-portnamn för utgång> autoloaded=<angiven om denna modul läses in "
"automatiskt> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Klockad NULL-mottagare"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Nullutgång"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Misslyckades med att ange format: ogiltig formatsträng %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Utgångsenheter"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Ingångsenheter"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "Ljud på @HOSTNAME@"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "Tunnel för %s@%s"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "Tunnel till %s/%s"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Virtual surround-mottagare"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<namn för mottagaren> sink_properties=<egenskaper för mottagaren> "
"master=<namn på mottagare att filtrera> sink_master=<namn på mottagare att "
"filtrera> format=<samplingsformat> rate=<samplingshastighet> channels=<antal "
"kanaler> channel_map=<kanalavbildning> use_volume_sharing=<ja eller nej> "
"force_flat_volume=<ja eller nej> hrir=/sökväg/till/left_hrir.wav hrir_left=/"
"sökväg/till/left_hrir.wav hrir=/sökväg/till/right_hrir.wav "
"autoloaded=<angiven om denna modul läses in automatiskt> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "Okänd enhetsmodell"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "RAOP-standardprofil"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "PulseAudio ljudserver"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Center fram"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Vänster fram"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Höger fram"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Center bak"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Vänster bak"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Höger bak"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Subwoofer"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Vänster-om-center fram"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Höger-om-center fram"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Vänster sida"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Höger sida"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Extra 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Extra 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Extra 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Extra 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Extra 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Extra 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Extra 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Extra 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Extra 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Extra 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Extra 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Extra 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Extra 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Extra 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Extra 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Extra 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Extra 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Extra 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Extra 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Extra 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Extra 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Extra 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Extra 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Extra 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Extra 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Extra 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Extra 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Extra 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Extra 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Extra 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Extra 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Extra 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Topp mitten"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Topp fram mitten"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Topp fram vänster"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Upp fram höger"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Topp bak mitten"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Topp bak vänster"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Topp bak höger"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(ogiltig)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Surround 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Surround 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Surround 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Surround 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Surround 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() misslyckades"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() returnerade true"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Misslyckades med att tolka kakdata"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Tog emot meddelande för okända tillägget ”%s”"

#: src/pulse/direction.c:37
msgid "input"
msgstr "ingång"

#: src/pulse/direction.c:39
msgid "output"
msgstr "utgång"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "dubbelriktad"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "ogiltig"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) ägs inte av oss (uid %d), utan av uid %d! (Det kan "
"hända om du exempelvis försöker att ansluta till en icke-root PulseAudio som "
"en root-användare över det interna protokollet. Gör inte det.)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "ja"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "nej"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Kan inte komma åt låset för autospawn."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Misslyckades med att öppna målfilen ”%s”."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Försökte att öppna målfilen ”%s”, ”%s.1”, ”%s.2” … ”%s.%d” men misslyckades."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Ogiltigt mål för logg."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Inbyggt ljud"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Åtkomst nekad"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Okänt kommando"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Ogiltigt argument"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "Entiteten finns"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Ingen sådan entitet"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Anslutning nekades"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Protokollfel"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Tidsgräns nåddes"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Ingen autentiseringsnyckel"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Internt fel"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Anslutningen terminerad"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Entitet dödad"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Ogiltig server"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Misslyckades med att starta modul"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Felaktigt tillstånd"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Ingen data"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Inkompatibel protokollversion"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "För stor"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Stöds inte"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Okänd felkod"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Det finns inget sådant tillägg"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Föråldrad funktionalitet"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Implementering saknas"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Klienten förgrenad"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "In-/utgångsfel"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Enhet eller resurs upptagen"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %u kan. %u Hz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Misslyckades med att dränera strömmen: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Uppspelningsströmmen dränerad."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Dränerar anslutning till servern."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() misslyckades: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() misslyckades: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Strömmen skapad."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() misslyckades: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Buffertmått: maxlength=%u tlength=%u prebuf=%u minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Buffertmått: maxlength=%u fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "Använder samplingsspec ”%s”, kanalmappning ”%s”."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Ansluten till enheten %s (index:%u, vänteläge: %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Strömfel: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Strömenheten avstängd.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Strömenheten återaktiverad.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Strömmen underskriden.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Strömmen överskriden.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Strömmen startad.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Strömmen flyttad till enhet %s (%u, %savstängd).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "inte "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Strömbuffertattribut ändrade.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Korkningsbegäransstacken är tom: Korkar igen ström"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Korkningsbegäransstacken är tom: Korkar ur ström"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr "Varning: Mottog fler urkorkningsbegäran än korkningsbegäran."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Anslutning etablerad.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() misslyckades: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() misslyckades: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Misslyckades med att ange övervakningsströmmen: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() misslyckades: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Anslutningsfel: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "Fick filslut."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() misslyckades: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() misslyckades: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Fick signal, avslutar."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Misslyckades med att avgöra latens: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Tid: %0.3f s; Latens: %0.0f μs."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() misslyckades: %s"

#: src/utils/pacat.c:676
#, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [flaggor]\n"
"%s\n"
"\n"
"  -h, --help                            Visa denna hjälp\n"
"      --version                         Visa version\n"
"\n"
"  -r, --record                          Skapa en anslutning för inspelning\n"
"  -p, --playback                        Skapa en anslutning för uppspelning\n"
"\n"
"  -v, --verbose                         Aktivera informativa åtgärder\n"
"\n"
"  -s, --server=SERVER                   Namn på servern att ansluta till\n"
"  -d, --device=ENHET                    Namn på mottagare/källa att ansluta "
"till. De speciella namnen @DEFAULT_SINK@, @DEFAULT_SOURCE@ och "
"@DEFAULT_MONITOR@ kan användas för att ange standardmottagaren, -källan "
"respektive -övervakaren.\n"
"  -n, --client-name=NAMN                Hur denna klient ska namnges på "
"servern\n"
"      --stream-name=NAMN                Hur denna ström ska namnges på "
"servern\n"
"      --volume=VOLYM                    Ange initiala (linjära) volymen i "
"intervallet 0…65536\n"
"      --rate=SAMPLINGSFREKVENS          Samplingsfrekvens i Hz (standard "
"44100)\n"
"      --format=SAMPLINGSFORMAT            Samplingsformat, se\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        för möjliga värden (standard s16ne)\n"
"      --channels=KANALER                Antalet kanaler, 1 för mono, 2 för "
"stereo\n"
"                                        (standard 2)\n"
"      --channel-map=KANALAVBILDNING     Kanalavbildning att använda istället "
"för standard\n"
"      --fix-format                      Ta samplingsformatet från mottagaren/"
"källan strömmen\n"
"                                        ansluts till.\n"
"      --fix-rate                        Ta samplingsfrekvensen från "
"mottagaren/källan strömmen\n"
"                                        ansluts till.\n"
"      --fix-channels                    Ta antalet kanaler och "
"kanalavbildning\n"
"                                        från mottagaren/källan strömmen "
"ansluts till.\n"
"      --no-remix                        Mixa inte upp eller ner kanaler.\n"
"      --no-remap                        Avbilda kanaler med index istället "
"för med namn.\n"
"      --latency=BYTE                    Begär angiven latens i byte.\n"
"      --process-time=BYTE               Begär angiven processtid per begäran "
"i byte.\n"
"      --latency-msec=MS                 Begär angiven latens i ms.\n"
"      --process-time-msec=MS            Begär angiven processtid per begäran "
"i ms.\n"
"      --property=EGENSKAP=VÄRDE         Sätt angiven egenskap till angivet "
"värde.\n"
"      --raw                             Spela in/upp rå PCM-data.\n"
"      --passthrough                     Strömma data igenom.\n"
"      --file-format[=FFORMAT]           Spela in/upp formaterad PCM-data.\n"
"      --list-file-formats               Lista tillgängliga filformat.\n"
"      --monitor-stream=INDEX            Spela in från mottagaringången med "
"index INDEX.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr "Spela upp kodade ljudfiler på en PulseAudio-server."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr "Fånga ljuddata från en PulseAudio-ljudserver och skriv den till fil."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"Fånga ljuddata från en PulseAudio-ljudserver och skriv den till STDOUT eller "
"angiven fil."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"Spela upp ljuddata från STDIN eller den angivna filen, på en PulseAudio-"
"server."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Kompilerade med libpulse %s\n"
"Länkade med libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Ogiltigt klientnamn ”%s”"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Ogiltigt namn på strömmen ”%s”"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Ogiltig kanalmappning ”%s”"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Ogiltig latensangivelse ”%s”"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Ogiltig angivelse av processtid ”%s”"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Ogiltig egenskap ”%s”"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Okänt filformat %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "Misslyckades med att tolka argumentet för --monitor-stream"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Ogiltig samplingsspecifikation"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "För många argument."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Misslyckades med att generera samplingsspecifikation för filen."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Misslyckades med att öppna ljudfilen."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Varning: angiven samplingsspecifikation kommer att skrivas över med "
"specifikation från filen."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Misslyckades med att avgöra samplingsspecifikation från filen."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "Varning: Misslyckades med att avgöra kanalmappningen från filen."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "Kanalmappning stämmer inte överens med samplingsspecifikationen"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Varning: Misslyckades med att skriva kanalmappningen till filen."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Öppnar en %s-ström med samplingsspecifikationen ”%s” och kanalmappningen "
"”%s”."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "inspelning"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "uppspelning"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Misslyckades med att ange medienamn."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() misslyckades."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() misslyckades."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() misslyckades."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() misslyckades: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() misslyckades."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() misslyckades."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "NAMN [ARG …]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "NAMN|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "NAMN"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "NAMN|#N VOLYM"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N VOLYM"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "NAMN|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "NAMN|#N NYCKEL=VÄRDE"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N NYCKEL=VÄRDE"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "NAMN MOTTAGARE|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "NAMN FILNAMN"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "SÖKVÄGSNAMN"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "FILNAMN MOTTAGARE|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N MOTTAGARE|KÄLLA"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "KORT PROFIL"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "NAMN|#N PORT"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "KORT-NAMN|KORT-#N PORT OFFSET"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "MÅL"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "NUMERISK NIVÅ"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "LJUDRUTOR"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "MOTTAGARE MEDDELANDE [MOTTAGARPARAMETRAR]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Visa denna hjälp\n"
"      --version                         Visa version\n"
"När inget kommando anges startar pacmd i det interaktiva läget.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"Kompilerad med libpulse %s\n"
"Länkad med libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr "Ingen PulseAudio-demon körs, eller körs ej som en sessionsdemon."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Misslyckades med att döda PulseAudio-demon."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Demon svarar ej."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Misslyckades med att hämta statistik: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Används för närvarande: %u block innehållande %s byte totalt.\n"
msgstr[1] "Används för närvarande: %u block innehållande %s byte totalt.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] ""
"Allokerade under hela livstiden: %u block innehållande %s byte totalt.\n"
msgstr[1] ""
"Allokerade under hela livstiden: %u block innehållande %s byte totalt.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Samplingscachestorlek: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Misslyckades med att hämta serverinformation: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr "%s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"Serversträng: %s\n"
"Biblioteksprotokollversion: %u\n"
"Serverprotokollversion: %u\n"
"Lokal: %s\n"
"Klientindex: %u\n"
"Blockstorlek: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Användarnamn: %s\n"
"Värdnamn: %s\n"
"Servernamn: %s\n"
"Serverversion: %s\n"
"Förvald samplingsspecifikation: %s\n"
"Förvald kanalmappning: %s\n"
"Förvald mottagare: %s\n"
"Förvald källa: %s\n"
"Kaka: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "tillgänglighet okänd"

#: src/utils/pactl.c:321
msgid "available"
msgstr "tillgänglig"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "inte tillgänglig"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "Okänd"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Aux"

#: src/utils/pactl.c:335
msgid "Line"
msgstr "Linje"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Mik"

#: src/utils/pactl.c:338
msgid "Handset"
msgstr "Lur"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "Hörlur"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "SPDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "TV"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "Blåtand"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "Nätverk"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "Analog"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Misslyckades med att få information om mottagare: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Mottagare #%u\n"
"\tTillstånd: %s\n"
"\tNamn: %s\n"
"\tBeskrivning: %s\n"
"\tDrivrutin: %s\n"
"\tSamplingsspecifikation: %s\n"
"\tKanalmappning: %s\n"
"\tÄgarmodul: %u\n"
"\tTyst: %s\n"
"\tVolym: %s\n"
"\t        balans %0.2f\n"
"\tBasvolym: %s\n"
"\tÖvervakarkälla: %s\n"
"\tLatens: %0.0f µs, anpassad %0.0f µs\n"
"\tFlaggor: %s%s%s%s%s%s%s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tPortar:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (typ: %s, prioritet: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ", tillgänglighetsgrupp: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tAktiv port: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tFormat:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Misslyckades med att få information om källa: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Källa nr %u\n"
"\tTillstånd: %s\n"
"\tNamn: %s\n"
"\tBeskrivning: %s\n"
"\tDrivrutin: %s\n"
"\tSamplingsspecifikation: %s\n"
"\tKanalmappning: %s\n"
"\tÄgarmodul: %u\n"
"\tTystad: %s\n"
"\tVolym: %s\n"
"\t       balans %0.2f\n"
"\tBasvolym: %s\n"
"\tÖvervakare för mottagare: %s\n"
"\tLatens: %0.0f µs, konfigurerad %0.0f µs\n"
"\tFlaggor: %s%s%s%s%s%s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "ej tillämpligt"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Misslyckades med att få modulinformation: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Modul nr. %u\n"
"\tNamn: %s\n"
"\tArgument: %s\n"
"\tAnvändningsräknare: %s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Misslyckades med att få klientinformation: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Klient #%u\n"
"\tDrivrutin: %s\n"
"\tÄgarmodul: %s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Misslyckades med att få kortinformation: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Kort #%u\n"
"\tNamn: %s\n"
"\tDrivrutin: %s\n"
"\tÄgarmodul: %s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tProfiler:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""
"\t\t%s: %s (mottagare: %u, källor: %u, prioritet: %u, tillgängliga: %s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tAktiv profil: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (typ: %s, prioritet: %u, latensavstånd: %<PRId64> µs%s%s, %s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tEgenskaper:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tDel av profil(er): %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Misslyckades med att få ingångsinformation för mottagaren: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Mottagaringång #%u\n"
"\tDrivrutin: %s\n"
"\tÄgarmodul: %s\n"
"\tKlient: %s\n"
"\tMottagare: %u\n"
"\tSamplingsspecifikation: %s\n"
"\tKanalmappning: %s\n"
"\tFormat: %s\n"
"\tKorkad: %s\n"
"\tTyst: %s\n"
"\tVolym: %s\n"
"\t        balans %0.2f\n"
"\tBuffertlatens: %0.0f µs\n"
"\tMottagarlatens: %0.0f µs\n"
"\tOmsamplingsmetod: %s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "Misslyckades med att få information om källutgång: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Källutgång #%u\n"
"\tDrivrutin: %s\n"
"\tÄgarmodul: %s\n"
"\tKlient: %s\n"
"\tKälla: %u\n"
"\tSamplingsspecifikation: %s\n"
"\tKanalmappning: %s\n"
"\tFormat: %s\n"
"\tKorkad: %s\n"
"\tTyst: %s\n"
"\tVolym: %s\n"
"\t        balans %0.2f\n"
"\tBuffertlatens: %0.0f µs\n"
"\tKällatens: %0.0f µs\n"
"\tOmsamplingsmetod: %s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Misslyckades med att få samplingsinformation: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sampling nr. %u\n"
"\tNamn: %s\n"
"\tSamplingsspecifikation: %s\n"
"\tKanalmappning: %s\n"
"\tVolym: %s\n"
"\t       balans %0.2f\n"
"\tVaraktighet: %0.1f s\n"
"\tStorlek: %s\n"
"\tLat: %s\n"
"\tFilnamn: %s\n"
"\tEgenskaper:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Misslyckande: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "Att skicka ett meddelande misslyckades: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr "listhanterares meddelande misslyckades: %s"

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "listhandlers-meddelandets svar kunde inte tolkas korrekt"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "list-handlers-meddelandets svar är inte en JSON-vektor"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""
"listhanterares meddelandesvars vektorelement %d är inte ett JSON-objekt"

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Misslyckades med att stänga modul: Modulen %s är inte aktiv"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"Misslyckades att sätta volym: du försökte att sätta volymer för %d kanal, "
"medan antalet kanaler som stödjs = %d\n"
msgstr[1] ""
"Misslyckades att sätta volym: du försökte att sätta volymer för %d kanaler, "
"medan antalet kanaler som stödjs = %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Misslyckades med att skicka upp samplingen: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "För tidigt filslut"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "ny"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "ändra"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "ta bort"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "okänd"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "mottagare"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "källa"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "mottagaringång"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "källutgång"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "modul"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "klient"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "sample-cache"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "server"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "kort"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "Händelse '%s' på %s #%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "Fick SIGINT, avslutar."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Ogiltig volymangivelse"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "Volym utanför tillåtet intervall.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Ogiltigt antal volymspecifikationer.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Inkonsekvent volymspecifikation.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[flaggor]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[TYP]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "FILNAMN [NAMN]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "NAMN [MOTTAGARE]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "NAMN|#N VOLYM [VOLYM …]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N VOLYM [VOLYM …]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "NAMN|#N 1|0|växla"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|växla"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N FORMAT"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"Specialnamnen @DEFAULT_SINK@, @DEFAULT_SOURCE och @DEFAULT_MONITOR@\n"
"kan användas för att ange standardmottagare, källa och övervakare.\n"

#: src/utils/pactl.c:2664
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Visa detta hjälpmeddelande\n"
"      --version                         Visa version\n"
"\n"
"  -f --format=FORMAT                    Utdataformatet. Antingen \"normal\" "
"eller \"json\"\n"
"  -s, --server=SERVER                   Namnet på servern att ansluta till\n"
"  -n, --client-name=NAMN                Vad klienten ska kallas på servern\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Kompilerad med libpulse %s\n"
"Länkad med libpulse %s\n"

#: src/utils/pactl.c:2751
#, c-format
msgid "Invalid format value '%s'"
msgstr "Ogiltigt formatvärde '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Specificera inget, eller endera av: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Ange en samplingsfil att ladda"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Misslyckades med att öppna ljudfilen."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr ""
"Varning: Misslyckades med att avgöra samplingsspecifikationen från filen."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Du måste ange ett samplingsnamn att spela"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Du måste ange ett samplingsnamn att ta bort"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "Du måste ange ett index för en ingångsmottagare och en mottagare"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "Du måste ange ett index för en källutgång och en källa"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Du måste ange ett modulnamn och argument."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "Du måste ange ett modulindex eller namn"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Du får inte ange fler än en mottagare. Du måste ange ett booleskt värde."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "Ogiltig avstängningsspecifikation."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr "Du får inte ange fler än en källa. Du måste ange ett booleskt värde."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Du måste ange ett kortnamn/-index och ett profilnamn"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Du måste ange ett mottagarnamn/-index och ett portnamn"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "Du måste ange namn på en mottagare"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Du måste ange ett källnamn/-index och ett portnamn"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "Du måste ange namn på en källa"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "Du måste ange ett namn/index på en sänka"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Du måste ange ett mottagarnamn/-index och en volym"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "Du måste ange ett namn/index på en källa"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Du måste ange ett källnamn/-index och en volym"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Du måste ange ett index för en mottagaringång och en volym"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Ogiltigt index för mottagaringång"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "Du måste ange ett källutgångsindex och en volym"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Ogiltigt källutgångsindex"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Du måste ange en mottagarnamn/-index och ett dämpningsvärde (0, 1, eller "
"”toggle”)"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Ogiltig dämpningsspecifikation"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Du måste ange ett källnamn/-index och ett dämpningsvärde (0, 1, eller "
"”toggle”)"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Du måste ange ett index för en mottagaringång och ett dämpningsvärde (0, 1, "
"eller ”toggle”)"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Ogiltig angivelse av index för mottagaringång"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr ""
"Du måste ange ett källutgångsindex och ett dämpningsvärde (0, 1, eller "
"”toggle”)"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Ogiltig specificering av källutgångsindex"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr "Du måste ange åtminstone en objektsökväg och ett meddelandenamn"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"Överflödiga argument givna, de kommer ignoreras. Observera att alla "
"meddelandeparametrar måste ges som en enda sträng."

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"Du måste ange ett mottagarindex och en semikolonavskild lista med format som "
"stöds"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "Du måste ange ett kortnamn/-index, ett portnamn och en latensoffset"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Kunde inte tolka latensoffset"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Inget giltigt kommando angett."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Misslyckades med att återuppta: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Misslyckades med vänteläge: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "VARNING: Ljudservern är inte lokal, försätter ej i vänteläge.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Anslutningsfel: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "Erhöll SIGINT, avslutar.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "VARNING: Underordnad process avslutad av signalen %u\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [flaggor] -- PROGRAM [ARGUMENT …]\n"
"\n"
"Stäng tillfälligt av PulseAudio medan PROGRAM kör.\n"
"\n"
"  -h, --help                            Visa denna hjälp\n"
"      --version                         Visa version\n"
"  -s, --server=SERVER                   Namnet på servern att ansluta till\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Kompilerad med libpulse %s\n"
"Länkad med libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() misslyckades.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() misslyckades.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() misslyckades.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O mottagare] [-I källa] [-c fil]  [-d|-e|-i|-"
"r]\n"
"\n"
" -d    Visa aktuell PulseAudio-data som hör till X11-display (standard)\n"
" -e    Exportera lokal PulseAudio-data till X11-display\n"
" -i    Importera PulseAudio-data från X11-display till lokala miljövariabler "
"och kakfil.\n"
" -r    Ta bort PulseAudio-data från X11-display\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Misslyckades med att tolka kommandorad.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Server: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Källa: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Mottagare: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Kaka: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Misslyckades med att tolka kakdata\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Misslyckades med att spara kakdata\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Misslyckades med att hämta FQDN.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Misslyckades med att läsa in kakdata\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Ännu inte implementerad.\n"

#~ msgid "Failed to initialize daemon."
#~ msgstr "Misslyckades med att initiera demon."

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "LFE på separat monoutgång"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Digital genomströmning (S/PDIF)"

#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "Digital genomströmning (IEC958)"

#~ msgid ""
#~ "ALSA woke us up to write new data to the device, but there was actually "
#~ "nothing to write!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA väckte oss för att skriva ny data till enheten, men det fanns inget "
#~ "att skriva!\n"
#~ "Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
#~ "detta problem till ALSA-utvecklarna.\n"
#~ "We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."

#~ msgid ""
#~ "ALSA woke us up to read new data from the device, but there was actually "
#~ "nothing to read!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA väckte oss för att läsa ny data från enheten, men det fanns inget "
#~ "att läsa!\n"
#~ "Förmodligen är detta ett fel i ALSA-drivrutinen ”%s”. Vänligen rapportera "
#~ "detta problem till ALSA-utvecklarna.\n"
#~ "We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."

#~ msgid "High Fidelity Playback (A2DP)"
#~ msgstr "High fidelity playback (A2DP)"

#~ msgid "High Fidelity Capture (A2DP)"
#~ msgstr "High fidelity capture (A2DP)"

#~ msgid "Telephony Duplex (HSP/HFP)"
#~ msgstr "Telephony duplex (HSP/HFP)"

#~ msgid "Handsfree Gateway"
#~ msgstr "Handsfree gateway"

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [flaggor]\n"
#~ "\n"
#~ "-h, --help                            Visa denna hjälp\n"
#~ "-v, --verbose                         Skriv ut felmeddelanden\n"
#~ "      --from-rate=SAMPLINGSFREKVENS   Från samplingsfrekvens i Hz "
#~ "(standard 44100)\n"
#~ "      --from-format=SAMPLINGSFORMAT   Från samplingstyp (standard s16le)\n"
#~ "      --from-channels=KANALER         Från antal kanaler (standard 1)\n"
#~ "      --to-rate=SAMPLINGSFREKVENS     Till samplingsfrekvens i Hz "
#~ "(standard 44100)\n"
#~ "      --to-format=SAMPLINGSFORMAT     Till samplingstyp (standard s16le)\n"
#~ "      --to-channels=KANALER           Till antal kanaler (standard 1)\n"
#~ "      --resample-method=METOD         Omsamplingsmetod (standard auto)\n"
#~ "      --seconds=SEKUNDER              Från strömuthållighet (standard "
#~ "60)\n"
#~ "\n"
#~ "Om formaten inte är angivna utför testet alla formatkombinationer,\n"
#~ "fram och tillbaka.\n"
#~ "\n"
#~ "Samplingstyp måste vare en av s16le, s16be, u8, float32le, float32be, "
#~ "ulaw, alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (standard s16ne)\n"
#~ "\n"
#~ "Se --dump-resample-methods för möjliga värden på omsamplingsmetoder.\n"
