PU<PERSON><PERSON><PERSON><PERSON><PERSON> SOUND SERVER

WEB SITE:
	http://pulseaudio.org/

GIT:
	https://gitlab.freedesktop.org/pulseaudio/pulseaudio.git

GitLab:
	https://gitlab.freedesktop.org/pulseaudio/pulseaudio

MAILING LIST:
	http://lists.freedesktop.org/mailman/listinfo/pulseaudio-discuss

GIT COMMITS MAILING LIST:
	http://lists.freedesktop.org/mailman/listinfo/pulseaudio-commits

TRAC/BUGZILLA TICKET CHANGES MAILING LIST:
	http://lists.freedesktop.org/mailman/listinfo/pulseaudio-bugs

IRC:
	https://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/User/Community/#ircandmatrix

FRESHMEAT:
	http://freshmeat.net/projects/pulseaudio/

OHLOH:
	http://www.ohloh.net/projects/4038

AUTHORS:
	Several

HACKING:
	In order to run pulseaudio from the build dir:
	  meson build
	  meson compile -C build
	  build/src/daemon/pulseaudio -n -F build/src/daemon/default.pa -p $(pwd)/build/src/modules/

SPELLING:
        PulseAudio
