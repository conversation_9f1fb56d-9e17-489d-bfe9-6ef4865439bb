# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Path for mixers that have a 'Headphone' control
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 99
description-key = analog-output-headphones

[Properties]
device.icon_name = audio-headphones

[Jack Dock Headphone]
required-any = any

[Jack Dock Headphone Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

[Jack Front Headphone]
required-any = any

; HP EliteDesk 800 DM Headset
[Jack Front Headphone Front]
required-any = any

[Jack Front Headphone Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

[Jack Headphone]
required-any = any

[Jack Headphone Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

# This jack can be either a headphone *or* a mic. Used on some ASUS netbooks.
[Jack Headphone Mic]
required-any = any

[Jack Headphone - Output]
required-any = any

[Element Hardware Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master Mono]
switch = off
volume = off

[Element Speaker+LO]
switch = off
volume = off

[Element Headphone+LO]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Headphone]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

; This path is intended to control the first headphones, not
; the second headphones. But it should not hurt if we leave the second
; headphone jack enabled nonetheless.
[Element Headphone,1]
switch = mute
volume = zero

[Element Headset]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Line HP Swap]
switch = on
required-any = any

; This profile path is intended to control the first headphones, not
; the second headphones. But it should not hurt if we leave the second
; headphone jack enabled nonetheless.
[Element Headphone2]
switch = mute
volume = zero

[Element Speaker]
switch = off
volume = off

[Element Desktop Speaker]
switch = off
volume = off

; On some machines, the Front Volume Control is shared by Headphone and Lineout,
; or Headphone and Speaker, but they have independent Volume Switch. Here only
; use switch to mute Lineout or Speaker.
[Element Front]
switch = off
volume = zero

[Element Rear]
switch = off
volume = off

[Element Surround]
switch = off
volume = off

[Element Side]
switch = off
volume = off

[Element Center]
switch = off
volume = off

[Element LFE]
switch = off
volume = off

[Element Bass Speaker]
switch = off
volume = off

[Element Speaker Front]
switch = off
volume = off

[Element Speaker Surround]
switch = off
volume = off

[Element Speaker Side]
switch = off
volume = off

[Element Speaker CLFE]
switch = off
volume = off

[Element Speaker Center/LFE]
switch = off
volume = off

.include analog-output.conf.common
