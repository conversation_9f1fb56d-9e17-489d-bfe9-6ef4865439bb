#!/bin/sh -e
[ -z "$DEBUG" ] || set -x
echo "########## pulseaudio-17.0: configure ##########"
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -rf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build
mkdir -p /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build
sed -e "/^\[binaries\]$/s:$::" -e "/^\[properties\]$/s:$::"         -e "s%@TARGET_CC@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/aarch64-buildroot-linux-gnu-gcc%g" -e "s%@TARGET_CXX@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/aarch64-buildroot-linux-gnu-g++%g" -e "s%@TARGET_AR@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/aarch64-buildroot-linux-gnu-ar%g" -e "s%@TARGET_STRIP@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/aarch64-buildroot-linux-gnu-strip%g"         -e "s%@TARGET_FC@%/bin/false%g" -e "s%@TARGET_ARCH@%aarch64%g" -e "s%@TARGET_CPU@%cortex-a53%g" -e "s%@TARGET_ENDIAN@%little%g" -e "s%@TARGET_FCFLAGS@%%g" -e "s%@TARGET_CFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-Os', '-g0', '-D_FORTIFY_SOURCE=1'%g" -e "s%@TARGET_LDFLAGS@%%g" -e "s%@TARGET_CXXFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-Os', '-g0', '-D_FORTIFY_SOURCE=1'%g" -e "s%@BR2_CMAKE@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/cmake%g" -e "s%@PKGCONF_HOST_BINARY@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/pkgconf%g" -e "s%@HOST_DIR@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host%g" -e "s%@STAGING_DIR@%/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/aarch64-buildroot-linux-gnu/sysroot%g" -e "s%@STATIC@%false%g" /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/support/misc/cross-compilation.conf.in > /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build/cross-compilation.conf
PATH="/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin" CC_FOR_BUILD="/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/ccache /usr/bin/gcc" CXX_FOR_BUILD="/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/ccache /usr/bin/g++"  PYTHONNOUSERSITE=y /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/meson setup --prefix=/usr --libdir=lib --default-library=shared --buildtype=release --cross-file=/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build/cross-compilation.conf -Db_pie=false -Dstrip=false -Dbuild.pkg_config_path=/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/lib/pkgconfig -Dbuild.cmake_prefix_path=/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/lib/cmake -Ddoxygen=false -Dlegacy-database-entry-format=false -Dman=false -Drunning-from-build-tree=false -Dtests=false -Davahi=disabled -Ddbus=enabled -Dfftw=disabled -Dsamplerate=enabled -Ddatabase=simple -Djack=disabled -Dlirc=disabled -Dopenssl=enabled -Dorc=disabled -Dgtk=disabled -Dsoxr=disabled -Dbluez5=enabled -Dudev=enabled -Dwebrtc-aec=disabled -Dalsa=enabled -Dx11=disabled -Dspeex=disabled -Dsystemd=disabled -Dvalgrind=disabled /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/ /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build
