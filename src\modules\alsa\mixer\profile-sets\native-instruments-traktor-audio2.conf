# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Native Instruments Traktor Audio 2
;
; This card has two stereo pairs of output.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-a]
description = Analog Stereo Channel A
device-strings = hw:%f,0,0
channel-map = left,right
direction = output

[Mapping analog-stereo-b]
description = Analog Stereo Channel B
device-strings = hw:%f,0,1
channel-map = left,right
direction = output

[Profile output:analog-stereo-a]
description = Analog Stereo Output Channel A
output-mappings = analog-stereo-a
priority = 60
skip-probe = yes

[Profile output:analog-stereo-b]
description = Analog Stereo Output Channel B
output-mappings = analog-stereo-b
priority = 50
skip-probe = yes

[Profile analog-stereo-all]
description = Analog Stereo Output Channels A & B
output-mappings = analog-stereo-a analog-stereo-b
priority = 100
skip-probe = yes
