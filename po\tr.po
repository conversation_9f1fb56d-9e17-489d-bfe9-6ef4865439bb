# Turkish translation for PulseAudio.
# Copyright (C) 2014 PulseAudio's COPYRIGHT HOLDER
# This file is distributed under the same license as the PulseAudio package.
# <AUTHOR> <EMAIL>, 2014.
# <PERSON><PERSON> <ka<PERSON>z<PERSON><EMAIL>>, 2014.
# <AUTHOR> <EMAIL>, 2015, 2016, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: PulseAudio master\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2023-05-27 21:20+0000\n"
"Last-Translator: Sabri Ünal <<EMAIL>>\n"
"Language-Team: Turkish <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/tr/>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.17\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [seçenekler]\n"
"\n"
"KOMUTLAR:\n"
"  -h, --help                            Bu yardımı gösterir\n"
"      --version                         Sürümü gösterir\n"
"      --dump-conf                       Öntanımlı yapılandırmayı döker\n"
"      --dump-modules                    Kullanılabilir modüllerin listesini "
"döker\n"
"      --dump-resample-methods           Kullanılabilir yeniden örnekleme "
"yöntemlerini döker\n"
"      --cleanup-shm                     Eski paylaşımlı bellek segmentlerini "
"temizler\n"
"      --start                           Eğer çalışmıyorsa art alan hizmetini "
"başlatır\n"
"  -k  --kill                             Çalışan bir art alan hizmetini "
"sonlandırır\n"
"      --check                         Çalışan bir art alan hizmetini "
"denetler (sadece çıkış kodu döner)\n"
"\n"
"SEÇENEKLER:\n"
"      --system[=BOOL]                   Sistem çapında örnek olarak "
"çalıştırır\n"
"  -D, --daemonize[=BOOL]                Başladıktan sonra art alan hizmeti "
"olarak çalıştırır\n"
"      --fail[=BOOL]                     Başlangıç başarısız olduğunda çıkar\n"
"      --high-priority[=BOOL]            Yüksek öncelik seviyesi ayarlamayı "
"dener\n"
"                                        (yalnızca root olarak, SUID "
"olduğunda veya\n"
"                                        yüksek RLIMIT_NICE ile "
"kullanılabilir)\n"
"      --realtime[=BOOL]                 Gerçek zamanlı zamanlamayı "
"etkinleştirmeyi dener\n"
"                                        (yalnızca root olarak, SUID "
"olduğunda veya\n"
"                                        yüksek RLIMIT_NICE ile "
"kullanılabilir)\n"
"      --disallow-module-loading[=BOOL]  Başlangıçtan sonra kullanıcının\n"
"                                        yükleme/kaldırma istediği modüllere "
"izin vermez\n"
"      --disallow-exit[=BOOL]            Kullanıcının çıkış isteğine izin "
"vermez\n"
"      --exit-idle-time=SANİYE           Boştayken ve bu süre geçtiğinde\n"
"                                        art alan hizmetini sonlandırır\n"
"      --scache-idle-time=SANİYE         Boştayken ve bu süre geçtiğinde "
"otomatik\n"
"                                        yüklenmiş örnekleri kaldırır\n"
"      --log-level[=SEVİYE]              Ayrıntı seviyesini ayarlar ya da "
"artırır\n"
"  -v  --verbose                         Ayrıntı seviyesini artırır\n"
"      --log-target={auto,syslog,stderr,file:YOL,newfile:YOL}\n"
"                                        Günlük kayıtlarının bulunacağı hedef "
"yolu belirtir\n"
"      --log-meta[=BOOL]                 Günlük mesajlarına kod konumlarını "
"dahil eder\n"
"      --log-time[=BOOL]                  Günlük mesajlarına zaman "
"damgalarını dahil eder\n"
"      --log-backtrace=ÇERÇEVELER        Günlük mesajlarına bir geri izleme "
"dahil eder\n"
"  -p, --dl-search-path=YOL              Dinamik paylaşımlı nesneler "
"(eklentiler)\n"
"                                        için arama yolu ayarlar\n"
"      --resample-method=YÖNTEM          Belirtilen yeniden örneklendirme "
"yöntemini kullanır\n"
"                                        (Olası değerler için --dump-resample-"
"methods\n"
"                                         seçeneğine bakın)\n"
"      --use-pid-file[=BOOL]             Bir PID dosyası oluşturur\n"
"      --no-cpu-limit[=BOOL]             Desteklendiği platformlarda\n"
"                                        CPU yükü sınırlayıcı kurmaz.\n"
"      --disable-shm[=BOOL]              Paylaşımlı bellek desteğini devre "
"dışı bırakır.\n"
"      --enable-memfd[=BOOL]             memfd paylaşılan bellek desteğini "
"etkinleştirir.\n"
"\n"
"BAŞLATMA BETİĞİ:\n"
"  -L, --load=\"MODÜL ARGÜMANLARI\"        Belirtilen argümanlar ile\n"
"                                        belirtilen eklenti modüllerini "
"yükler.\n"
"  -F, --file=DOSYA_ADI                  Belirtilen betiği çalıştırır\n"
"  -C                                    Başlangıçtan sonra çalışan TTY "
"üzerinde\n"
"                                        bir komut satırı açar\n"
"\n"
"  -n                                    Öntanımlı betik dosyasını yüklemez\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize boolean değişken bekler"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail boolean değişken bekler"

#: src/daemon/cmdline.c:265
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level günlük seviyesi değişkeni bekler (ya 0..4 aralığında sayısal "
"değer ya da error, warn, notice, info, debug değişkenlerinden birini)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority boolean değişken bekler"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime boolean değişken bekler"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading boolean değişken bekler"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit boolean değişken bekler"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file boolean değişken bekler"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Geçersiz günlük hedefi: ya 'syslog', 'journal', 'stderr', 'auto' ya da "
"geçerli bir dosya adı 'file:<yol>', 'newfile:<yol>' kullanın."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Geçersiz günlük hedefi: ya 'syslog', 'journal', 'stderr', 'auto' ya da "
"geçerli bir dosya adı 'file:<yol>', 'newfile:<yol>' kullanın."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time boolean değişken bekler"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta boolean değişken bekler"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Geçersiz yeniden örneklendirme yöntemi '%s'."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system boolean değişken bekler"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit boolean değişken bekler"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm boolean değişken bekler"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd boolean değişken bekler"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Geçersiz günlük hedefi '%s'."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Geçersiz günlük seviyesi '%s'."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Geçersiz yeniden örneklendirme yöntemi '%s'."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] Geçersiz rlimit '%s'."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Geçersiz örnekleme biçimi '%s'."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Geçersiz örnekleme oranı '%s'."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Geçersiz örnekleme kanalları '%s'."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Geçersiz kanal adresleme '%s'."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Geçersiz bölümlenme sayısı '%s'."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Geçersiz bölümlenme boyutu '%s'."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Geçersiz nice seviyesi '%s'."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Geçersiz sunucu türü '%s'."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Yapılandırma dosyası açılamadı: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"Belirtilen öntanımlı kanal adresleme belirtilmiş öntanımlı kanal sayısından "
"farklı sayıda kanala sahiptir."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Yapılandırma dosyasından oku: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Ad: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Hiçbir modül bilgisi bulunamadı\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Sürüm: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Açıklama: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Yazar: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Kullanım: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Bir kez Yükle: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "UYGUNSUZLUK UYARISI: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Yol: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "'%s' modülü açılamadı: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Orjinal lt_dlopen yükleyicisi bulunamadı."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Yeni dl yükleyici ayırma işlemi başarısız oldu."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Bind-now-loader eklenemedi."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Kullanıcı '%s' bulunamadı."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Grup '%s' bulunamadı."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "'%s' kullanıcısı ve '%s' grubunun GID eşleşmiyor."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "'%s' kullanıcısının ev dizini '%s' değil, yok sayılıyor."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "'%s' oluşturulamadı: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Grup listesi değiştirilemedi: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "GID değiştirilemedi: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "UID değiştirilemedi: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "Sistem geneli kipi bu platformda desteklenmiyor."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Komut satırı çözümlenemedi."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Sistem kipi root olmayan kullanıcıyı reddetti. Sadece D-Bus sunucu arama "
"servisi başlatıyor."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Art alan hizmeti durdurulamadı: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Bu programın root kullanıcı ile çalıştırılması amaçlanmamıştır (--system "
"belirtilmediyse)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Root hakları gereklidir."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start sistem örnekleri için desteklenmiyor."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""
"Kullanıcı tarafından yapılandırılmış şuradaki sunucu: %s, başlamayı/otomatik "
"başlamayı reddediyor."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Kullanıcı tarafından yapılandırılmış, yerel olarak görülen şuradaki sunucu: "
"%s. Daha derinlemesine sorgulanıyor."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "Sistem kipinde çalıştırılıyor fakat --disallow-exit ayarlı değil."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"Sistem kipinde çalıştırılıyor fakat --disallow-module-loading ayarlı değil."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "Sistem kipinde çalıştırılıyor, SHM kipi zorla devre dışı bırakılıyor."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"Sistem kipinde çalıştırılıyor, boşta kalma süresi çıkışı zorla devre dışı "
"bırakılıyor."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Stdio alınamadı."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() başarısız oldu: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() başarısız oldu: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() başarısız oldu: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Art alan hizmetini başlatma başarısız oldu."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() başarısız oldu: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Makine kimliği alınamadı"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"Tamam, sistem kipinde PA çalıştırıyorsunuz. Lütfen gerçekten bunu yapmak "
"istediğinizden emin olun.\n"
"Lütfen sistem kipinin neden genellikle kötü bir fikir olduğunun açıklamasını "
"http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/User/"
"WhatIsWrongWithSystemWide/ adresinden okuyunuz."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() başarısız oldu."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() başarısız oldu."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "komut satırı argümanları"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"Başlatma komutlarını çalıştırırken oluşan hatalar nedeniyle art alan hizmeti "
"başlatılamadı. Komutların kaynağı: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr ""
"Hiç modül yüklenmeden çalışmaya başlamayan art alan hizmeti, çalışmayı "
"reddediyor."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "PulseAudio Ses Sistemi"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "PulseAudio Ses Sistemini Başlat"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Giriş"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Yerleştirme İstasyonu Girişi"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Yerleştirme İstasyonu Mikrofonu"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "Yerleştirme İstasyonu Hat Girişi"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "Hat Girişi"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Ön Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Arka Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Harici Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Dahili Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Radyo"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Video"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Otomatik Kazanç Denetimi"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Otomatik Kazanç Denetimi Yok"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Artır"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Artırma Yok"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Yükseltici"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Yükseltici Yok"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Bas Artır"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Bas Artırma Yok"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Hoparlör"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Kulaklık"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Analog Giriş"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Yapışık Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Mikrofonlu Kulaklık"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Analog Çıkış"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "Kulaklık 2"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "Kulaklık Tek Kanallı Çıkış"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "Hat Çıkışı"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Analog Tek Kanallı Çıkış"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Hoparlörler"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Sayısal Çıkış (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Sayısal Giriş (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "Çok Kanallı Giriş"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "Çok Kanallı Çıkış"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "Oyun Çıkışı"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "Sohbet Çıkışı"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "Sohbet Girişi"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "Sanal Çevresel Ses 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Analog Tek Kanallı"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "Analog Tek Kanallı (Sol)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "Analog Tek Kanallı (Sağ)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Analog Stereo"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Tek Kanallı"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Stereo"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Kulaklık"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "Hoparlör"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Çok kanallı"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Analog Çevresel Ses 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Analog Çevresel Ses 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Analog Çevresel Ses 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Analog Çevresel Ses 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Analog Çevresel Ses 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Analog Çevresel Ses 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Analog Çevresel Ses 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Analog Çevresel Ses 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Analog Çevresel Ses 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Analog Çevresel Ses 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Analog Çevresel Ses 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Sayısal Stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Sayısal Çevresel Ses 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Sayısal Çevresel Ses 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Sayısal Çevresel Ses 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Sayısal Stereo (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Sayısal Çevresel Ses 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "Sohbet"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "Oyun"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Analog Tek Kanallı İkili"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Analog İkili Stereo"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Sayısal İkili Stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "Çok Kanallı Duplex"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "İkili Stereo"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "Tek Kanallı Sohbet + 7.1 Çevresel Ses"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Kapalı"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "%s Çıkışı"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "%s Girişi"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA aygıta yeni veri yazmamız için bizi uyardı fakat aslında yazılacak "
"hiçbir şey yoktu.\n"
"Büyük ihtimalle bu ALSA '%s' sürücüsünde bir hatadır. Lütfen bu sorunu ALSA "
"geliştiricilerine bildirin.\n"
"Biz POLLOUT ayarı ile uyandırıldık -- bununla birlikte sonraki "
"snd_pcm_avail() 0 ya da min_avail değerinden küçük başka bir değer döndü."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA aygıttan yeni veri okumamız için bizi uyardı fakat aslında okunacak "
"hiçbir şey yok.\n"
"Büyük ihtimalle bu bir ALSA '%s' sürücüsü hatasıdır. Lütfen bu sorunu ALSA "
"geliştiricilerine bildirin.\n"
"Biz POLLIN ayarı ile uyandırıldık -- bununla birlikte sonraki bir "
"snd_pcm_avail() 0 ya da min_avail değerinden küçük başka bir değer döndü."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() son derece büyük bir değer döndürdü: %lu bayt (%lu ms).\n"
"Büyük ihtimalle bu bir ALSA sürücüsü '%s' hatasıdır. Lütfen bu sorunu ALSA "
"geliştiricilerine bildirin."

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() son derece büyük bir değer döndürdü: %li bayt (%s%lu ms).\n"
"Büyük ihtimalle bu bir ALSA sürücüsü '%s' hatasıdır. Lütfen bu sorunu ALSA "
"geliştiricilerine bildirin."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() garip değerler döndü: gecikme %lu kazançtan %lu daha "
"azdır.\n"
"Büyük ihtimalle bu bir ALSA sürücüsü '%s' hatasıdır. Lütfen bu sorunu ALSA "
"geliştiricilerine bildirin."

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() son derece büyük bir değer döndürdü: %lu bayt (%lu "
"ms).\n"
"Büyük ihtimalle bu bir ALSA sürücüsü '%s' hatasıdır. Lütfen bu sorunu ALSA "
"geliştiricilerine bildirin."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Bluetooth Girişi"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Bluetooth Çıkışı"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Ahizesiz"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Kulaklık"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Taşınabilir"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Araba"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "Yüksek Kalite"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Telefon"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "Yüksek Kaliteli Çalma (A2DP Alıcı)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "Yüksek Kaliteli Yakalama (A2DP Kaynak)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "Kulaklık Ana Birimi (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "Kulaklık Ses Geçidi (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "Ahizesiz Ana Birimi (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Ahizesiz Ses Geçidi (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<kaynak adı> source_properties=<kaynak özellikleri> "
"source_master=<süzülecek kaynağın adı> sink_name=<alıcı adı> "
"sink_properties=<alıcı özellikleri> sink_master=<süzülecek alıcının adı> "
"adjust_time=<hızların kaç saniyede bir yeniden ayarlanacağı> "
"adjust_threshold=<kaç ms sonra kaymanın yeniden ayarlanacağı> format=<örnek "
"biçimi> rate=<örnek hızı> channels=<kaynak sayısı> channel_map=<kaynak "
"eşlem> aec_method=<kullanılacak uygulanış> aec_args=<AEC motorunun "
"parametreleri> save_aec=<AEC verilerini /tmp içine kaydet> autoloaded=<bu "
"modülün otomatik olarak yüklenip yüklenmeyeceğini belirle> "
"use_volume_sharing=<yes ya da no> use_master_format=<yes ya da no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "Açık"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Sahte Çıkış"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "Yüklenen alıcı boş bile olsa her zaman en az bir tanesini korur"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "Boş olsa bile her zaman en az bir kaynağı yüklü tutar"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "Genel Amaçlı Dengeleyici"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"alıcı_adı=<name of the sink> alıcı_özellikleri=<properties for the sink> "
"ana_alıcı=<sink to connect to> biçim=<sample format> hız=<sample rate> "
"kanallar=<number of channels> kanal_adresleme=<channel map> "
"otomatikyüklenmiş=<set if this module is being loaded automatically> "
"ses_paylaşım_kullan=<yes or no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "%s üzerinde FFT tabanlı dengeleyici"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "otomatik temizle=<automatically unload unused filters?>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Sanal LADSPA alıcısı"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<alıcı adı> sink_properties=<alıcı özellikleri> "
"sink_input_properties=<alıcı girişi özellikleri> master=<süzülecek alıcının "
"adı> sink_master=<süzülecek alıcının adı> format=<örnek biçimi> rate=<örnek "
"hızı> channels=<kanal sayısı> channel_map=<giriş kanal haritası> "
"plugin=<ladspa eklenti adı> label=<ladspa eklenti etiketi> control=<giriş "
"denetim değerlerinin virgülle ayrılmış listesi> input_ladspaport_map=<giriş "
"LADSPA bağlantı noktası adlarının virgülle ayrılmış listesi> "
"output_ladspaport_map=<çıkış LADSPA bağlantı noktası adlarının virgülle "
"ayrılmış listesi> autoloaded=<eğer bu modül kendiliğinden yükleniyorsa bunu "
"ayarlayın> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Zamanlanmış BOŞ alıcı"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Boş Çıkış"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Biçim ayarlanamadı: geçersiz biçim dizisi %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Çıkış Aygıtları"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Giriş Aygıtları"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "@HOSTNAME@ üzerindeki SES"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "%s@%s için tünel"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "%s/%s tünel"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Sanal çevresel ses alıcısı"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<alıcı adı> sink_properties=<alıcı özellikleri> master=<süzülecek "
"alıcı adı> sink_master=<süzülecek alıcı adı> format=<örnek biçimi> "
"rate=<örnek hızı> channels=<kanal sayısı> channel_map=<kanal haritası> "
"use_volume_sharing=<evet ya da hayır> force_flat_volume=<evet ya da hayır> "
"hrir=/dosya/yolu/left_hrir.wav hrir_left=/dosya/yolu/left_hrir.wav "
"hrir_right=/isteğe/bağlı/dosya/yolu/right_hrir.wav autoloaded=<eğer bu modül "
"kendiliğinden yükleniyorsa bunu ayarlayın> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "Bilinmeyen aygıt modeli"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "RAOP standart profili"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "PulseAudio Ses Sunucusu"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Ön Orta"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Ön Sol"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Ön Sağ"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Arka Orta"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Arka Sol"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Arka Sağ"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Derin Bas Hoparlör"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Ön Ortanın Solu"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Ön Ortanın Sağı"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Sol Yan"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Sağ Yan"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Harici 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Harici 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Harici 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Harici 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Harici 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Harici 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Harici 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Harici 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Harici 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Harici 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Harici 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Harici 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Harici 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Harici 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Harici 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Harici 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Harici 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Harici 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Harici 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Harici 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Harici 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Harici 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Harici 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Harici 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Harici 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Harici 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Harici 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Harici 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Harici 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Harici 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Harici 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Harici 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Üst Orta"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Üst Ön Orta"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Üst Ön Sol"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Üst Ön Sağ"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Üst Arka Orta"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Üst Arka Sol"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Üst Arka Sağ"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(geçersiz)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Çevresel Ses 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Çevresel Ses 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Çevresel Ses 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Çevresel Ses 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Çevresel Ses 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() başarısız oldu"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() doğru değer döndü"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Çerez veri ayrıştırılamadı"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Bilinmeyen eklenti '%s' için ileti alındı"

#: src/pulse/direction.c:37
msgid "input"
msgstr "giriş"

#: src/pulse/direction.c:39
msgid "output"
msgstr "çıkış"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "çift yönlü"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "geçersiz"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) bize ait değil (uid %d), ancak uid %d kullanıcısına "
"ait! (Bu, örneğin bir root kullanıcısı olarak root olmayan bir PulseAudio'ya "
"yerel protokol üzerinden bağlanmaya çalışırsanız gerçekleşebilir. Bunu "
"yapmayın.)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "evet"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "hayır"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Otomatik oluşturma kilidine erişim yok."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Hedef dosya '%s' açılamadı."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Hedef dosyaları '%s', '%s.1', '%s.2' ... '%s.%d' açmayı denedi fakat hiçbiri "
"açılamadı."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Geçersiz günlük hedefi."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Dahili Ses"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "TAMAM"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Erişim engellendi"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Bilinmeyen komut"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Geçersiz değişken"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "Giriş var"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Hiçbir giriş yok"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Bağlantı reddedildi"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Protokol hatası"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Zaman aşımı"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Kimlik doğrulama anahtarı yok"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "İç hata"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Bağlantı sonlandırıldı"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Giriş durduruldu"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Geçersiz sunucu"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Modül başlatma başarısız oldu"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Kötü durum"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Veri yok"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Uyumsuz protokol sürümü"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Çok büyük"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Desteklenmeyen"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Bilinmeyen hata kodu"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Böyle bir uzantı yok"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Kullanılmayan işlev"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Eksik uygulama"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Müşteri çatallandı"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Giriş/Çıkış hatası"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Aygıt ya da kaynak meşgul"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Akış boşaltma başarısız oldu: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Playback akışı boşaltıldı."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Sunucuya akış bağlantısı."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() başarısız oldu: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() başarısız oldu: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Akış başarılı bir şekilde oluşturuldu."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() başarısız oldu: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Tampon ölçüleri: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Tampon ölçüleri: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "Örnekleme tanımı '%s', kanal listesi '%s' kullanma."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Aygıta %s bağlanıldı (dizin: %u, askıda kalan: %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Akış hatası: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Akış aygıtı askıda. %s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Akış aygıtı devam ettirildi.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Akış yetersiz.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Akış taşması.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Akış başladı. %s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Akış %s aygıtına taşındı (%u, %sertelenmiş).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "değil "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Akış tampon bellek özellikleri değişti. %s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Durdurma isteği yığını boş: akış durduruluyor"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Durdurma isteği yığını boş: akış durdurma sonlandırılıyor"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr "Uyarı: Durdurma isteğinden daha fazla devam ettirme isteği alındı."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Bağlantı kuruldu.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() başarısız oldu: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() başarısız oldu: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Akış izleme ayarlanamadı: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() başarısız oldu: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Bağlantı hatası: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF Al."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() başarısız oldu: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() başarısız oldu: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Sinyal alındı, çıkılıyor."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Gecikme alınamadı: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Zaman: %0.3f saniye; Gecikme: %0.0f usec."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() başarısız oldu: %s"

#: src/utils/pacat.c:676
#, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [seçenekler]\n"
"%s\n"
"\n"
"  -h, --help                          Yardımı gösterir\n"
"      --version                       Sürümü gösterir\n"
"\n"
"  -r, --record                        Kayıt için bir bağlantı oluşturur\n"
"  -p, --playback                      Çalmak için bir bağlantı oluşturur\n"
"\n"
"  -v, --verbose                       Ayrıntılı işlemleri etkinleştirir\n"
"\n"
"  -s, --server=SUNUCU                 Bağlanılacak sunucunun adı\n"
"  -d, --device=AYGIT                  Bağlanılacak alıcı/kaynak adı. "
"@DEFAULT_SINK@, @DEFAULT_SOURCE@ ve @DEFAULT_MONITOR@ özel adları sırasıyla "
"öntanımlı alıcıyı, kaynağı ve monitörü belirtmek için kullanılabilir.\n"
"  -n, --client-name=AD                Sunucu üzerinde bu istemciye ne ad "
"verileceği\n"
"      --stream-name=AD                Sunucu üzerinde bu akışa ne ad "
"verileceği\n"
"      --volume=SESDÜZEYİ              0...65536 aralığında başlangıç ses "
"düzeyini (doğrusal) belirtir\n"
"      --rate=ÖRNEKLEMEHIZI            Hz cinsinde örnekleme oranı (öntanımlı "
"değer: 44100)\n"
"      --format=ÖRNEKLEMEBİÇİMİ        Örnekleme biçimi, olası değerler için\n"
"                                      https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                      adresine bakın (öntanımlı değer: "
"s16ne)\n"
"      --channels=KANALLAR             Kanal sayısı, mono için 1, stereo için "
"2\n"
"                                      (öntanımlı değer: 2)\n"
"      --channel-map=KANALADRESLEME    Öntanımlı yerine kullanılacak kanal "
"eşleme\n"
"      --fix-format                    Örnekleme biçimini akışın bağlı olduğu "
"alıcı/kaynaktan alır.\n"
"      --fix-rate                      Örnekleme oranını akışın bağlı olduğu "
"alıcı/kaynaktan alır.\n"
"      --fix-channels                  Kanal eşlemesini ve kanal sayısını "
"akışın bağlı olduğu\n"
"                                      alıcı/kaynaktan alır.\n"
"      --no-remix                      Kanalları indirgemez ya da "
"çoğaltamaz.\n"
"      --no-remap                      Ad yerine dizin ile kanalları eşler.\n"
"      --latency=BAYT                  Bayt cinsinden belirtilen gecikmeyi "
"ister.\n"
"      --process-time=BAYT             Bayt cinsinden istek başına belirtilen "
"işlem\n"
"                                      zamanını ister.\n"
"      --latency-msec=MİLİSANİYE       Milisaniye cinsinde belirtilen "
"gecikmeyi ister.\n"
"      --process-time-msec=MİLİSANİYE  Milisaniye cinsinde istek başına "
"belirtilen\n"
"                                      işlem zamanını ister.\n"
"      --property=ÖZELLİK=DEĞER        Belirtilen özelliği, belirtilen değere "
"ayarlar.\n"
"      --raw                           Ham PCM veri kaydeder/çalar.\n"
"      --passthrough                   Doğrudan veri geçişi.\n"
"      --file-format[=DBİÇİMİ]         Biçimlendirilmiş PCM verilerini "
"kaydeder/çalar.\n"
"      --list-file-formats             Kullanılabilir dosya biçimlerini "
"listeler.\n"
"      --monitor-stream=DİZİN            DİZİN dizinine sahip alıcı "
"girişinden kayıt yapar.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""
"Bir PulseAudio ses sunucusu üzerinde kodlanmış ses dosyalarını oynatın."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""
"Bir PulseAudio ses sunucusundan ses verilerini yakalayın ve bir dosyaya "
"yazın."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"Bir PulseAudio ses sunucusundan ses verilerini yakalayın ve STDOUT'a ya da "
"belirtilen dosyaya yazın."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"Bir PulseAudio ses sunucusu üzerinde STDIN'den ya da belirtilen dosyadan ses "
"verilerini oynatın."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Libpulse %s ile derlendi\n"
"Libpulse %s ile bağlantılı\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Geçersiz istemci adı '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Geçersiz akış adı '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Geçersiz kanal adresleme '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Geçersiz gecikme tanımı '%s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Geçersiz işlem zaman tanımı '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Geçersiz özellik '%s'"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Bilinmeyen dosya biçimi %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "--monitor-stream için değişken ayrıştırılamadı"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Geçersiz örnekleme tanımı"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Çok fazla değişken."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Dosya için örnekleme tanımı oluşturulamadı."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Ses dosyası açılamadı."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Uyarı: belirtilmiş örnek tanımlama, dosyadan alınacak başka bir tanımlama "
"ile üzerine yazılacak."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Dosyadan örnekleme tanımı belirlenemedi."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "Uyarı: Dosyadan kanal adresleme belirlenemedi."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "Kanal adresleme örnekleme tanımı ile eşleşmiyor"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Uyarı: kanal adresleme dosyaya yazılamadı."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Örnekleme tanımı '%s' ve kanal adresleme '%s' ile bir %s akışı açılıyor."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "kaydediliyor"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "çal"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Ortam adı ayarlanamadı."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() başarısız oldu."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() başarısız oldu."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() başarısız oldu."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() başarısız oldu: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() başarısız oldu."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() başarısız oldu."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "AD [DEĞİŞKENLER ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "AD|#A"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "AD"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "AD|#A SES"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N SES"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "AD|#A 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#A 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "AD|#A ANAHTAR=DEĞER"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#A ANAHTAR=DEĞER"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#A"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "AD ALICI|#A"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "AD DOSYAADI"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "YOLADI"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "DOSYAADI ALICI|#A"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#A ALICI|KAYNAK"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "KART PROFİLİ"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "AD|#A BAĞLANTI NOKTASI"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "KART-ADI|KART-#N BAĞLANTI-NOKTASI UZAKLIK"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "HEDEF"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "SAYISAL-SEVİYE"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "KARELER"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "ALICI MESAJ [MESAJ_PARAMETRELERİ]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Yardım gösterir\n"
"      --version                         Sürüm gösterir\n"
"pacmd için komut verilmediğinde karşılıklı konuşma kipinde başlar.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"libpulse %s ile derlendi\n"
"libpulse %s ile bağlantılı\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"Çalışan PulseAudio art alan hizmeti yok veya bir oturum art alan hizmeti "
"olarak çalışmıyor."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "PulseAudio art alan hizmeti durdurulamadı."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Art alan hizmeti yanıt vermiyor."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "İstatistikler alınamadı: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Şu anda kullanımda: %u blok toplamda %s bayt içeriyor.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "Tüm ömrü boyunca ayrılan: %u blok toplamda %s bayt içeriyor.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Örnekleme ön bellek boyutu: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Sunucu bilgisi alınamadı: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr "%s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"Sunucu Karakter Dizisi: %s\n"
"Kütüphane Protokol Sürümü: %u\n"
"Sunucu Protokol Sürümü: %u\n"
"Yerelde Mi: %s\n"
"İstemci Dizini: %u\n"
"Desen Boyutu: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Kullanıcı Adı: %s\n"
"Ana Makine Adı: %s\n"
"Sunucu Adı: %s\n"
"Sunucu Sürümü: %s\n"
"Öntanımlı Örnekleme Tanımı: %s\n"
"Öntanımlı Kanal Adresleme: %s\n"
"Öntanımlı Alıcı: %s\n"
"Öntanımlı Kaynak: %s\n"
"Çerez: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "kullanılabilirlik bilinmiyor"

#: src/utils/pactl.c:321
msgid "available"
msgstr "kullanılabilir"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "kullanılabilir değil"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "Bilinmeyen"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Harici"

#: src/utils/pactl.c:335
msgid "Line"
msgstr "Hat"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Mikrofon"

#: src/utils/pactl.c:338
msgid "Handset"
msgstr "Ahize"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "Kulak içi kulaklık"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "SPDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "TV"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "Bluetooth"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "Ağ"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "Analog"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Alıcı bilgisi alınamadı: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Alıcı #%u\n"
"\tDurum: %s\n"
"\tAd: %s\n"
"\tAçıklama: %s\n"
"\tSürücü: %s\n"
"\tÖrnekleme Tanımı: %s\n"
"\tKanal Adresleme: %s\n"
"\tModül Sahibi: %u\n"
"\tSessiz: %s\n"
"\tSes: %s\n"
"\t        denge %0.2f\n"
"\tTemel Ses: %s\n"
"\tİzleme Kaynağı: %s\n"
"\tGecikme: %0.0f usec, yapılandırılmış %0.0f usec\n"
"\tBayraklar: %s%s%s%s%s%s%s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tBağlantı Noktaları:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (tür: %s, öncelik: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ", kullanılabilirlik grubu: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tEtkin Bağlantı Noktası: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tBiçimler:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Kaynak bilgisi alınamadı: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Kaynak #%u\n"
"\tDurum: %s\n"
"\tAd: %s\n"
"\tAçıklama: %s\n"
"\tSürücü: %s\n"
"\tÖrnekleme Tanımı: %s\n"
"\tKanal Adresleme: %s\n"
"\tModül Sahibi: %u\n"
"\tSessiz: %s\n"
"\tSes: %s\n"
"\t        denge %0.2f\n"
"\tTemel Ses: %s\n"
"\tAlıcı İzleme: %s\n"
"\tGecikme: %0.0f usec, yapılandırılmış %0.0f usec\n"
"\tBayraklar: %s%s%s%s%s%s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "yok"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Modül bilgisi alınamadı: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Modül #%u\n"
"\tAdı: %s\n"
"\tDeğişken: %s\n"
"\tKullanım Sayacı: %s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "İstemci bilgisi alınamadı: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"İstemci #%u\n"
"\tSürücü: %s\n"
"\tModül Sahibi: %s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Kart bilgisi alınamadı: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Kart #%u\n"
"\tAdı: %s\n"
"\tSürücü: %s\n"
"\tModül Sahibi: %s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tProfiller:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""
"\t\t%s: %s (alıcılar: %u, kaynaklar: %u, öncelik: %u, kullanılabilir: %s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tEtkin Profil: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (tür: %s, öncelik: %u, gecikme uzaklığı: %<PRId64> usec%s%s, %s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tÖzellikler:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\\Profil(ler)in parçası: %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Alıcı giriş bilgisi alınamadı: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Alıcı Girişi #%u\n"
"\tSürücü: %s\n"
"\tModül Sahibi: %s\n"
"\tİstemci: %s\n"
"\tAlıcı: %u\n"
"\tÖrnekleme Tanımı: %s\n"
"\tKanal Adresleme: %s\n"
"\tBiçim: %s\n"
"\tDurduruldu: %s\n"
"\tSessiz: %s\n"
"\tSes: %s\n"
"\t        denge %0.2f\n"
"\tTampon Bellek Gecikmesi: %0.0f usec\n"
"\tAlıcı Gecikmesi: %0.0f usec\n"
"\tÖrnekleme Yöntemi: %s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "Kaynak çıkış bilgileri alınamadı: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Kaynak Çıkışı #%u\n"
"\tSürücü: %s\n"
"\tModül Sahibi: %s\n"
"\tİstemci: %s\n"
"\tKaynak: %u\n"
"\tÖrnekleme Tanımı: %s\n"
"\tKanal Adresleme: %s\n"
"\tBiçim: %s\n"
"\tVeri Akışı Durduruldu: %s\n"
"\tSessiz: %s\n"
"\tSes: %s\n"
"\t        denge %0.2f\n"
"\tTampon Bellek Gecikmesi: %0.0f usec\n"
"\tKaynak Gecikmesi: %0.0f usec\n"
"\tYeniden Örnekleme yöntemi: %s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Örnekleme bilgisi alınamadı: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Örnekleme #%u\n"
"\tAd: %s\n"
"\tÖrnekleme Tanımı: %s\n"
"\tKanal Adresleme: %s\n"
"\tSes: %s\n"
"\t        denge %0.2f\n"
"\tSüre: %0.1fs\n"
"\tBoyut: %s\n"
"\tYavaş: %s\n"
"\tDosya adı: %s\n"
"\tÖzellikler:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Hata: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "Mesaj gönderme başarısız oldu: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr "list-handlers mesajı başarısız oldu: %s"

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "list-handlers mesajı yanıtı doğru şekilde ayrıştırılamadı"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "list-handlers mesajı yanıtı bir JSON dizisi değil"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr "list-handlers mesajı yanıtı dizisi ögesi %d bir JSON nesnesi değil"

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Modül kaldırılamadı: Modül %s yüklenemedi"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"Ses ayarlanamadı: %d kanal için ses ayarlamayı denediniz, ancak desteklenen "
"kanal sayısı %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Örnekleme yüklenemedi: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Dosyanın erken bitişi"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "yeni"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "değiştir"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "kaldır"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "bilinmeyen"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "alıcı"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "kaynak"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "alıcı-girişi"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "alıcı-çıkışı"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "modül"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "istemci"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "örnek-önbellek"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "sunucu"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "kart"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "%2$s #%3$u üzerinde '%1$s' olayı\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT alındı, çıkılıyor."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Geçersiz ses tanımı"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "İzin verilebilir aralık dışındaki ses.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Geçersiz ses tanımı numarası.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Tutarsız ses tanımı.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[seçenekler]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[TÜR]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "DOSYAADI [AD]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "AD [ALICI]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "AD|#A SES [SES ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#A SES [SES ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "AD|#N 1|0|geçiş"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|geçiş"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#A BİÇİMLER"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"@DEFAULT_SINK@, @DEFAULT_SOURCE@ ve @DEFAULT_MONITOR@ özel adları\n"
"öntanımlı alıcıyı, kaynağı ve ekranı belirtmek için kullanılabilir.\n"

#: src/utils/pactl.c:2664
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Yardım gösterir\n"
"      --version                         Sürüm gösterir\n"
"\n"
"  -f, --format=BİÇİM                    Çıktı biçimi. \"normal\" veya \"json"
"\" seçeneklerinden birisi\n"
"  -s, --server=SUNUCU                   Bağlantı kurulacak sunucu adı\n"
"  -n, --client-name=AD                Sunucu üzerinde bu istemci nasıl "
"çağrılacak\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Libpulse %s ile derlendi\n"
"Libpulse %s ile bağlantılı\n"

#: src/utils/pactl.c:2751
#, c-format
msgid "Invalid format value '%s'"
msgstr "Geçersiz biçim değeri '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Şunlardan birini belirtin ya da hiçbir şey belirtmeyin: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Lütfen yüklemek için bir örnekleme dosyası belirtin"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Ses dosyası açılamadı."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "Uyarı: Dosyadan örnekleme tanımı belirlenemedi."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Çalmak için örnek ad belirtmelisiniz"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Kaldırmak için örnek ad belirtmelisiniz"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "Bir alıcı giriş göstergesi ve alıcı belirtmelisiniz"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "Bir kaynak çıkışı dizini ve bir kaynak belirtmelisiniz"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Bir modül adı ve değişken belirtmelisiniz."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "Bir modül dizini ya da adı belirtmelisiniz"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Birden daha fazla alıcı belirtemezsiniz. Bir boolean değer belirtmelisiniz."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "Geçersiz bekletme tanımlaması."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Bir kaynaktan daha fazlasını belirtemezsiniz. Bir boolean değer "
"belirtmelisiniz."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Bir kart adı/dizin ve bir profil adı belirtmelisiniz"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Bir alıcı adı/dizini ve bir bağlantı noktası adı belirtmelisiniz"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "Bir alıcı adı belirtmelisiniz"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Bir kaynak adı/dizini ve bir bağlantı noktası adı belirtmelisiniz"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "Bir kaynak adı belirtmelisiniz"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "Bir alıcı adı/dizini belirtmelisiniz"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Bir alıcı adı/dizini ve bir ses seviyesi belirtmelisiniz"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "Bir kaynak adı/dizini belirtmelisiniz"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Bir kaynak adı/dizini ve bir ses belirtmelisiniz"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Bir alıcı girişi dizini ve bir ses seviyesi belirtmelisiniz"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Geçersiz alıcı girişi dizini"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "Bir kaynak çıkışı dizini ve bir ses seviyesi belirtmelisiniz"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Geçersiz kaynak çıkış dizini"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Bir alıcı adı/dizini ve bir sessizlik eylemi (0, 1 veya 'toggle') "
"belirtmelisiniz"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Geçersiz sessiz tanımı"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Bir kaynak adı/dizini ve bir sessizlik eylemi (0, 1 veya 'toggle') "
"belirtmelisiniz"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Bir alıcı girişi dizini ve bir sessizlik eylemi (0, 1 veya 'toggle') "
"belirtmelisiniz"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Geçersiz alıcı girişi dizini tanımı"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr ""
"Bir kaynak çıkış dizini ve bir sessizlik eylemi (0, 1 veya 'toggle') "
"belirtmelisiniz"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Geçersiz kaynak çıkış dizini tanımlaması"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr "En azından bir nesne yolu ve bir mesaj adı belirtmelisiniz"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"Fazladan belirtilen argümanlar yok sayılacaktır. Tüm mesaj parametrelerinin "
"tek bir dizge olarak verilmesi gerektiğini unutmayın."

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"Bir alıcı dizini ve desteklenen biçimlerin noktalı virgülle ayrılmış bir "
"listesini belirtmelisiniz"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr ""
"Bir kart adı/dizini, bir bağlantı noktası adı ve bir gecikme uzaklığı "
"belirtmelisiniz"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Gecikme uzaklığı ayrıştırılamadı"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Belirtilen geçerli komut yok."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Devam edilemedi: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Bekletilemedi: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "UYARI: Ses sunucusu yerel değil, askıya alınamıyor.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Bağlantı hatası: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT sinyali alındı, çıkılıyor.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "UYARI: Alt işlem %u sinyali ile sonlandırıldı\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [seçenekler] -- PROGRAM [ARGÜMANLAR ...]\n"
"\n"
"PROGRAM çalışırken PulseAudio'yu geçici olarak askıya al.\n"
"\n"
"  -h, --help                            Bu yardımı gösterir\n"
"      --version                         Sürümü gösterir\n"
"  -s, --server=SUNUCU                   Bağlanılacak sunucunun adı\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Libpulse %s ile derlendi\n"
"Libpulse %s ile bağlantılı\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() başarısız oldu.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() başarısız oldu.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() başarısız oldu.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D ekran] [-S sunucu] [-O alıcı] [-I kaynak] [-c dosya]  [-d|-e|-i|-r]\n"
"\n"
" -d    X11 ekranına bağlı geçerli PulseAudio verilerini gösterir "
"(öntanımlı)\n"
" -e    X11 ekranına bağlı yerel PulseAudio verilerini dışa aktarır\n"
" -i    X11 ekranından PulseAudio verilerini yerel çevresel değişkenlere ve "
"çerez dosyalarına aktarır.\n"
" -r    X11 ekranından PulseAudio verilerini kaldırır\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Komut satırı ayrıştırılamadı.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Sunucu: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Kaynak: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Alıcı: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Çerez: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Çerez veriler çözümlenemedi\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Çerez veriler kaydedilemedi\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "FQDN alınamadı.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Çerez veriler yüklenemedi\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Henüz uygulanmadı.\n"

#~ msgid "Failed to initialize daemon."
#~ msgstr "Artalan işlem başlatılamadı."

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "Ayrı Tekli Çıktılar üzerinde LFE"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Sayısal Düzgeçiş (S/PDIF)"

#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "Sayısal Düzgeçiş  (IEC958)"

#~ msgid ""
#~ "ALSA woke us up to write new data to the device, but there was actually "
#~ "nothing to write!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA aygıta yeni veri yazmamız için bizi uyardı fakat aslında yazılacak "
#~ "hiçbir şey yok!\n"
#~ "Büyük ihtimalle bu ALSA '%s' sürücüsünde bir hatadır. Lütfen bu sorunu "
#~ "ALSA geliştiricilerine bildirin.\n"
#~ "Biz POLLOUT ayarı ile anladık -- ayrıca sonraki bir snd_pcm_avail() 0 ya "
#~ "da min_avail değerinden küçük başka bir değer döndü."

#~ msgid ""
#~ "ALSA woke us up to read new data from the device, but there was actually "
#~ "nothing to read!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA aygıttan yeni veri okumamız için bizi uyardı fakat aslında okunacak "
#~ "hiçbir şey yok!\n"
#~ "Büyük ihtimalle bu bir ALSA '%s' sürücüsü hatasıdır. Lütfen bu sorunu "
#~ "ALSA geliştiricilerine bildirin.\n"
#~ "Biz POLLIN ayarı ile anladık -- ayrıca sonraki snd_pcm_avail() 0 ya da "
#~ "min_avail değerinden küçük başka bir değer döndü."

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [seçenekler]\n"
#~ "\n"
#~ "-h, --help                            Yardımı gösterir\n"
#~ "-v, --verbose                         Hata ayıklama iletilerini yazdırır\n"
#~ "      --from-rate=ÖRNEKLEMEHIZ          Hz cinsinde örnekleme hızından "
#~ "(varsayılan - 44100)\n"
#~ "      --from-format=ÖRNEKLEMEBİÇİMİ      Örnekleme türünden (varsayılan - "
#~ "s16le)\n"
#~ "      --from-channels=KANALLAR        Kanal sayılarından (varsayılan - "
#~ "1)\n"
#~ "      --to-rate=ÖRNEKLEMEHIZI            Hz cinsinde örnekleme hızına "
#~ "(varsayılan - 44100)\n"
#~ "      --to-format=ÖRNEKLEMEBİÇİMİ        Örnekleme türüne (varsayılan - "
#~ "s16le)\n"
#~ "      --to-channels=KANALLAR          Kanal sayılarına (varsayılan - 1)\n"
#~ "      --resample-method=YÖNTEM        Yöntemi yeniden örnekleme "
#~ "(varsayılan otomatik)\n"
#~ "      --seconds=SANİYE               Akış süresinden (varsayılan - 60)\n"
#~ "\n"
#~ "Eğer biçimler belirtilmezse, test bütün biçimlerin birleşiminde ileri "
#~ "geri\n"
#~ "gerçekleştirilir.\n"
#~ "\n"
#~ "Örnek tür şunlardan biri olmalıdır; s16le, s16be, u8, float32le, "
#~ "float32be, ulaw, alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "Yeniden örnekleme yöntemlerinin olası değerlerini --dump-resample-methods "
#~ "gösterir.\n"
