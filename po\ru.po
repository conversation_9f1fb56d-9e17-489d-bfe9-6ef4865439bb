# Russian translation of pulseaudio.
# Copyright (C) 2010 pulseaudio
# This file is distributed under the same license as the pulseaudio package.
#
# <PERSON><PERSON> <<EMAIL>>, 2010, 2012.
# <PERSON> <<EMAIL>>, 2014, 2019.
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2022-05-23 11:18+0000\n"
"Last-Translator: <PERSON>. <<EMAIL>>\n"
"Language-Team: Russian <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/ru/>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.12.2\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [параметры]\n"
"\n"
"КОМАНДЫ:\n"
"  -h, --help                            Показать эту справку\n"
"      --version                         Показать сведения о версии\n"
"      --dump-conf                       Создать снимок (dump) текущих "
"настроек\n"
"      --dump-modules                    Создать снимок (dump) доступных "
"модулей\n"
"      --dump-resample-methods           Создать снимок (dump) доступных "
"методов изменения частотных характеристик\n"
"      --cleanup-shm                     Очистить неиспользуемые блоки общей "
"памяти\n"
"      --start                           Запустить демон, если ещё не "
"запущен\n"
"  -k  --kill                            Убить процесс запущенного демона\n"
"      --check                           Проверить, запущен ли демон "
"(возвращает только код завершения)\n"
"\n"
"ПАРАМЕТРЫ:\n"
"      --system[=BOOL]                   Запустить в общесистемном режиме\n"
"  -D, --daemonize[=BOOL]                Запустить как демон\n"
"      --fail[=BOOL]                     Завершить работу при неудачном "
"запуске\n"
"      --high-priority[=BOOL]            Попытаться установить высший уровень "
"nice\n"
"                                        (доступно только для root, с SUID\n"
"                                        или повышенным RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Попытаться включить планировщик\n"
"                                        (доступно только для root, с SUID\n"
"                                        или повышенным RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Запретить загрузку указанного "
"пользователем\n"
"                                        модуля загрузки/выгрузки после "
"запуска\n"
"      --disallow-exit[=BOOL]            Запретить выход по запросу "
"пользователя\n"
"      --exit-idle-time=SECS             Завершить работу демона после "
"указанного\n"
"                                        времени бездействия\n"
"      --scache-idle-time=SECS           Выгрузить автоматически загруженные "
"фрагменты после\n"
"                                        указанного времени бездействия\n"
"      --log-level[=УРОВЕНЬ]             Увеличить или установить уровень "
"детализации журналирования\n"
"  -v  --verbose                         Увеличить уровень детализации\n"
"      --log-target={auto,syslog,stderr,file:ПУТЬ,newfile:ПУТЬ}\n"
"                                        Указать цели журнала\n"
"      --log-meta[=BOOL]                 Записывать в журнал позиции в "
"исходном коде\n"
"      --log-time[=BOOL]                 Записывать в журнал отметки времени\n"
"      --log-backtrace=FRAMES            Записывать в журнал обратную "
"трассировку\n"
"  -p, --dl-search-path=ПУТЬ             Задать путь для поиска динамических "
"разделяемых\n"
"                                        объектов (расширений)\n"
"      --resample-method=МЕТОД           Использовать указанный метод "
"изменения частотных характеристик\n"
"                                        («--dump-resample-methods» для "
"просмотра\n"
"                                        списка возможных значений)\n"
"      --use-pid-file[=BOOL]             Создать файл с идентификатором "
"процесса (PID)\n"
"      --no-cpu-limit[=BOOL]             Не устанавливать ограничитель "
"загрузки процессора\n"
"                                        на платформах, на которых он "
"поддерживается.\n"
"      --disable-shm[=BOOL]              Отключить поддержку общей памяти.\n"
"      --enable-memfd[=BOOL]             Включить поддержку общей памяти "
"memfd.\n"
"\n"
"СЦЕНАРИИ ЗАПУСКА:\n"
"  -L, --load=\"МОДУЛЬ АРГУМЕНТЫ\"         Загрузить указанный модуль с\n"
"                                        указанными аргументами\n"
"  -F, --file=ИМЯФАЙЛА                   Запустить указанный сценарий\n"
"  -C                                    Открыть командную строку на "
"работающем TTY\n"
"                                        после запуска\n"
"\n"
"  -n                                    Не загружать файл сценария, "
"используемый по умолчанию\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "«--daemonize» ожидает логический аргумент"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "«--fail» ожидает логический аргумент"

#: src/daemon/cmdline.c:265
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"«--log-level» ожидает уровень журналирования — либо число в диапазоне от 0 "
"до 4, либо одно из слов «debug», «info», «notice», «warn» или «error»."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "«--high-priority» ожидает логический аргумент"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "«--realtime» ожидает логический аргумент"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "«--disallow-module-loading» ожидает логический аргумент"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "«--disallow-exit» ожидает логический аргумент"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "«--use-pid-file» ожидает логический аргумент"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Недопустимый журнал: используйте «syslog», «journal», «stderr», «auto» или "
"файл — «file:<путь>», «newfile:<путь>»."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Недопустимый журнал: используйте «syslog», «stderr», «auto» или файл — «file:"
"<путь>», «newfile:<путь>»."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "«--log-time» ожидает логический аргумент"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "«--log-meta» ожидает логический аргумент"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Недопустимый метод передискретизации «%s»."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "«--system» ожидает логический аргумент"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "«--no-cpu-limit» ожидает логический аргумент"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "«--disable-shm» ожидает логический аргумент"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "«--enable-memfd» ожидает логический аргумент"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Недопустимое назначение журнала «%s»."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Недопустимый уровень журналирования «%s»."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Недопустимый метод передискретизации «%s»."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr ""
"[%s:%u] Недопустимое значение ограничения на используемые ресурсы (rlimit) "
"«%s»."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Недопустимый формат отсчётов «%s»."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Недопустимая частота дискретизации «%s»."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Недопустимые каналы сэмпла «%s»."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Недопустимая схема каналов «%s»."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Недопустимое число фрагментов «%s»."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Недопустимый размер фрагмента «%s»."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Недопустимый приоритет (nice) «%s»."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Недопустимый тип сервера «%s»."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Не удалось открыть файл конфигурации: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"В указанной схеме каналов по умолчанию число каналов отличается от числа "
"каналов по умолчанию."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Прочитано из файла конфигурации: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Имя: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Нет информации о модуле\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Версия: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Описание: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Автор: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Использование: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Загружать только один раз: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "Предупреждение об устаревшем модуле: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Путь: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "Не удалось открыть модуль %s: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Не удалось найти исходный загрузчик lt_dlopen."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr ""
"Не удалось выделить память для нового загрузчика разделяемых библиотек."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Не удалось добавить новый загрузчик bind-now."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Не удалось найти пользователя «%s»."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Не удалось найти группу пользователей «%s»."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "Идентификаторы групп пользователя «%s» и группы «%s» не совпадают."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr ""
"Домашний каталог пользователя «%s» не совпадает с «%s», проигнорировано."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Не удалось создать «%s»: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Не удалось изменить список групп: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Не удалось изменить идентификатор группы (GID): %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Не удалось изменить идентификатор пользователя (UID): %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "Общесистемный режим не поддерживается на этой платформе."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Ошибка разбора командной строки."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Общесистемный режим невозможно использовать без привилегий администратора. "
"Будет запущена только служба обнаружения сервера D-Bus."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Не удалось завершить работу демона: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Эта программа не предназначена для запуска с привилегиями администратора "
"(кроме случая, когда указан ключ «--system»)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Необходимы привилегии администратора."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "«--start» не поддерживается для общесистемного режима."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr "Обнаружен настроенный вручную сервер на %s, отказ от запуска."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Обнаружен настроенный вручную сервер на %s, который работает на этом "
"компьютере. Попытка запуска будет продолжена."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "Запущен в общесистемном режиме, но «--disallow-exit» не задан."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"Запущен в общесистемном режиме, но «--disallow-module-loading» не задан."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr ""
"Запущен в общесистемном режиме, поэтому режим SHM принудительно отключён."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"Запущен в общесистемном режиме, поэтому автоматическое завершение при долгом "
"простое отключено."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Не удалось начать критическую секцию работы с вводом-выводом."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "Произошла ошибка при выполнении pipe(): %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "Произошла ошибка при выполнении fork(): %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "Произошла ошибка при выполнении read(): %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Не удалось запустить демон."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "Произошла ошибка при выполнении setsid(): %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Не удалось получить идентификатор компьютера"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"Вы запустили PulseAudio в общесистемном режиме. Убедитесь, что это именно "
"то, что вы хотели сделать.\n"
"Пожалуйста, прочитайте http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ для понимания, почему "
"общесистемный режим обычно является плохой практикой."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "Произошла ошибка при выполнении pa_pid_file_create()."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "Произошла ошибка при выполнении pa_core_new()."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "аргументы командной строки"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"Не удалось инициализировать сервис из-за ошибок при выполнении команд "
"запуска. Источник команд: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "Запуск демона без каких-либо загружаемых модулей, отказ от работы."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "Звуковая система PulseAudio"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Запуск звуковой системы PulseAudio"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Вход"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Вход док-станции"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Микрофон док-станции"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "Линейный вход док-станции"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "Линейный вход"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Микрофон"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Фронтальный микрофон"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Тыловой микрофон"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Внешний микрофон"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Встроенный микрофон"

# BUGME: please clarify, is this FM Radio or some digital radio frequency channel? --aspotashev
#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Радио"

# BUGME: please clarify? --aspotashev
#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Видео"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Автоматическая регулировка усиления"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Нет автоматической регулировки усиления"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Усиление"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Нет усиления"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Усилитель"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Нет усилителя"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Усиление басов"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Нет усиления басов"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Динамик"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Наушники"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Аналоговый вход"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Микрофон док-станции"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Микрофон гарнитуры"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Аналоговый выход"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "Вторые наушники"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "Выход на наушники моно"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "Линейный выход"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Аналоговый выход моно"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Динамики"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Цифровой выход (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Цифровой вход (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "Многоканальный вход"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "Многоканальный выход"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "Игровой выход"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "Разговорный выход"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "Разговорный вход"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "Виртуальный объёмный звук 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Аналоговый моно"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "Аналоговый моно (Левый)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "Аналоговый моно (Правый)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Аналоговый стерео"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Моно"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Стерео"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Гарнитура"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "Динамик"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Многоканальный"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Аналоговый объёмный 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Аналоговый объёмный 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Аналоговый объёмный 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Аналоговый объёмный 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Аналоговый объёмный 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Аналоговый объёмный 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Аналоговый объёмный 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Аналоговый объёмный 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Аналоговый объёмный 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Аналоговый объёмный 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Аналоговый объёмный 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Цифровой стерео (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Цифровой объёмный 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Цифровой объёмный 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Цифровой объёмный 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Цифровой стерео (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Цифровой объёмный 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "Чат"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "Игра"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Аналоговый моно дуплекс"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Аналоговый стерео дуплекс"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Цифровой стерео дуплекс (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "Многоканальный дуплекс"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "Стерео дуплекс"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "Моно разговор + 7.1 окружающий звук"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Выключено"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "%s выход"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "%s вход"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA сообщила о возможности записи новых данных в устройство, но на самом "
"деле писать было нечего.\n"
"Скорее всего, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA.\n"
"Процесс разбужен с установленным POLLOUT, однако последующий вызов "
"snd_pcm_avail() вернул 0 или другое значение, меньшее чем min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA сообщила о возможности чтения новых данных из устройства, но на самом "
"деле читать было нечего.\n"
"Скорее всего, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA.\n"
"Процесс разбужен с установленным POLLIN, однако последующий вызов "
"snd_pcm_avail() вернул 0 или другое значение, меньшее чем min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() возвращает значение, которое является исключительно большим: "
"%lu байт (%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."
msgstr[1] ""
"snd_pcm_avail() возвращает значение, которое является исключительно большим: "
"%lu байта (%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."
msgstr[2] ""
"snd_pcm_avail() возвращает значение, которое является исключительно большим: "
"%lu байт (%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() возвращает значение, которое является исключительно большим: "
"%li байт (%s%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."
msgstr[1] ""
"snd_pcm_delay() возвращает значение, которое является исключительно большим: "
"%li байта (%s%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."
msgstr[2] ""
"snd_pcm_delay() возвращает значение, которое является исключительно большим: "
"%li байт (%s%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() возвращает странное значение: задержка %lu меньше "
"доступных %lu.\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() возвращает значение, которое является исключительно "
"большим: %lu байт (%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."
msgstr[1] ""
"snd_pcm_mmap_begin() возвращает значение, которое является исключительно "
"большим: %lu байта (%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."
msgstr[2] ""
"snd_pcm_mmap_begin() возвращает значение, которое является исключительно "
"большим: %lu байт (%lu мс).\n"
"Вероятно, это ошибка в драйвере ALSA «%s». Пожалуйста, сообщите об этой "
"проблеме разработчикам ALSA."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Вход Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Выход Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Хендс-фри"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Наушник"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Портативный динамик"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Автомобильный динамик"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "Hi-Fi"

# BUGME: please clarify, does this mean a cell phone? --aspotashev
#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Телефон"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "Воспроизведение высокого качества (приёмник A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "Запись высокого качества (передатчик A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "Гарнитура (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "Адаптер аудиогарнитуры (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "Гарнитура (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Адаптер аудиогарнитуры (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<имя источника> source_properties=<свойства источника> "
"source_master=<имя источника для фильтрации> sink_name=<имя аудиоприёмника> "
"sink_properties=<свойства аудиоприёмника> sink_master=<имя аудиоприёмника "
"для фильтрации> adjust_time=<частота выравнивания в секундах> "
"adjust_threshold=<пороговая величина дрейфа в миллисекундах, после которой "
"нужно проводить выравнивание> format=<формат отсчётов> rate=<частота "
"дискретизации> channels=<число каналов> channel_map=<схема каналов> "
"aec_method=<используемая реализация> aec_args=<параметры для алгоритмов "
"эхоподавления> save_aec=<сохранять данные эхоподавления в /tmp> "
"autoloaded=<нужно установить, если этот модуль загружен автоматически> "
"use_volume_sharing=<использовать общий уровень громкости (yes или no)> "
"use_master_format=<yes или no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "Включено"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Фиктивный выход"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr ""
"Всегда оставлять хотя бы один аудиоприёмник загруженным, даже если он пустой"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr ""
"Всегда оставлять хотя бы один источник загруженным, даже если он пустой"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "Эквалайзер общего назначения"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<имя аудиоприёмника> sink_properties=<свойства аудиоприёмника> "
"sink_master=<аудиоприёмника, к которому нужно подключиться> format=<формат "
"отсчётов> rate=<частота дискретизации> channels=<число каналов> "
"channel_map=<схема каналов> autoloaded=<нужно установить, если этот модуль "
"загружен автоматически> use_volume_sharing=<использовать общий уровень "
"громкости (yes или no)> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "Эквалайзер на основе БПФ на %s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<нужно ли автоматически выгружать неиспользуемые фильтры>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Виртуальный аудиоприёмник LADSPA"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<имя аудиоприёмника> sink_properties=<свойства аудиоприёмника> "
"sink_input_properties=<свойства входа аудиоприёмника> master=<имя "
"аудиоприёмника для фильтрации> sink_master=<имя аудиоприёмника для "
"фильтрации> format=<формат отсчётов> rate=<частота дискретизации> "
"channels=<число каналов> channel_map=<схема каналов> plugin=<имя расширения "
"LADSPA> label=<метка расширения LADSPA> control=<разделенный запятыми список "
"управляющих значений> input_ladspaport_map=<разделенный запятыми список имён "
"входных портов LADSPA> output_ladspaport_map=<разделенный запятыми список "
"имён выходных портов LADSPA> autoloaded=<установлено, если этот модуль "
"загружается автоматически> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Синхронный пустой аудиоприёмник"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Пустой выход"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Не удалось задать формат: недопустимый формат «%s»"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Устройства вывода"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Устройства ввода"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "Аудио на @HOSTNAME@"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "Туннель для %s@%s"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "Туннель к %s/%s"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Виртуальный аудиоприёмник объёмного звука"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<имя приёмника> sink_properties=<свойства приёмника> master=<имя "
"приёмника для фильтрации> sink_master=<имя приёмника для фильтрации> "
"format=<формат отсчётов> rate=<частота дискретизации> channels=<число "
"каналов> channel_map=<схема каналов> use_volume_sharing=<использовать общий "
"уровень (yes или no)> force_flat_volume=<yes или no> hrir=/path/to/left_hrir."
"wav hrir_left=/path/to/left_hrir.wav hrir_right=/path/to/optional/right_hrir."
"wav autoloaded=<установлено, если этот модуль загружается автоматически> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "Неизвестная модель устройства"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "Стандартный профиль RAOP"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "Аудиосервер PulseAudio"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Центральный фронтальный"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Левый фронтальный"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Правый фронтальный"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Центральный тыловой"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Левый тыловой"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Правый тыловой"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Сабвуфер"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Фронтальный левее центра"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Фронтальный правее центра"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Левый боковой"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Правый боковой"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Вспомогательный 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Вспомогательный 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Вспомогательный 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Вспомогательный 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Вспомогательный 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Вспомогательный 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Вспомогательный 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Вспомогательный 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Вспомогательный 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Вспомогательный 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Вспомогательный 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Вспомогательный 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Вспомогательный 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Вспомогательный 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Вспомогательный 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Вспомогательный 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Вспомогательный 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Вспомогательный 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Вспомогательный 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Вспомогательный 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Вспомогательный 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Вспомогательный 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Вспомогательный 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Вспомогательный 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Вспомогательный 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Вспомогательный 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Вспомогательный 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Вспомогательный 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Вспомогательный 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Вспомогательный 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Вспомогательный 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Вспомогательный 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Верхний центральный"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Верхний центральный фронтальный"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Верхний левый фронтальный"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Верхний правый фронтальный"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Верхний центральный тыловой"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Верхний левый тыловой"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Верхний правый тыловой"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(недействительно)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Объёмный 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Объёмный 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Объёмный 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Объёмный 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Объёмный 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "Произошла ошибка при выполнении xcb_connect()"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "Вызов xcb_connection_has_error() вернул «true»"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Не удалось разобрать данные cookie"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Получено сообщение для неизвестного расширения «%s»"

#: src/pulse/direction.c:37
msgid "input"
msgstr "вход"

#: src/pulse/direction.c:39
msgid "output"
msgstr "выход"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "двунаправленный"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "некорректный"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) принадлежит не данному пользователю (uid %d), а "
"пользователю с uid %d. (Это может происходить, например, в случае "
"подключения от имени администратора к серверу PulseAudio, запущенному от "
"имени обычного пользователя, по родному протоколу. Не делайте так.)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "да"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "нет"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr ""
"Не удалось получить доступ к блокировке, используемой для запуска сервера по "
"требованию."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Не удалось открыть файл журнала «%s»."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Не удалось открыть ни один из файлов журналов «%s», «%s.1», «%s.2», ..., «%s."
"%d»."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Недопустимый журнал."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Встроенное аудио"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Модем"

#: src/pulse/error.c:38
msgid "OK"
msgstr "ОК"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Доступ запрещён"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Неизвестная команда"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Недопустимый параметр"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "Объект существует"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Нет такого объекта"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Соединение отвергнуто"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Ошибка протокола"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Время ожидания истекло"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Нет ключа аутентификации"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Внутренняя ошибка"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Соединение завершено"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Объект уничтожен"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Недопустимый сервер"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Не удалось инициализировать модуль"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Некорректное состояние"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Нет данных"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Несовместимая версия протокола"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Слишком большое значение параметра"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Не поддерживается"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Неизвестный код ошибки"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Нет такого расширения"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Устаревшая функциональность"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Отсутствует реализация"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Клиент посылает запросы после вызова fork()"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Ошибка ввода/вывода"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Устройство или ресурс занято"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %u-канальный %u Гц"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f ГиБ"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f МиБ"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f КиБ"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u Б"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Не удалось передать остатки данных в потоке: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Переданы остатки данных в потоке воспроизведения."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Закрытие соединения с сервером."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_begin_write(): %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_peek(): %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Поток успешно создан."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_get_buffer_attr(): %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Показатели буфера: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Показатели буфера: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "Использование отсчётов «%s», схемы каналов «%s»."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr ""
"Установлено соединение с устройством %s (номер: %u, приостановлено: %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Ошибка потока: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Поток приостановлен.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Поток возобновлён.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Недостаток данных на входе потока.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Переполнение на выходе потока.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Поток запущен.%s"

# BUGME: word puzzle with "not". --aspotashev
# https://gitlab.freedesktop.org/pulseaudio/pulseaudio/issues/643
#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Поток перемещён на устройство %s (%u, %sприостановлено).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "не "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Атрибуты буфера потока изменены.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Стек запросов по закупорке пуст: закупоривание потока"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Стек запросов по закупорке пуст: откупоривание потока"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""
"Предупреждение: получено больше запросов на возобновление передачи данных в "
"потоке, чем запросов на приостановку потока."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Соединение установлено.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_new(): %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_connect_playback(): %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Не удалось установить мониторный поток: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_connect_record(): %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Ошибка подключения: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "Достигнут конец файла."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_write(): %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "Произошла ошибка при выполнении write(): %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Получен сигнал, выход."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Не удалось получить задержку: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Время: %0.3f с; задержка: %0.0f мкс."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "Произошла ошибка при выполнении pa_stream_update_timing_info(): %s"

#: src/utils/pacat.c:676
#, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [параметры]\n"
"%s\n"
"\n"
"  -h, --help                            Показать эту справку\n"
"      --version                         Показать сведения о версии\n"
"\n"
"  -r, --record                          Создать соединение для записи\n"
"  -p, --playback                        Создать соединение для "
"воспроизведения\n"
"\n"
"  -v, --verbose                         Включить подробные операции\n"
"\n"
"  -s, --server=СЕРВЕР                   Имя сервера для подключения\n"
"  -d, --device=УСТРОЙСТВО               Имя приёмника/источника для "
"подключения. Могут использоваться специальные имена @DEFAULT_SINK@, "
"@DEFAULT_SOURCE@ и @DEFAULT_MONITOR@ для указания приёмника, источника и "
"монитора соответственно.\n"
"  -n, --client-name=ИМЯ                 Имя этого клиента на сервере\n"
"      --stream-name=ИМЯ                 Имя этого потока на сервере\n"
"      --volume=VOLUME                   Указать начальную (линейную) "
"громкость в диапазоне 0...65536\n"
"      --rate=SAMPLERATE                 Частота дискретизации в Гц (по "
"умолчанию 44100)\n"
"      --format=SAMPLEFORMAT             Формат выборки, смотрите\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        возможные значения (по умолчанию "
"s16ne)\n"
"      --channels=КАНАЛЫ                 Количество каналов, 1 для моно, 2 "
"для стерео\n"
"                                        (по умолчанию 2)\n"
"      --channel-map=CHANNELMAP          Карта каналов для использования "
"вместо установленной по умолчанию.\n"
"      --fix-format                      Взять образец формата из приёмника/"
"источника, к которому\n"
"                                        подключен поток.\n"
"      --fix-rate                        Взять частоту дискретизации из "
"приёмника/источника, к которому\n"
"                                        подключен поток.\n"
"      --fix-channels                    Взять количество каналов и карту "
"каналов.\n"
"                                        из приёмника/источника, к которому "
"подключен поток.\n"
"      --no-remix                        Не менять число каналов.\n"
"      --no-remap                        Сопоставлять каналы по индексу, а не "
"по имени.\n"
"      --latency=BYTES                   Запросить указанную задержку в "
"байтах.\n"
"      --process-time=BYTES              Запросить указанное время процесса "
"на запрос в байтах.\n"
"      --latency-msec=MSEC               Запросить указанную задержку в "
"мсек.\n"
"      --process-time-msec=MSEC          Запросить указанное время процесса "
"на запрос в мсек.\n"
"      --property=PROPERTY=VALUE         Установить для указанного свойства "
"указанное значение.\n"
"      --raw                             Запись/воспроизведение "
"необработанных данных PCM.\n"
"      --passthrough                     Пропускать данные.\n"
"      --file-format[=FFORMAT]           Запись/воспроизведение "
"форматированных данных PCM.\n"
"      --list-file-formats               Список доступных форматов файлов.\n"
"      --monitor-stream=INDEX            Запись с входа приёмника с индексом "
"INDEX.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""
"Воспроизвести закодированные аудиофайлы через звуковой сервер PulseAudio."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""
"Получать аудиоданные от звукового сервера PulseAudio и писать их в файл."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"Получать аудиоданные от звукового сервера PulseAudio и писать их на "
"стандартный вывод (stdout) или в указанный файл."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"Воспроизвести аудиоданные из стандартного ввода (stdin) или из указанного "
"файла через звуковой сервер PulseAudio."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Скомпилировано с libpulse %s\n"
"Скомпоновано с libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Недопустимое имя клиента «%s»"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Недопустимое имя потока «%s»"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Недопустимая схема каналов «%s»"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Недопустимое значение задержки «%s»"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Недопустимое время процесса «%s»"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Недопустимое свойство «%s»"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Неизвестный формат файла «%s»."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "Не удалось разобрать аргумент ключа «--monitor-stream»"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Недопустимая спецификация отсчётов"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Слишком много аргументов."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Не удалось создать спецификацию отсчётов для файла."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Не удалось открыть аудиофайл."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Предупреждение: указанная спецификация отсчётов будет заменена спецификацией "
"из файла."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Не удалось определить спецификацию отсчётов из файла."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "Предупреждение: не удалось определить схему каналов из файла."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "Схема каналов не соответствует спецификации отсчётов"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Предупреждение: не удалось записать схему каналов в файл."

# %s = "recording" or "playback" --aspotashev
#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Открытие потока %s со спецификацией отсчётов «%s» и схемой каналов «%s»."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "записи"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "воспроизведения"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Не удалось установить имя потока."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "Произошла ошибка при выполнении pa_mainloop_new()."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "Произошла ошибка при выполнении io_new()."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "Произошла ошибка при выполнении pa_context_new()."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "Произошла ошибка при выполнении pa_context_connect(): %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "Произошла ошибка при выполнении pa_context_rttime_new()."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "Произошла ошибка при выполнении pa_mainloop_run()."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "ИМЯ [АРГУМЕНТЫ ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "ИМЯ|№"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "ИМЯ"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "ИМЯ|№ ГРОМКОСТЬ"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "№ ГРОМКОСТЬ"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "ИМЯ|№ 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "№ 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "ИМЯ|№ СВОЙСТВО=ЗНАЧЕНИЕ"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "№ СВОЙСТВО=ЗНАЧЕНИЕ"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "№"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "ИМЯ АУДИОПРИЁМНИК|№"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "ИМЯ ИМЯ_ФАЙЛА"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "ПУТЬ"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "ИМЯ_ФАЙЛА АУДИОПРИЁМНИК|№"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "№ АУДИОПРИЁМНИК|ИСТОЧНИК"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "ПЛАТА ПРОФИЛЬ"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "ИМЯ|№ ПОРТ"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "ИМЯ_ПЛАТЫ|№_ПЛАТЫ ПОРТ ЗАДЕРЖКА"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "КУДА"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "ЧИСЛОВОЙ-УРОВЕНЬ"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "КАДРОВ"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "СООБЩЕНИЕ ПОЛУЧАТЕЛЯ [ПАРАМЕТРЫ_СООБЩЕНИЯ]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Показать эту справку\n"
"      --version                         Показать сведения о версии\n"
"Если команда для выполнения не указана, pacmd будет запущен в интерактивном "
"режиме.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"Скомпилировано с libpulse %s\n"
"Скомпоновано с libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"Нет запущенного демона PulseAudio, либо он не запущен в качестве сеансового "
"демона."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Не удалось завершить работу демона PulseAudio."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Демон не отвечает."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Не удалось получить статистику: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Сейчас используется: %u блок, содержащий в совокупности %s байт.\n"
msgstr[1] "Сейчас используется: %u блока, содержащих в совокупности %s байт.\n"
msgstr[2] ""
"Сейчас используется: %u блоков, содержащих в совокупности %s байт.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] ""
"Выделено за всё время: %u блок, содержащий в совокупности %s байт.\n"
msgstr[1] ""
"Выделено за всё время: %u блока, содержащих в совокупности %s байт.\n"
msgstr[2] ""
"Выделено за всё время: %u блоков, содержащих в совокупности %s байт.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Размер кэша сэмплов: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Не удалось получить информацию о сервере: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr "%s\n"

# Tile Size = PA_MEMPOOL_SLOT_SIZE in src/pulsecore/memblock.c. --aspotashev
#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"Строка сервера: %s\n"
"Версия протокола библиотеки: %u\n"
"Версия протокола сервера: %u\n"
"Выполняется локально: %s\n"
"Номер клиента: %u\n"
"Размер блока памяти: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Имя пользователя: %s\n"
"Имя хоста: %s\n"
"Имя сервера: %s\n"
"Версия сервера: %s\n"
"Спецификация отсчётов по умолчанию: %s\n"
"Схема каналов по умолчанию: %s\n"
"Аудиоприёмник по умолчанию: %s\n"
"Источник по умолчанию: %s\n"
"Cookie: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "доступность неясна"

#: src/utils/pactl.c:321
msgid "available"
msgstr "доступен"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "недоступен"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "Неизвестный"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Aux"

#: src/utils/pactl.c:335
msgid "Line"
msgstr "Линейный вход/выход"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Микрофон"

#: src/utils/pactl.c:338
msgid "Handset"
msgstr "Гарнитура"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "Наушник"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "SPDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "ТВ"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "Bluetooth"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "Сеть"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "Аналоговый"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Не удалось получить информацию об аудиоприёмнике: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Аудиоприёмник №%u\n"
"\tСостояние: %s\n"
"\tИмя: %s\n"
"\tОписание: %s\n"
"\tДрайвер: %s\n"
"\tСпецификация отсчётов: %s\n"
"\tСхема каналов: %s\n"
"\tМодуль-владелец: %u\n"
"\tЗвук выключен: %s\n"
"\tГромкость: %s\n"
"\t        баланс %0.2f\n"
"\tБазовая громкость: %s\n"
"\tМониторный источник: %s\n"
"\tЗадержка: %0.0f мкс, настроено на %0.0f мкс\n"
"\tФлаги: %s%s%s%s%s%s%s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tПорты:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (тип: %s, приоритет: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ", группа доступности: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tАктивный порт: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tФорматы:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Не удалось получить информацию об источнике: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Источник №%u\n"
"\tСостояние: %s\n"
"\tИмя: %s\n"
"\tОписание: %s\n"
"\tДрайвер: %s\n"
"\tСпецификация отсчётов: %s\n"
"\tСхема каналов: %s\n"
"\tМодуль-владелец: %u\n"
"\tЗвук выключен: %s\n"
"\tГромкость: %s\n"
"\t        баланс %0.2f\n"
"\tБазовая громкость: %s\n"
"\tЯвляется монитором аудиоприёмника: %s\n"
"\tЗадержка: %0.0f мкс, настроено на %0.0f мкс\n"
"\tФлаги: %s%s%s%s%s%s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "н/д"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Не удалось получить информацию о модуле: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Модуль №%u\n"
"\tИмя: %s\n"
"\tАргумент: %s\n"
"\tСчётчик использования: %s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Не удалось получить информацию о клиенте: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Клиент №%u\n"
"\tДрайвер: %s\n"
"\tМодуль-владелец: %s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Не удалось получить информацию о звуковой плате: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Звуковая плата №%u\n"
"\tИмя: %s\n"
"\tДрайвер: %s\n"
"\tМодуль-владелец: %s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tПрофили:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""
"\t\t%s: %s (аудиоприёмников: %u, источников: %u, приоритет: %u, доступен: "
"%s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tАктивный профиль: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (тип: %s, приоритет: %u, смещение задержки: %<PRId64> мкс%s%s, "
"%s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tСвойства:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tВходит в профиль(и): %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Не удалось получить информацию о входе аудиоприёмника: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Вход аудиоприёмника №%u\n"
"\tДрайвер: %s\n"
"\tМодуль-владелец: %s\n"
"\tКлиент: %s\n"
"\tАудиоприёмник: %u\n"
"\tСпецификация отсчётов: %s\n"
"\tСхема каналов: %s\n"
"\tФормат: %s\n"
"\tПоток данных приостановлен: %s\n"
"\tЗвук выключен: %s\n"
"\tГромкость: %s\n"
"\t        баланс %0.2f\n"
"\tЗадержка буфера: %0.0f мкс\n"
"\tЗадержка аудиоприёмника: %0.0f мкс\n"
"\tМетод передискретизации: %s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "Не удалось получить информацию о выходе источника: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Выход источника №%u\n"
"\tДрайвер: %s\n"
"\tМодуль-владелец: %s\n"
"\tКлиент: %s\n"
"\tИсточник: %u\n"
"\tСпецификация отсчётов: %s\n"
"\tСхема каналов: %s\n"
"\tФормат: %s\n"
"\tПоток данных приостановлен: %s\n"
"\tЗвук выключен: %s\n"
"\tГромкость: %s\n"
"\t        баланс %0.2f\n"
"\tЗадержка буфера: %0.0f мкс\n"
"\tЗадержка источника: %0.0f мкс\n"
"\tМетод передискретизации: %s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Не удалось получить информацию о сэмплах: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Сэмпл №%u\n"
"\tИмя: %s\n"
"\tСпецификация отсчётов: %s\n"
"\tСхема каналов: %s\n"
"\tГромкость: %s\n"
"\t        баланс %0.2f\n"
"\tДлительность: %0.1f с\n"
"\tРазмер: %s\n"
"\tОтложенная загрузка: %s\n"
"\tИмя файла: %s\n"
"\tСвойства:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Произошла ошибка: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "Ошибка при отправлении сообщения: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr "ошибка обработки сообщения list-handlers: %s"

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "не удалось правильно разобрать ответ list-handlers"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "ответ list-handlers не является массивом JSON"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""
"элемент массива ответов на сообщения list-handlers %d не является объектом "
"JSON"

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Не удалось выгрузить модуль: модуль «%s» не загружен"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"Не удалось задать громкость: вы попытались задать громкость для %d канала, "
"но число поддерживаемых каналов не совпадает и равно %d\n"
msgstr[1] ""
"Не удалось задать громкость: вы попытались задать громкость для %d каналов, "
"но число поддерживаемых каналов не совпадает и равно %d\n"
msgstr[2] ""
"Не удалось задать громкость: вы попытались задать громкость для %d каналов, "
"но число поддерживаемых каналов не совпадает и равно %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Не удалось загрузить сэмпл в кэш: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Неожиданный конец файла"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "появление"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "изменение"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "удаление"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "(неизвестно)"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2161
msgid "sink"
msgstr "аудиоприёмника"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2164
msgid "source"
msgstr "источника"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "входа аудиоприёмника"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "выхода источника"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2173
msgid "module"
msgstr "модуля"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2176
msgid "client"
msgstr "клиента"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "кэшированного сэмпла"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2182
msgid "server"
msgstr "сервера"

# [event-facility] --aspotashev
#: src/utils/pactl.c:2185
msgid "card"
msgstr "платы"

# "Событие [event-type] в отношении [event-facility] #N", поэтому все строки [event-facility] выше пишем в родительном падеже. --aspotashev
#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "Событие «%s» в отношении %s №%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "Получен сигнал для остановки (SIGINT), выход."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Недопустимое значение громкости"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "Указанная громкость выходит за границы разрешённого диапазона.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Недопустимое количество значений громкости.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Несогласованные способы указания значений громкости.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[параметры]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[ТИП]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "ИМЯ_ФАЙЛА [ИМЯ]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "ИМЯ [АУДИОПРИЁМНИК]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "ИМЯ|№ ГРОМКОСТЬ [ГРОМКОСТЬ ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "№ ГРОМКОСТЬ [ГРОМКОСТЬ ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "ИМЯ|№ 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "№ 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "№ ФОРМАТЫ"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"Специальные имена @DEFAULT_SINK@, @DEFAULT_SOURCE@ и @DEFAULT_MONITOR@\n"
"можно использовать для указания аудиоприёмника, источника и монитора, "
"используемых по умолчанию.\n"

#: src/utils/pactl.c:2664
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Показать эту справку\n"
"      --version                         Показать сведения о версии\n"
"\n"
"  -f, --format=ФОРМАТ                   Формат вывода. Варианты: «normal» "
"или «json»\n"
"  -s, --server=СЕРВЕР                   Имя сервера для подключения\n"
"  -n, --client-name=ИМЯ                 Имя этого клиента на сервере\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Скомпилировано с libpulse %s\n"
"Скомпоновано с libpulse %s\n"

#: src/utils/pactl.c:2751
#, c-format
msgid "Invalid format value '%s'"
msgstr "Недопустимое значение формата «%s»"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Не указывайте ничего либо укажите одно из: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Необходимо указать файл, из которого будет загружен сэмпл"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Не удалось открыть аудиофайл."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "Предупреждение: не удалось определить спецификацию отсчётов из файла."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Необходимо указать имя сэмпла для воспроизведения"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Необходимо указать имя сэмпла для удаления"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "Необходимо указать номер входа аудиоприёмника и аудиоприёмник"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "Необходимо указать номер выхода источника и источник"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Необходимо указать имя модуля и аргументы."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "Необходимо указать номер или имя модуля"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Нельзя указывать больше одного аудиоприёмника. Необходимо указать логическое "
"значение."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr ""
"Недопустимое значение операции приостановки, ожидалось логическое значение."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Нельзя указывать больше одного источника. Необходимо указать логическое "
"значение."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Необходимо указать имя или номер звуковой платы и имя профиля"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Необходимо указать имя или номер аудиоприёмника и имя порта"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "Необходимо указать имя аудиоприёмника"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Необходимо указать имя или номер источника и имя порта"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "Необходимо указать имя источника"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "Необходимо указать имя или номер аудиоприёмника"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Необходимо указать имя или номер аудиоприёмника и громкость"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "Необходимо указать имя или номер источника"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Необходимо указать имя или номер источника и громкость"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Необходимо указать номер входа аудиоприёмника и громкость"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Недопустимый номер входа аудиоприёмника"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "Необходимо указать номер выхода источника и громкость"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Недопустимый номер выхода источника"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Необходимо указать имя или номер аудиоприёмника и логическое значение "
"выключения звука (0, 1 или «toggle»)"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Недопустимое логическое значение выключения звука"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Необходимо указать имя или номер источника и логическое значение выключения "
"звука (0, 1 или «toggle»)"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Необходимо указать номер входа аудиоприёмника и логическое значение "
"выключения звука (0, 1 или «toggle»)"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Недопустимый номер входа аудиоприёмника"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr ""
"Необходимо указать номер выхода источника и логическое значение выключения "
"звука (0, 1 или «toggle»)"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Недопустимый номер выхода источника"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr "Вы должны указать как минимум путь к объекту и имя сообщения"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"Даны лишние аргументы, они будут проигнорированы. Обратите внимание, что все "
"параметры сообщения должны быть предоставлены в виде одной строки."

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"Необходимо указать номер аудиоприёмника и разделённый запятыми список "
"поддерживаемых форматов"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "Необходимо указать имя или номер звуковой платы, имя порта и задержку"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Недопустимое значение задержки"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Имя команды не указано или не распознано."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Не удалось возобновить: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Не удалось приостановить: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr ""
"Предупреждение: аудиосервер не является локальным, не приостанавливается.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Ошибка соединения: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "Получен сигнал для остановки (SIGINT), выход.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "Предупреждение: дочерний процесс завершён по сигналу %u\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [параметры] -- ПРОГРАММА [АРГУМЕНТЫ ...]\n"
"\n"
"Временно приостановить работу PulseAudio, пока работает ПРОГРАММА.\n"
"\n"
"  -h, --help                            Показать эту справку\n"
"      --version                         Показать сведения о версии\n"
"  -s, --server=СЕРВЕР                   Имя сервера для подключения\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Скомпилировано с libpulse %s\n"
"Скомпоновано с libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "Произошла ошибка при выполнении pa_mainloop_new().\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "Произошла ошибка при выполнении pa_context_new().\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "Произошла ошибка при выполнении pa_mainloop_run().\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D дисплей] [-S сервер] [-O аудиоприёмник] [-I источник] [-c файл]  [-d|-"
"e|-i|-r]\n"
"\n"
" -d    Показать текущие данные PulseAudio, прикреплённые к дисплею X11 "
"(действие по умолчанию)\n"
" -e    Экспортировать локальные данные PulseAudio в дисплей X11\n"
" -i    Импортировать данные PulseAudio из дисплея X11 в локальные переменные "
"окружения и в файл cookie.\n"
" -r    Удалить данные PulseAudio из дисплея X11\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Не удалось разобрать командную строку.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Сервер: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Источник: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Аудиоприёмник: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Cookie: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Не удалось разобрать данные cookie.\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Не удалось сохранить данные cookie.\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Не удалось получить полное имя домена (FQDN).\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Не удалось загрузить данные cookie.\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Не реализовано.\n"

#~ msgid ""
#~ "%s [options]\n"
#~ "%s\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -r, --record                          Create a connection for "
#~ "recording\n"
#~ "  -p, --playback                        Create a connection for playback\n"
#~ "\n"
#~ "  -v, --verbose                         Enable verbose operations\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -d, --device=DEVICE                   The name of the sink/source to "
#~ "connect to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ "      --stream-name=NAME                How to call this stream on the "
#~ "server\n"
#~ "      --volume=VOLUME                   Specify the initial (linear) "
#~ "volume in range 0...65536\n"
#~ "      --rate=SAMPLERATE                 The sample rate in Hz (defaults "
#~ "to 44100)\n"
#~ "      --format=SAMPLEFORMAT             The sample format, see\n"
#~ "                                        https://www.freedesktop.org/wiki/"
#~ "Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
#~ "                                        for possible values (defaults to "
#~ "s16ne)\n"
#~ "      --channels=CHANNELS               The number of channels, 1 for "
#~ "mono, 2 for stereo\n"
#~ "                                        (defaults to 2)\n"
#~ "      --channel-map=CHANNELMAP          Channel map to use instead of the "
#~ "default\n"
#~ "      --fix-format                      Take the sample format from the "
#~ "sink/source the stream is\n"
#~ "                                        being connected to.\n"
#~ "      --fix-rate                        Take the sampling rate from the "
#~ "sink/source the stream is\n"
#~ "                                        being connected to.\n"
#~ "      --fix-channels                    Take the number of channels and "
#~ "the channel map\n"
#~ "                                        from the sink/source the stream "
#~ "is being connected to.\n"
#~ "      --no-remix                        Don't upmix or downmix channels.\n"
#~ "      --no-remap                        Map channels by index instead of "
#~ "name.\n"
#~ "      --latency=BYTES                   Request the specified latency in "
#~ "bytes.\n"
#~ "      --process-time=BYTES              Request the specified process "
#~ "time per request in bytes.\n"
#~ "      --latency-msec=MSEC               Request the specified latency in "
#~ "msec.\n"
#~ "      --process-time-msec=MSEC          Request the specified process "
#~ "time per request in msec.\n"
#~ "      --property=PROPERTY=VALUE         Set the specified property to the "
#~ "specified value.\n"
#~ "      --raw                             Record/play raw PCM data.\n"
#~ "      --passthrough                     Passthrough data.\n"
#~ "      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
#~ "      --list-file-formats               List available file formats.\n"
#~ "      --monitor-stream=INDEX            Record from the sink input with "
#~ "index INDEX.\n"
#~ msgstr ""
#~ "%s [параметры]\n"
#~ "%s\n"
#~ "\n"
#~ "  -h, --help                            Показать эту справку\n"
#~ "      --version                         Показать сведения о версии\n"
#~ "\n"
#~ "  -r, --record                          Создать соединение для записи\n"
#~ "  -p, --playback                        Создать соединение для "
#~ "воспроизведения\n"
#~ "\n"
#~ "  -v, --verbose                         Включить подробные операции\n"
#~ "\n"
#~ "  -s, --server=СЕРВЕР                   Имя сервера для подключения\n"
#~ "  -d, --device=УСТРОЙСТВО               Имя приёмника/источника для "
#~ "подключения\n"
#~ "  -n, --client-name=ИМЯ                 Имя этого клиента на сервере\n"
#~ "      --stream-name=ИМЯ                 Имя этого потока на сервере\n"
#~ "      --volume=VOLUME                   Указать начальную (линейную) "
#~ "громкость в диапазоне 0...65536\n"
#~ "      --rate=SAMPLERATE                 Частота дискретизации в Гц (по "
#~ "умолчанию 44100)\n"
#~ "      --format=SAMPLEFORMAT             Формат выборки, смотрите\n"
#~ "                                        https://www.freedesktop.org/wiki/"
#~ "Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
#~ "                                        возможные значения (по умолчанию "
#~ "s16ne)\n"
#~ "      --channels=КАНАЛЫ                 Количество каналов, 1 для моно, 2 "
#~ "для стерео\n"
#~ "                                        (по умолчанию 2)\n"
#~ "      --channel-map=CHANNELMAP          Карта каналов для использования "
#~ "вместо установленной по умолчанию\n"
#~ "      --fix-format                      Взять образец формата из "
#~ "приёмника/источника, к которому\n"
#~ "                                        подключен поток.\n"
#~ "      --fix-rate                        Взять частоту дискретизации из "
#~ "приёмника/источника, к которому\n"
#~ "                                        подключен поток.\n"
#~ "      --fix-channels                    Взять количество каналов и карту "
#~ "каналов\n"
#~ "                                        из приёмника/источника, к "
#~ "которому подключен поток.\n"
#~ "      --no-remix                        Не менять число каналов.\n"
#~ "      --no-remap                        Сопоставлять каналы по индексу, а "
#~ "не по имени.\n"
#~ "      --latency=BYTES                   Запросить указанную задержку в "
#~ "байтах.\n"
#~ "      --process-time=BYTES              Запросить указанное время "
#~ "процесса на запрос в байтах.\n"
#~ "      --latency-msec=MSEC               Запросить указанную задержку в "
#~ "мсек.\n"
#~ "      --process-time-msec=MSEC          Запросить указанное время "
#~ "процесса на запрос в мсек.\n"
#~ "      --property=PROPERTY=VALUE         Установить для указанного "
#~ "свойства указанное значение.\n"
#~ "      --raw                             Запись/воспроизведение "
#~ "необработанных данных PCM.\n"
#~ "      --passthrough                     Пропускать данные.\n"
#~ "      --file-format[=FFORMAT]           Запись/воспроизведение "
#~ "форматированных данных PCM.\n"
#~ "      --list-file-formats               Список доступных форматов "
#~ "файлов.\n"
#~ "      --monitor-stream=INDEX            Запись с входа приёмника с "
#~ "индексом INDEX.\n"

#~ msgid "Failed to initialize daemon."
#~ msgstr "Не удалось инициализировать демон."

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "Аналоговый выход для низких частот"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Сквозной цифровой канал (S/PDIF)"

#~ msgid "Digital Passthrough (IEC958)"
#~ msgstr "Сквозной цифровой канал (IEC958)"

#~ msgid "Cleaning up privileges."
#~ msgstr "Отказ от привилегий администратора."

#~ msgid "Got signal %s."
#~ msgstr "Получен сигнал %s."

#~ msgid "Exiting."
#~ msgstr "Выход."

#~ msgid "Found user '%s' (UID %lu) and group '%s' (GID %lu)."
#~ msgstr "Найден пользователь «%s» (UID %lu) и группа «%s» (GID %lu)."

# BUGME: gettext understands only single string constants. --aspotashev
# https://bugs.freedesktop.org/show_bug.cgi?id=78564
#~ msgid "Successfully changed user to \""
#~ msgstr "Успешно произведена смена пользователя на «"

#~ msgid "setrlimit(%s, (%u, %u)) failed: %s"
#~ msgstr "Не удалось выполнить setrlimit(%s, (%u, %u)): %s"

#~ msgid "Daemon not running"
#~ msgstr "Демон не запущен."

#~ msgid "Daemon running as PID %u"
#~ msgstr "Демон запущен с идентификатором процесса (PID) %u."

#~ msgid "Daemon startup successful."
#~ msgstr "Демон успешно запущен."

#~ msgid "This is PulseAudio %s"
#~ msgstr "PulseAudio %s"

#~ msgid "Compilation host: %s"
#~ msgstr "Скомпилирован на хосте: %s"

#~ msgid "Compilation CFLAGS: %s"
#~ msgstr "CFLAGS при компиляции: %s"

#~ msgid "Running on host: %s"
#~ msgstr "Запущен на хосте: %s"

#~ msgid "Found %u CPUs."
#~ msgstr "Найдено процессоров: %u."

#~ msgid "Page size is %lu bytes"
#~ msgstr "Размер страницы: %lu байт"

#~ msgid "Compiled with Valgrind support: yes"
#~ msgstr "Скомпилировано с поддержкой Valgrind: да"

#~ msgid "Compiled with Valgrind support: no"
#~ msgstr "Скомпилировано с поддержкой Valgrind: нет"

#~ msgid "Running in valgrind mode: %s"
#~ msgstr "Запущен в режиме Valgrind: %s"

#~ msgid "Running in VM: %s"
#~ msgstr "Запущен в виртуальной машине: %s"

#~ msgid "Optimized build: yes"
#~ msgstr "Оптимизированная сборка: да"

#~ msgid "Optimized build: no"
#~ msgstr "Оптимизированная сборка: нет"

#~ msgid "NDEBUG defined, all asserts disabled."
#~ msgstr "NDEBUG задан, все проверки отключены."

# BUGME: Should be "FASTPATH defined, only fast path asserts enabled." --aspotashev
# https://bugs.freedesktop.org/show_bug.cgi?id=78568
#~ msgid "FASTPATH defined, only fast path asserts disabled."
#~ msgstr "FASTPATH задан, включены только быстрые проверки «fast path»."

#~ msgid "All asserts enabled."
#~ msgstr "Все проверки включены."

#~ msgid "Machine ID is %s."
#~ msgstr "Идентификатор компьютера: %s."

#~ msgid "Session ID is %s."
#~ msgstr "Идентификатор сеанса: %s."

#~ msgid "Using runtime directory %s."
#~ msgstr "Используется рабочий каталог %s."

#~ msgid "Using state directory %s."
#~ msgstr "Используется каталог хранения состояний %s."

#~ msgid "Using modules directory %s."
#~ msgstr "Используется каталог модулей %s."

#~ msgid "Running in system mode: %s"
#~ msgstr "Запущен в общесистемном режиме: %s"

# BUGME: remove exclamation --aspotashev
# https://bugs.freedesktop.org/show_bug.cgi?id=78563
#~ msgid "Fresh high-resolution timers available! Bon appetit!"
#~ msgstr "Доступны таймеры высокого разрешения."

# BUGME: remove exclamation --aspotashev
# https://bugs.freedesktop.org/show_bug.cgi?id=78563
#~ msgid ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"
#~ msgstr ""
#~ "Рекомендуется включить в ядре Linux поддержку таймеров высокого "
#~ "разрешения."

#~ msgid "Daemon startup complete."
#~ msgstr "Запуск демона завершён."

#~ msgid "Daemon shutdown initiated."
#~ msgstr "Начато завершение демона."

#~ msgid "Daemon terminated."
#~ msgstr "Демон завершён."

#~ msgid "No cookie loaded. Attempting to connect without."
#~ msgstr "Cookie не загружены. Попытка подключения без них."

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [параметры]\n"
#~ "\n"
#~ "-h, --help                            Показать эту справку о параметрах\n"
#~ "-v, --verbose                         Выводить отладочные сообщения\n"
#~ "      --from-rate=ЧАСТОТА             Исходная частота дискретизации в "
#~ "Гц\n"
#~ "                                      (по умолчанию 44100)\n"
#~ "      --from-format=ФОРМАТ_ОТСЧЁТОВ   Исходный формат отсчётов (по "
#~ "умолчанию\n"
#~ "                                      s16le)\n"
#~ "      --from-channels=КАНАЛОВ         Исходное число каналов (по "
#~ "умолчанию 1)\n"
#~ "      --to-rate=ЧАСТОТА               Конечная частота дискретизации в "
#~ "Гц\n"
#~ "                                      (по умолчанию 44100)\n"
#~ "      --to-format=ФОРМАТ_ОТСЧЁТОВ     Конечный формат отсчётов (по "
#~ "умолчанию                                       s16le)\n"
#~ "      --to-channels=КАНАЛОВ           Конечное число каналов (по "
#~ "умолчанию 1)\n"
#~ "      --resample-method=МЕТОД         Метод передискретизации (по "
#~ "умолчанию                                       «auto»)\n"
#~ "      --seconds=СЕКУНДЫ               Исходная длительность потока\n"
#~ "                                      (по умолчанию 60)\n"
#~ "\n"
#~ "Если форматы не указаны, но будут перебраны все комбинации форматов.\n"
#~ "\n"
#~ "Формат отсчётов должен принимать одно из значений s16le, s16be, u8, "
#~ "float32le,\n"
#~ "float32be, ulaw, alaw, s24le, s24be, s24-32le, s24-32be, s32le, s32be\n"
#~ "(по умолчанию s16ne).\n"
#~ "\n"
#~ "Используйте «--dump-resample-methods» для просмотра списка возможных "
#~ "методов\n"
#~ "передискретизации.\n"

#~ msgid "=== %d seconds: %d Hz %d ch (%s) -> %d Hz %d ch (%s)"
#~ msgstr "=== %d секунд: %d Гц %d-канальный (%s) -> %d Hz %d-канальный (%s)"

#~ msgid "Failed to load client configuration file.\n"
#~ msgstr "Не удалось загрузить файл конфигурации клиента.\n"

#~ msgid "Failed to read environment configuration data.\n"
#~ msgstr "Не удалось прочитать данные конфигурации окружения.\n"

#~ msgid "PulseAudio Sound System KDE Routing Policy"
#~ msgstr "Звуковая система PulseAudio с маршрутизацией для KDE"

#~ msgid "Start the PulseAudio Sound System with KDE Routing Policy"
#~ msgstr "Запуск звуковой системы PulseAudio с маршрутизацией для KDE"

#~ msgid "Failed to open configuration file '%s': %s"
#~ msgstr "Не удалось открыть файл конфигурации «%s»: %s"

#, fuzzy
#~ msgid "Successfully dropped root privileges."
#~ msgstr "Успешное удаление привилегий администратора."

#~ msgid "[%s:%u] rlimit not supported on this platform."
#~ msgstr "[%s:%u] rlimit не поддерживается на этой платформе."

#~ msgid "XOpenDisplay() failed"
#~ msgstr "XOpenDisplay() не удалось"

#~ msgid ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"
#~ msgstr ""
#~ "Выход источника #%u\n"
#~ "\tДрайвер: %s\n"
#~ "\tРодительский модуль: %s\n"
#~ "\tКлиент: %s\n"
#~ "\tИсточник: %u\n"
#~ "\tСпецификация сэмплов: %s\n"
#~ "\tСхема каналов: %s\n"
#~ "\tЗадержка буфера: %0.0f мкс\n"
#~ "\tЗадержка источника: %0.0f мкс\n"
#~ "\tМетод ресэмплирования: %s\n"
#~ "\tСвойства:\n"
#~ "\t\t%s\n"

#, fuzzy
#~ msgid ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "%s [options] subscribe\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ msgstr ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"

#~ msgid "%s+%s"
#~ msgstr "%s+%s"

#~ msgid "%s / %s"
#~ msgstr "%s / %s"

#~ msgid "Digital Surround 4.0 (IEC958)"
#~ msgstr "Цифровой объёмный 4.0 (IEC958)"

#~ msgid "Low Frequency Emmiter"
#~ msgstr "Сабвуфер"
