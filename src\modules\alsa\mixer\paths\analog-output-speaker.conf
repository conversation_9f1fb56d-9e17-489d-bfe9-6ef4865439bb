# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Path for mixers that have a 'Speaker' control
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 100
description-key = analog-output-speaker

[Properties]
device.icon_name = audio-speakers

[Jack Headphone]
state.plugged = no
state.unplugged = unknown

[Jack Dock Headphone]
state.plugged = no
state.unplugged = unknown

[Jack Front Headphone]
state.plugged = no
state.unplugged = unknown

[Jack Line Out]
state.plugged = no
state.unplugged = unknown

[Jack Line Out Front]
state.plugged = no
state.unplugged = unknown

[Jack Front Line Out]
state.plugged = no
state.unplugged = unknown

[Jack Rear Line Out]
state.plugged = no
state.unplugged = unknown

[Jack Dock Line Out]
state.plugged = no
state.unplugged = unknown

[Jack Speaker]
required-any = any

[Jack Speaker Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

[Jack Speaker Front Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

[Jack Speaker - Output]
required-any = any

[Element Hardware Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master Mono]
switch = off
volume = off

; Make sure the internal speakers are not auto-muted once the system has speakers
[Element Auto-Mute Mode]
enumeration = select

[Option Auto-Mute Mode:Disabled]
name = analog-output-speaker

; This profile path is intended to control the speaker, let's mute headphones
; else there will be a spike when plugging in headphones
[Element Headphone]
switch = off
volume = off

[Element Headphone,1]
switch = off
volume = off

[Element Headphone2]
switch = off
volume = off

[Element Headphone+LO]
switch = off
volume = off

[Element Speaker+LO]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Speaker]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Desktop Speaker]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Front]
switch = mute
volume = merge
override-map.1 = all-front
override-map.2 = front-left,front-right

[Element Front Speaker]
switch = mute
volume = merge
override-map.1 = all-front
override-map.2 = front-left,front-right
required-any = any

[Element Speaker Front]
switch = mute
volume = merge
override-map.1 = all-front
override-map.2 = front-left,front-right
required-any = any

[Element Rear]
switch = mute
volume = merge
override-map.1 = all-rear
override-map.2 = rear-left,rear-right

[Element Surround]
switch = mute
volume = merge
override-map.1 = all-rear
override-map.2 = rear-left,rear-right

[Element Surround Speaker]
switch = mute
volume = merge
override-map.1 = all-rear
override-map.2 = rear-left,rear-right
required-any = any

[Element Speaker Surround]
switch = mute
volume = merge
override-map.1 = all-rear
override-map.2 = rear-left,rear-right
required-any = any

[Element Side]
switch = mute
volume = merge
override-map.1 = all-side
override-map.2 = side-left,side-right

[Element Speaker Side]
switch = mute
volume = merge
override-map.1 = all-side
override-map.2 = side-left,side-right

[Element Center]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,all-center

[Element Center Speaker]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,all-center
required-any = any

[Element LFE]
switch = mute
volume = merge
override-map.1 = lfe
override-map.2 = lfe,lfe

[Element LFE Speaker]
switch = mute
volume = merge
override-map.1 = lfe
override-map.2 = lfe,lfe
required-any = any

[Element Bass Speaker]
switch = mute
volume = merge
override-map.1 = lfe
override-map.2 = lfe,lfe
required-any = any

[Element CLFE]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,lfe

[Element Center/LFE]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,lfe

[Element Speaker CLFE]
switch = mute
volume = merge
override-map.1 = all-center
override-map.2 = all-center,lfe

.include analog-output.conf.common
