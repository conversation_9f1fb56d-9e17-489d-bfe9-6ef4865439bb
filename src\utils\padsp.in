#!/bin/sh

# This file is part of PulseAudio.
#
# Copyright 2006 Lennart Poettering
# Copyright 2006 <PERSON> <<EMAIL>> for Cendio AB
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published by
# the Free Software Foundation; either version 2 of the License, or
# (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

while getopts 'hs:n:m:MSDd' param ; do
	case $param in
		s)
			PULSE_SERVER="$OPTARG"
			export PULSE_SERVER
			;;
		n)
			PADSP_CLIENT_NAME="$OPTARG"
			export PADSP_CLIENT_NAME
			;;
		m)
			PADSP_STREAM_NAME="$OPTARG"
			export PADSP_STREAM_NAME
			;;
		M)
			PADSP_NO_MIXER=1
			export PADSP_NO_MIXER
			;;
		S)
			PADSP_NO_SNDSTAT=1
			export PADSP_NO_SNDSTAT
			;;
		D)
			PADSP_NO_DSP=1
			export PADSP_NO_DSP
			;;
		d)
			if [ x"$PADSP_DEBUG" = x ]; then
				PADSP_DEBUG=1
			else
				PADSP_DEBUG=$(( $PADSP_DEBUG + 1 ))
			fi
			export PADSP_DEBUG
			;;
		*)
			echo "$0 - redirect OSS audio devices to PulseAudio"
			echo " "
			echo "$0 [options] application [arguments]"
			echo " "
			echo "options:"
			echo "	-h                  show brief help"
			echo "	-s <host>[:<port>]  contact a specific PulseAudio server"
			echo "	-n <name>           client name to report to the server"
			echo "	-m <name>           stream name to report to the server"
			echo "	-M                  disable /dev/mixer emulation"
			echo "	-S                  disable /dev/sndstat emulation"
			echo "	-D                  disable /dev/dsp emulation"
			echo "	-d                  enable debug output"
			exit 0
			;;
	esac
done

shift $(( $OPTIND - 1 ))

if [ x"$LD_PRELOAD" = x ] ; then
   LD_PRELOAD="@PULSEDSP_LOCATION@/libpulsedsp.so"
else
   LD_PRELOAD="$LD_PRELOAD @PULSEDSP_LOCATION@/libpulsedsp.so"
fi

export LD_PRELOAD

exec "$@"
