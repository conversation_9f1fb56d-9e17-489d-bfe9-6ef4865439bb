.tarball-version
.version
.*.swp
ABOUT-NLS
build*
*~
*.tar.gz
*.pc
PulseAudioConfig.cmake
PulseAudioConfigVersion.cmake
/Makefile
/Makefile.in
aclocal.m4
autom4te.cache
compile
config.guess
config.h
config.h.in
config.log
config.rpath
config.status
config.sub
configure
cscope.out
cscope.in.out
cscope.po.out
pulse-daemon.log
depcomp
install/
install-sh
libltdl
libtool
ltmain.sh
missing
mkinstalldirs
stamp-*
.dirstamp
*.orig
*.rej
subprojects/*/
