# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; HP Thunderbolt Dock Audio Module
;
; This device attaches to the "HP Thunderbolt Dock 120W G2" dock. The audio
; module provides a speakerphone with echo cancellation and appears in ALSA as
; a USB sound card with stereo input and output.
;
; The dock itself has a 3.5mm headset connector and appears as a separate USB
; sound card, configuration for it is in hp-tbt-dock-120w-g2.conf.

[General]
auto-profiles = no

[Mapping analog-stereo-speakerphone]
device-strings = hw:%f,0,0
channel-map = left,right
intended-roles = phone

[Profile output:analog-stereo-speakerphone+input:analog-stereo-speakerphone]
output-mappings = analog-stereo-speakerphone
input-mappings = analog-stereo-speakerphone
skip-probe = yes
