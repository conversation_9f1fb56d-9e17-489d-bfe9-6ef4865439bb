# Simplified Chinese translation for PulseAudio.
# Copyright (C) 2008 PULSEAUDIO COPYRIGHT HOLDER
# This file is distributed under the same license as the pulseaudio package.
# <AUTHOR> <EMAIL>, 2008, 2009.
# <PERSON> <<EMAIL>>, 2009, 2012.
# <PERSON><PERSON><PERSON><PERSON>seng <<EMAIL>>, 2010, 2012.
# <PERSON> <<EMAIL>>, 2015.
# <PERSON><PERSON> (Arthur2e5) <<EMAIL>>, 2015.
#
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio.master-tx\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2021-12-04 09:16+0000\n"
"Last-Translator: Lv <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Chinese (Simplified) <https://translate.fedoraproject.org/"
"projects/pulseaudio/pulseaudio/zh_CN/>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.9.1\n"
"X-Launchpad-Export-Date: 2016-03-22 13:23+0000\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [选项]\n"
"\n"
"命令：\n"
"  -h, --help                            显示此帮助\n"
"      --version                         显示版本号\n"
"      --dump-conf                 转储默认设置\n"
"      --dump-modules        转储可用的模块列表\n"
"      --dump-resample-methods           转储可用的重采样方法\n"
"      --cleanup-shm             清理滞留的共享内存段\n"
"      --start                               如果后台程序没有运行则启动后台程"
"序\n"
"  -k  --kill                                杀死运行中的后台程序\n"
"      --check                            寻找运行中的后台程序（只返回退出"
"码）\n"
"\n"
"选项：\n"
"      --system[=BOOL]        作为系统范围实例运行\n"
"  -D, --daemonize[=BOOL]                启动后转为后台运行\n"
"      --fail[=BOOL]                 启动失败则退出\n"
"      --high-priority[=BOOL]                尝试设定高静态优先级\n"
"                                        （仅以 root 运行时，处于 SUID 或者\n"
"                                        在 RLIMIT_NICE 提升时可用）\n"
"      --realtime[=BOOL]                 尝试启用实时调度\n"
"                                        （仅以 root 运行时，处于 SUID 或者\n"
"                                        在 RLIMIT_PTPRIO 提升时可用）\n"
"      --disallow-module-loading[=BOOL]  启动后拒绝模块用户的\n"
"                                                                                           加"
"载／卸载请求\n"
"      --disallow-exit[=BOOL]        拒绝用户的退出请求\n"
"      --exit-idle-time=SECS           空闲指定时长后终止\n"
"                                                             后台程序\n"
"      --scache-idle-time=SECS    空闲指定时长后卸载\n"
"                                                                       自动加"
"载的采样\n"
"      --log-level[=LEVEL]                设定日志等级\n"
"  -v  --verbose                                  记录详细信息\n"
"      --log-target={auto,syslog,stderr,file:路径,newfile:路径}\n"
"                                        指定日志目标\n"
"      --log-meta[=BOOL]                 将代码位置加入日志\n"
"      --log-time[=BOOL]                  将时间戳加入日志\n"
"      --log-backtrace=FRAMES     将回溯跟踪加入日志\n"
"  -p, --dl-search-path=PATH      为动态共享对象（插件）设定\n"
"                                        搜索路径\n"
"      --resample-method=METHOD          使用指定的重采样方法\n"
"                                        (使用 --dump-resample-methods 查看\n"
"                                        可能的值)\n"
"      --use-pid-file[=BOOL]            创建一个PID文件\n"
"      --no-cpu-limit[=BOOL]          不在支持的平台上\n"
"                                                                        安装 "
"CPU 负载限制器\n"
"       --disable-shm[=BOOL]         禁用共享内存支持\n"
"       --enable-memfd[=BOOL]    使能使用内存文件句柄共享内存\n"
"\n"
"启动脚本：\n"
"  -L, --load=\"模块 参数...\"           用指定的参数加载\n"
"                                                                        指定"
"的插件模块\n"
"  -F, --file=文件名                           启动后运行\n"
"                                                                        指定"
"的脚本\n"
"  -C                                    启动后在运行的 tty 上打开命令行\n"
"\n"
"  -n                                    不加载默认的脚本文件\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize 布尔参数"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail 布尔参数"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level 日志级别参数(可以是数字 0～4 或者 debug、info、notice、warn 、"
"error 中的一个)。"

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority 布尔参数"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime 布尔参数"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading 布尔参数"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit 布尔参数"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file 布尔参数"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"无效的日志目标：要么使用 syslog、journal、stderr 或 auto，要么给定正确的文件"
"名：file:<路径>、newfile:<路径>。"

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"无效的日志目标：要么使用 syslog、stderr 或 auto，要么给定正确的文件名：file:<"
"路径>、newfile:<路径>。"

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time 布尔参数"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta 布尔参数"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "无效的重采样方法 %s。"

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system 布尔参数"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit 布尔参数"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm 布尔参数"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd 期望布尔型参数"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] 无效的日志目标 %s。"

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] 无效的日志级别 '%s。"

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] 无效的重采样方法 %s。"

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] 无效的运行限制 %s。"

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] 无效的采样格式 %s。"

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] 无效的采样率 %s。"

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] 无效的采样声道 %s。"

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] 无效声道映射 '%s'。"

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] 无效的分片数 %s。"

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] 无效的分片大小 %s。"

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] 无效的优先级 %s。"

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] 无效的服务器类型 '%s'。"

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "打开配置文件失败：%s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr "指定的默认声道映射的声道数与指定的默认声道数不同。"

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### 从配置文件读取：%s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "名称：%s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "没有可用的模块信息\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "版本：%s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "描述：%s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "作者：%s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "用法：%s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "加载一次：%s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "旧接口警告：%s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "路径：%s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "打开模块 %s 失败：%s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "查找原始 lt_dlopen 加载器失败。"

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "分配新的动态加载器失败。"

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "添加 bind-now-loader 失败。"

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "找不到用户 %s。"

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "找不到组 %s。"

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "用户 %s 与组 %s 的 GID 不匹配。"

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "用户 %s 的主文件夹不是 %s，忽略。"

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "创建 %s 失败：%s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "更改组列表失败：%s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "更改 GID 失败：%s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "更改 UID 失败：%s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "此平台不支持系统全局模式。"

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "命令行解析失败。"

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr "已拒绝非超级用户使用系统模式，仅启动 D-Bus 服务器查找服务。"

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "杀死守护进程失败：%s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr "不应以 root 身份运行本程序(除非指定 --system 参数)。"

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "需要 root 权限。"

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "系统实例不支持 --start 参数。"

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr "用户配置的服务器 %s，拒绝启动/自动派生。"

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr "用户配置的服务器 %s，看起来是本地服务器。正在进一步探测。"

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "正在以系统模式运行，但未设定 --disallow-exit。"

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr "正在以系统模式运行，但未设定 --disallow-module-loading。"

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "正在以系统模式运行，强制禁用 SHM 模式。"

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr "正在以系统模式运行，强制禁用退出空闲时间。"

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "获取 stdio 失败。"

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() 失败：%s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() 失败：%s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() 失败：%s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "守护程序启动失败。"

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() 失败：%s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "获取机器 ID 失败"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"好的，那么您现在是在系统模式中运行 PA。请注意：您很可能不应该这样做。\n"
"请阅读 http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/"
"User/WhatIsWrongWithSystemWide/ 以了解为什么系统模式通常不是个好主意。"

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() 失败。"

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() 失败。"

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "命令行参数"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr "由于执行启动命令出错，初始化守护进程失败。原始的命令为：%s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "守护进程未加载任何负载模块，拒绝工作。"

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "PulseAudio 声音系统"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "启动 PulseAudio 声音系统"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "输入"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "扩展坞输入"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "扩展坞话筒"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "扩展坞线输入"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "输入插孔"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "话筒"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "前麦克风"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "后麦克风"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "外部话筒"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "内部话筒"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "无线电"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "视频"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "自动增益控制"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "无自动增益控制"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "增强"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "无增强"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "功放"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "无功放"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "重低音增强"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "无重低音增强"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "扬声器"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "模拟耳机"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "模拟输入"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "扩展坞麦克风"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "头挂麦克风"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "模拟输出"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "模拟耳机"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "模拟耳机单声道输出"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "线缆输出"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "模拟单声道输出"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "扬声器"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "高清多媒体接口/ DisplayPort接口"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "数字输出 (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "数字输入 (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "多声道输入"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "多声道输出"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "游戏输出"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "聊天输出"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "聊天输入"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "虚拟环绕声信宿 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "模拟单声道"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "模拟单声道（左）"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "模拟单声道（右）"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "模拟立体声"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "单声道"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "立体声"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "耳机"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "喇叭扩音器"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "多声道"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "模拟环绕 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "模拟环绕 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "模拟环绕 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "模拟环绕 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "模拟环绕 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "模拟环绕 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "模拟环绕 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "模拟环绕 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "模拟环绕 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "模拟环绕 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "模拟环绕 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "数字立体声(IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "数字环绕 4.0(IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "数字环绕 5.1(IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "数字环绕 5.1（IEC958/DTS）"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "数字立体声(HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "数字环绕 5.1（HDMI）"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "聊天"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "游戏"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "模拟单声道双工"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "模拟立体声双工"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "数字立体声双工(IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "多声道双工"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "立体声双工"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "单声道聊天+7.1环绕声"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "关"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "%s 输出"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "%s 输入"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA 提醒我们在该设备中写入新数据，但实际上没有什么可以写入的！\n"
"这很可能是 ALSA 驱动程序 '%s' 中的一个 bug。请向 ALSA 开发人员报告这个问"
"题。\n"
"我们因 POLLOUT 被设置而唤醒 -- 但结果是 snd_pcm_avail() 返回 0 或者另一个小于"
"最小可用值的数值。"

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA 提醒我们从该设备中读取新数据，但实际上没有什么可以读取的！\n"
"这很可能是 ALSA 驱动程序 '%s' 中的一个 bug。请向 ALSA 开发人员报告这个问"
"题。\n"
"我们因 POLLOUT 被设置而唤醒 -- 但结果是 snd_pcm_avail() 返回 0 或者另一个小于"
"最小可用值的数值。"

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() 的返回值非常大：%lu 字节(%lu毫秒)。\n"
"这很可能是由 ALSA 驱动程序' %s' 的缺陷导致的。请向 ALSA 开发者报告这个问题。"

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() 返回的值非常大：%li 字节(%s%lu 毫秒)。\n"
"这很可能是由 ALSA 驱动程序 '%s' 的缺陷导致的。请向 ALSA 开发者报告这个问题。"

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() 返回的值非常很奇怪：延迟 %lu 小于可用 (avail) %lu。\n"
"很可能是 ALSA 驱动程序 '%s' 中的 bug。请向 ALSA 开发者举报这个问题。"

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() 的返回值非常大：%lu 字节(%lu ms)。\n"
"这很可能是由 ALSA 驱动程序' %s' 中的 bug。请向 ALSA 开发者举报这个问题。"

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "蓝牙输入"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "蓝牙输出"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "免手操作"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "头戴耳机"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "便携式"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "车内"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "高保真"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "电话"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "高保真回放 (A2DP 信宿)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "高保真采集（A2DP 信源）"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "头戴式耳机单元 (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "头戴式音频网关 (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "头戴式耳机单元 (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "头戴式音频网关 (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<信源名称> source_properties=<信源属性> source_master=<工作信源名"
"称> sink_name=<信宿名称> sink_properties=<信宿属性> sink_master=<工作信宿名称"
"> adjust_time=<多少秒重新调整一次频率> adjust_threshold=<重新调整时的偏移量阀"
"值 (微秒)> format=<采样格式> rate=<采样率> channels=<声道数> channel_map=<声"
"道映射> aec_method=<要使用的实现> aec_args=<AEC 引擎的参数> save_aec=<在 /"
"tmp 下保存 AEC 数据> autoloaded=<若为自动加载则会设置> "
"use_volume_sharing=<yes 或 no> use_master_format=<yes or no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "启用"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "伪输出"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "总是保持至少载入一个信宿，即使它并不真实存在"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "总是保留至少有一个信宿被加载，即使它为空"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "通用均衡器"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<信宿名称> sink_properties=<信宿属性> sink_master=<要连接的信宿> "
"format=<采样格式> rate=<采样率> channels=<声道数> channel_map=<声道映射> "
"autoloaded=<若为自动加载则会设置> use_volume_sharing=<yes 或 no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "基于快速傅里叶变换的均衡器%s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<自动卸载未使用的滤波器？>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "虚拟 LDASPA 信宿"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<信宿名称> sink_properties=<信宿属性> master=<工作信宿名称> "
"format=<样本格式> rate=<采样率> channels=<声道数> channel_map=<输入声道映射> "
"plugin=<ladspa 插件名称> label=<ladspa 插件标签> control=<以半角逗号分隔的输"
"入控制值列表> input_ladspaport_map=<以半角逗号分隔的输入 LADSPA 连接端口名称"
"列表> output_ladspaport_map=<以半角逗号分隔的输出 LADSPA 连接端口名称列表> "
"autoloaded=<如果是自动加载则设置> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "定时的空信宿"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "空输出"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "未能设置格式：无效的格式字串 %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "输出设备"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "输入设备"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "@HOSTNAME@ 中的音频"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "%s@%s 的通道"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "到远程信宿 %s/%s 的通道"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "虚拟环绕声信宿"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<信宿名称> sink_properties=<信宿属性> master=<工作信宿名> format=<"
"采样格式> rate=<采样率> channels=<声道数> channel_map=<声道映射> "
"use_volume_sharing=<yes 或 no> force_flat_volume=<yes 或 no> hrir=/path/to/"
"left_hrir.wav autoloaded=<如果自动加载则设置> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "未知的设备模型"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "远程音频输出协议规范文档"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "PulseAudio 声音服务器"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "正前"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "左前"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "右前"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "正后"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "左后"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "右后"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "低音音箱"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "前左中央"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "前右中央"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "左侧"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "右侧"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "辅助 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "辅助 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "辅助 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "辅助 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "辅助 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "辅助 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "辅助 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "辅助 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "辅助 7"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "辅助 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "辅助 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "辅助 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "辅助 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "辅助 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "辅助 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "辅助 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "辅助 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "辅助 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "辅助 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "辅助 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "辅助 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "辅助 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "辅助 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "辅助 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "辅助 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "辅助 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "辅助 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "辅助 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "辅助 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "辅助 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "辅助 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "辅助 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "上中"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "上中前"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "上左前"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "上右前"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "上中后"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "上左后"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "上右后"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(无效)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "环绕 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "环绕 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "环绕 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "环绕 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "环绕 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() 失败"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() 返回真"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "cookie 数据解析失败"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork()：%s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid()：%s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "收到未知扩展 %s 的信息"

#: src/pulse/direction.c:37
msgid "input"
msgstr "输入"

#: src/pulse/direction.c:39
msgid "output"
msgstr "输出"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "双向"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "无效"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) 不属于本进程 (uid %d)，而属于 uid %d 号进程! (这可能是在"
"原生协议下通过 root 用户连接一个非 root 用户的 PulseAudio 导致的，请不要这样"
"做。)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "是"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "否"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "不能访问自动执行锁。"

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "打开目标文件 %s 失败。"

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr "尝试打开目标文件 '%s'，'%s.1'，'%s.2'…'%s.%d'，但均失败。"

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "无效的日志目标。"

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "内置音频"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "调制解调器"

#: src/pulse/error.c:38
msgid "OK"
msgstr "确定"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "拒绝访问"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "未知命令"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "无效参数"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "实体存在"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "无此实体"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "拒绝连接"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "协议错误"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "超时"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "没有授权密钥"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "内部错误"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "连接终止"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "实体已被杀死"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "无效服务器"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "模块初始化失败"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "错误状态"

#: src/pulse/error.c:54
msgid "No data"
msgstr "无数据"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "不兼容的协议版本"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "太大"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "不支持"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "未知错误码"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "没有该扩展"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "废弃的功能"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "缺少实现"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "客户端分支"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "输入/输出错误"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "设备或者资源忙"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "排空流失败：%s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "回放流枯竭。"

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "到服务器的 Draining 连接。"

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() 失败：%s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() 失败：%s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "成功创建流。"

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() 失败：%s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "缓冲量度：maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "缓冲量度：maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "正在使用采样规格 %s，声道映射 %s。"

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "已连接至设备 %s (索引: %u，已挂起: %s）。"

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "流错误：%s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "流设备挂起。%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "流设备恢复。%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "流欠载运行。%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "流超限运行。%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "流已启动。%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "流移至设备 %s (%u，%s 挂起)。%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "非 "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "更改流缓冲属性。%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "抑制请求列表为空：正在抑制音频流"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "抑制请求列表为空：正在反抑制音频流"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr "警告：收到比抑制请求更多的反抑制请求。"

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "连接已建立。%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() 失败：%s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() 失败：%s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "设置监视器流失败: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() 失败：%s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "连接失败：%s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "到达 EOF。"

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() 失败：%s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() 失败：%s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "收到信号，退出。"

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "获取传输延迟失败：%s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "时间：%0.3f 秒；延迟：%0.0f 微秒。"

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() 失败：%s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [选项]\n"
"%s\n"
"  -h, --help                        显示此帮助\n"
"      --version                     显示版本\n"
"\n"
"  -r, --record                      为录制创建连接\n"
"  -p, --playback                    为回放创建连接\n"
"\n"
"  -v, --verbose                     启用详述操作\n"
"\n"
"  -s, --server=服务器               要连接的服务器名\n"
"  -d, --device=设备名               要连接的信宿/信源名称\n"
"  -n, --client-name=名称            如何在服务器中调用此客户端\n"
"      --stream-name=名称            如何在服务器中调用这个流\n"
"      --volume=音量                 指定初始（线性）音量，取值在0...65536之"
"间\n"
"      --rate=SAMPLERATE             采样频率（单位 Hz，默认为44100）\n"
"      --format=SAMPLEFORMAT         采样类型，s16le、s16be、u8、float32le 之"
"一\n"
"                                    float32be、ulaw、alaw、s32le、s32be 中取"
"（默认为 s16ne）\n"
"      --channels=CHANNELS           通道数，1为单声道，2为立体声（默认为2）\n"
"      --channel-map=CHANNELMAP      取代默认值的通道映射表\n"
"      --fix-format                  从流连接的信宿中提取采样格式。\n"
"      --fix-rate                    从流连接的信宿中提取采样率。\n"
"      --fix-channels                从流连接的信宿中提取通道数和通道映射"
"表。\n"
"      --no-remix                    不要对通道进行 upmix 或者 downmix 操"
"作。\n"
"      --no-remap                    根据下标而非名称来映射通道。\n"
"      --latency=BYTES               请求指定字节数的延迟。\n"
"      --process-time=BYTES          每次请求指定字节数的处理时间。\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr "在 PulseAudio 声音服务器回放音频编码文件。"

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr "从 PulseAudio 声音服务器获取音频数据并写入文件。"

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr "从 PulseAudio 声音服务器获取音频数据并写入 STDOUT 或指定的文件。"

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr "在 PulseAudio 声音服务器回放 STDIN 或指定文件中的音频数据。"

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"使用 libpulse %s 编译\n"
"与 libpulse %s 链接\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "无效的客户端名称 '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "无效的流名称 '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "无效的通道映射 '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "无效的延迟规格 %s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "无效的处理时间规格 '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "无效的属性 %s"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "未知文件格式 %s。"

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "--monitor-stream 的参数解析失败"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "无效的采样规格"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open()：%s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2()：%s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "参数过多。"

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "为文件生成采样规格失败。"

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "打开声音文件失败。"

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr "警告：指定的采样规格将覆盖文件中的说明。"

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "从文件中确定采样规格失败。"

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "警告：从文件中确定通道映射失败。"

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "通道映射与采样规格不匹配"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "警告：在文件中写入通道映射失败。"

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr "使用采样规格 %s 和通道映射 %s 打开 %s 流。"

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "正在录制"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "回放"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "设置媒体名失败。"

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() 失败。"

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() 失败。"

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() 失败。"

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() 失败：%s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() 失败。"

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() 失败。"

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "名称 [参数 ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "名称|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "名称"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "名称|#N 音量"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N 音量"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "名称|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "名称|#N 键=值"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N 键=值"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "名称 信宿|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "名称 文件名"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "路径名"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "文件名 信宿|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N 信宿|信号源"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "声卡配置文件"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "名称|#N 端口"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "声卡名|卡号-#N 端口 偏移量"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "目标"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "级别 (数字)"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "FRAMES"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr ""

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            显示此帮助\n"
"     --version                     显示版本\n"
"不给定命令的话 pacmd 会启动入交互模式。\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"使用 libpulse %s 编译\n"
"与 libpulse %s 链接\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr "没有 PulseAudio 守护进程在运行，或者没有作为会话守护进程运行。"

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0)：%s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect()：%s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "杀死 PulseAudio 守护程序失败。"

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "守护进程未响应"

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write()：%s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll()：%s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read()：%s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "获取统计数据失败：%s"

#: src/utils/pactl.c:199
#, fuzzy, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "当前使用：%u 块，总共 %s 字节。\n"
msgstr[1] "当前使用：%u 块，总共 %s 字节。\n"

#: src/utils/pactl.c:205
#, fuzzy, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "整个生命周期所得分配：%u 块，总共 %s 字节。\n"
msgstr[1] "整个生命周期所得分配：%u 块，总共 %s 字节。\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "采样缓存大小：%s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "获取服务器信息失败：%s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, fuzzy, c-format
msgid "%s\n"
msgstr "%s %s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"服务器字串：%s\n"
"程序库协议版本：%u\n"
"服务器协议版本：%u\n"
"是否本地服务器：%s\n"
"客户端索引：%u\n"
"区块大小: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"用户名：%s\n"
"主机名：%s\n"
"服务器名：%s\n"
"服务器版本：%s\n"
"默认采样规格：%s\n"
"默认声道映射：%s\n"
"默认音频入口：%s\n"
"默认信源： %s\n"
"Cookie：%04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr ""

#: src/utils/pactl.c:321
msgid "available"
msgstr ""

#: src/utils/pactl.c:322
msgid "not available"
msgstr ""

#: src/utils/pactl.c:331 src/utils/pactl.c:355
#, fuzzy
msgid "Unknown"
msgstr "未知"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr ""

#: src/utils/pactl.c:335
#, fuzzy
msgid "Line"
msgstr "输入插孔"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr ""

#: src/utils/pactl.c:338
#, fuzzy
msgid "Handset"
msgstr "耳机"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr ""

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr ""

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr ""

#: src/utils/pactl.c:342
msgid "TV"
msgstr ""

#: src/utils/pactl.c:345
msgid "USB"
msgstr ""

#: src/utils/pactl.c:346
#, fuzzy
msgid "Bluetooth"
msgstr "蓝牙输入"

#: src/utils/pactl.c:352
msgid "Network"
msgstr ""

#: src/utils/pactl.c:353
#, fuzzy
msgid "Analog"
msgstr "模拟单声道"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "获取音频出口信息失败：%s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"信宿 #%u\n"
"\t状态：%s\n"
"\t名称：%s\n"
"\t描述：%s\n"
"\t驱动程序：%s\n"
"\t采样规格：%s\n"
"\t声道映射：%s\n"
"\t所有者模块：%u\n"
"\t静音：%s\n"
"\t音量：%s\n"
"\t        平衡 %0.2f\n"
"\t基础音量：%s\n"
"\t监视器信源：%s\n"
"\t延迟：%0.0f 微秒，设置为 %0.0f 微秒\n"
"\t标记：%s%s%s%s%s%s%s\n"
"\t属性：\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\t端口：\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, fuzzy, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (信宿：%u，信源：%u，优先级：%u，可用性：%s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ""

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\t活动端口：%s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\t格式：\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "获取音频入口信息失败：%s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"信源 #%u\n"
"\t状态：%s\n"
"\t名称：%s\n"
"\t描述：%s\n"
"\t驱动程序：%s\n"
"\t采样规格：%s\n"
"\t声道映射：%s\n"
"\t所有者模块：%u\n"
"\t静音：%s\n"
"\t音量：%s\n"
"\t        平衡 %0.2f\n"
"\t基础音量：%s\n"
"\t信宿的监视器：%s\n"
"\t延迟：%0.0f 微秒，已设置 %0.0f 微秒\n"
"\t标记：%s%s%s%s%s%s\n"
"\t属性：\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "n/a"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "获取模块信息失败：%s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"模块 #%u\n"
"\t名称：%s\n"
"\t参数：%s\n"
"\t使用计数：%s\n"
"\t属性\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "获取客户端信息失败：%s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"客户端 #%u\n"
"\t驱动：%s\n"
"\t拥有者模块：%s\n"
"\t属性：\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "获取声卡信息失败：%s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"卡 #%u\n"
"\t名称：%s\n"
"\t驱动：%s\n"
"\t拥有者模块：%s\n"
"\t属性：\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\t配置文件：\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr "\t\t%s: %s (信宿：%u，信源：%u，优先级：%u，可用性：%s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\t活动配置：%s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\t属性：\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\t属于配置文件：%s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "获取音频信宿输入信息失败：%s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"信宿输入 #%u\n"
"\t驱动程序：%s\n"
"\t所有者模块：%s\n"
"\t客户端：%s\n"
"\t信宿：%u\n"
"\t采样规格：%s\n"
"\t声道映射：%s\n"
"\t格式：%s\n"
"\t抑制: %s\n"
"\t静音：%s\n"
"\t音量：%s\n"
"\t        平衡 %0.2f\n"
"\t缓冲延迟：%0.0f 微秒\n"
"\t信宿延迟：%0.0f 微秒\n"
"\t重采样方法：%s\n"
"\t属性：\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "获取音频信源输出信息失败：%s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"信源输出 #%u\n"
"\t驱动程序：%s\n"
"\t所有者模块：%s\n"
"\t客户端：%s\n"
"\t信源：%u\n"
"\t采样规格：%s\n"
"\t声道映射：%s\n"
"\t格式：%s\n"
"\t抑制：%s\n"
"\t静音：%s\n"
"\t音量：%s\n"
"\t        平衡 %0.2f\n"
"\t缓冲延迟：%0.0f 微秒\n"
"\t信源延迟：%0.0f 微秒\n"
"\t重采样方法：%s\n"
"\t属性：\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "获取采样信息失败：%s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"样本 #%u\n"
"\t名称：%s\n"
"\t采样规格：%s\n"
"\t声道映射：%s\n"
"\t音量：%s\n"
"\t        平衡 %0.2f\n"
"\t时长：%0.1fs\n"
"\t大小：%s\n"
"\tLazy：%s\n"
"\t文件名称：%s\n"
"\t属性：\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "失败：%s"

#: src/utils/pactl.c:1667
#, fuzzy, c-format
msgid "Send message failed: %s"
msgstr "read() 失败：%s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "无法正确分析列表处理程序消息响应"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "列表处理程序消息响应不是JSON数组"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "未能卸载模块：模块 %s 未加载"

#: src/utils/pactl.c:1818
#, fuzzy, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] "设置音量失败：您尝试设置 %d 个声道的音量，而支持的声道数为 %d。\n"
msgstr[1] "设置音量失败：您尝试设置 %d 个声道的音量，而支持的声道数为 %d。\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "上传采样失败：%s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "文件过早结束"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "新"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "变更"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "移除"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "未知"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "信宿"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "信源"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "信宿-输入"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "信源-输出"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "模块"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "客户端"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "采样-缓冲"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "服务器"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "声卡"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "事件“%s”于 %s #%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "收到 SIGINT 信号，退出。"

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "无效采样规格"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "音量超出允许范围。\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "无效音量规格数。\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "无效音量规格。\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[选项]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[类型]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "文件名 [名称]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "名称 [信宿]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "名称|#N 音量 [音量 ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N 音量 [音量 ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "名称|#N 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N 格式列表"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"指定名称 @DEFAULT_SINK@，@DEFAULT_SOURCE@ 和 @DEFAULT_MONITOR@\n"
"可用于指定默认的信宿、信源和监视器。\n"

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            显示此帮助\n"
"      --version                         显示版本\n"
"  -s, --server=服务器                   要连接的服务器名\n"
"  -n, --client-name=名称                向服务器提供的客户端自称\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"使用 libpulse %s 编译\n"
"与 libpules %s 链接\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "无效的流名称 '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "不指定，或指定成下列之一：%s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "请指定要加载的采样文件"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "打开声音文件失败。"

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "警告：从文件中确定采样规格失败。"

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "您必须指定要播放的采样名"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "您必须指定要删除的采样名"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "您必须指定信宿输入索引和信宿"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "您必须指定信源输出索引和信源"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "必须指定模块名和参数。"

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "必须指定模块索引或名称"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr "不可指定多个信宿。必须指定一个布尔值。"

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "无效挂起规范。"

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr "不可指定多个信源。必须指定一个布尔值。"

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "您必须指定声卡名称/索引和侧写名称"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "您必须指定信宿名称/索引和端口名称"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "必须指定接收器名"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "您必须指定信源名称/索引和端口名称"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "必须指定信号源索引"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "必须指定接收器名/索引"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "您必须指定信宿名称/索引和音量"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "必须指定信源名称/索引"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "您必须指定源名称/索引和音量"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "您必须指定信宿输入索引和音量"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "无效信宿输入索引"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "您必须指定信源输出索引 (index) 和音量"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "无效信源输出索引"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "您必须指定信宿名称/索引和静音动作 (0, 1, 或 'toggle' 切换)"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "无效静音说明"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "您必须指定信源名称/索引和静音动作 (0, 1, 或 'toggle' 切换)"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr "您必须指定信宿输入索引和静音动作 (0, 1, 或 'toggle' 切换)"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "无效信宿输入索引规格"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "您必须指定信源输出索引和静音动作 (0, 1, 或 'toggle' 切换)"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "无效信源输出索引说明"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr "您必须指定对象路径和消息名称"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"如果给出过多的参数，它们将被忽略。请注意，所有消息参数必须作为单个字符串提"
"供。"

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr "您必须指定信宿名称及以英文分号分隔的其所支持格式的列表"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "您必须指定声卡名称/索引、端口名称和延迟偏移"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "无法解析延迟偏移"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "未指定有效的命令。"

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork()：%s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp()：%s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "恢复失败：%s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "挂起失败：%s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "警告：非本地声音服务器，不会挂起。\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "连接失败：%s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "收到 SIGINT 信号，退出。\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "警告：子进程被信号 %u 终止\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [选项] -- 程序 [参数...]\n"
"\n"
"当指定程序运行时，临时挂起PulseAudio。\n"
"\n"
"-h, --help 显示此帮助\n"
"--version 显示版本\n"
"-s, --server=SERVER 要连接的服务器名\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"使用 libpulse %s 编译\n"
"与 libpulse %s 链接\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() 失败。\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() 失败。\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() 失败。\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D 显示] [-S 服务器] [-O 信宿] [-I 信源] [-c 文件]  [-d|-e|-i|-r]\n"
"\n"
" -d    显示与 X11 显示关联的当前 PulseAudio 数据(默认)\n"
" -e    将本地 PulseAudio 数据导出至 X11 显示器\n"
" -i    将 PulseAudio 数据由 X11 显示器导入至本地环境变量和 cookie 文件。\n"
" -r    从 X11 显示中清除 PulseAudio 数据\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "解析命令行失败。\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "服务器：%s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "信源：%s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "信宿：%s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Cookie：%s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "解析 cookie 数据失败\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "保存 cookie 数据失败\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "获取 FQDN 失败。\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "加载 cookie 数据失败\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "尚未实现。\n"

#~ msgid "Failed to initialize daemon."
#~ msgstr "初始化守护进程失败。"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "模拟输出（LFE）"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "数字直通 (S/PDIF)"

#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "数字直通（IEC958）"

#~ msgid ""
#~ "ALSA woke us up to write new data to the device, but there was actually "
#~ "nothing to write!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA 提醒我们在该设备中写入新数据，但实际上没有什么可以写入的！\n"
#~ "这很可能是 ALSA 驱动程序 %s 中的一个 bug。请向 ALSA 开发人员报告这个问"
#~ "题。\n"
#~ "提醒我们设置 POLLOUT - 但结果是 snd_pcm_avail() 返回 0 或者另一个小于最小"
#~ "可用值的数值。"

#~ msgid ""
#~ "ALSA woke us up to read new data from the device, but there was actually "
#~ "nothing to read!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA 提醒我们从该设备中读取新数据，但实际上没有什么可以读取的！\n"
#~ "这很可能是 ALSA 驱动程序 %s 中的一个 bug。请向 ALSA 开发人员报告这个问"
#~ "题。\n"
#~ "提醒我们设置 POLLIN - 但结果是 snd_pcm_avail() 返回 0 或者另一个小于最小可"
#~ "用值的数值。"

#~ msgid "wants to record audio."
#~ msgstr "试图录制音频。"

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [选项]\n"
#~ "\n"
#~ "-h, --help                            显示帮助\n"
#~ "-v, --verbose                         输出调试信息\n"
#~ "      --from-rate=SAMPLERATE          音源频率（默认为 44100）赫兹\n"
#~ "      --from-format=SAMPLEFORMAT      音源格式（默认为 s16le）\n"
#~ "      --from-channels=CHANNELS        音源声道（默认为 1）\n"
#~ "      --to-rate=SAMPLERATE           转换为频率（默认 44100）赫兹\n"
#~ "      --to-format=SAMPLEFORMAT        转换为格式（默认为 s16le）\n"
#~ "      --to-channels=CHANNELS          转换到声道（默认为 1）\n"
#~ "      --resample-method=METHOD        重采样方法（默认为自动）\n"
#~ "      --seconds=SECONDS               音源流长度（默认为 60 秒）\n"
#~ "\n"
#~ "如果格式未指定，将会测试进行所有格式组合来回测试。\n"
#~ "\n"
#~ "采样类型有这几种：s16le，s16be，u8，float32le，float32be，ulaw，alaw，\n"
#~ "s24le，s24be，s24-32le，s24-32be，s32le，s32be。\n"
#~ "\n"
#~ "使用 --dump-resample-methods 参数可列出可能的采样方法。\n"
