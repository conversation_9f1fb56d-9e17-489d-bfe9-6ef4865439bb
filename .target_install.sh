#!/bin/sh -e
[ -z "$DEBUG" ] || set -x
echo "########## pulseaudio-17.0: target install ##########"
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
touch /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.stamp_installed
if [ -r /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.files-list-target.txt ]; then cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target; cat /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.files-list-target.txt | xargs md5sum /dev/null > /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.md5 2>/dev/null || true; cd -; fi
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
PATH="/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin"  DESTDIR=/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target PYTHONNOUSERSITE=y /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/ninja  -C /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build install
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
/usr/bin/install -D -m 755 package/pulseaudio/S50pulseaudio /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/etc/init.d/S50pulseaudio
/usr/bin/install -D -m 755 package/pulseaudio/pulse.sh /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/etc/profile.d/pulse.sh
/usr/bin/install -D -m 755 package/pulseaudio/default.pa /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/etc/pulse/default.pa
/usr/bin/install -D -m 755 package/pulseaudio/daemon.conf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/etc/pulse/daemon.conf
/usr/bin/install -D -m 755 package/pulseaudio/client.conf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/etc/pulse/client.conf
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -rf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/share/vala
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -f /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/lib/pulse-17.0/modules/module-console-kit.so
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -rf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/share/vala
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -f /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/lib/pulse-17.0/modules/module-console-kit.so
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -rf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/share/vala
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -f /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/lib/pulse-17.0/modules/module-console-kit.so
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -rf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/share/vala
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -f /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/usr/lib/pulse-17.0/modules/module-console-kit.so
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
rm -f -rf  /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target/share/info/dir
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
if test -n "" ; then rm -f -f  ; fi
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target; find . \( -type f -o -type l \) -cnewer /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.stamp_installed | tee /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.files-list-target.txt | /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/tar --no-recursion --ignore-failed-read -cf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/pulseaudio-17.0.tar -T -; true;
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target; if [ -r /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.md5 ]; then md5sum -c /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.md5 2>&1 | grep "FAILED$" | cut -d':' -f1 > /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.files-list-target-update.txt; fi
