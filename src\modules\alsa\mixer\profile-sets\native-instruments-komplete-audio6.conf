# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Native Instruments Komplete Audio 6
;
; This card has three stereo pairs of input and three stereo pairs of
; output.
;
; We knowingly only define a subset of the theoretically possible
; mapping combinations as profiles here.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-out-ab]
description = Analog Stereo 1/2
device-strings = hw:%f,0,0
channel-map = left,right,aux0,aux1,aux2,aux3
direction = output

[Mapping analog-stereo-out-cd]
description = Analog Stereo 3/4
device-strings = hw:%f,0,0
channel-map = aux0,aux1,left,right,aux2,aux3
direction = output

[Mapping stereo-out-ef]
description = Stereo 5/6 (S/PDIF)
device-strings = hw:%f,0,0
channel-map = aux0,aux1,aux2,aux3,left,right
direction = output

[Mapping analog-mono-in-a]
description = Analog Mono Input 1
device-strings = hw:%f,0,0
channel-map = mono,aux0,aux1,aux2,aux3,aux4
direction = input

[Mapping analog-mono-in-b]
description = Analog Mono Input 2
device-strings = hw:%f,0,0
channel-map = aux0,mono,aux1,aux2,aux3,aux4
direction = input

[Mapping analog-stereo-in-ab]
description = Analog Stereo Input 1/2
device-strings = hw:%f,0,0
channel-map = left,right,aux0,aux1,aux2,aux3
direction = input

[Mapping analog-stereo-in-cd]
description = Analog Stereo Input 3/4
device-strings = hw:%f,0,0
channel-map = aux0,aux1,left,right,aux2,aux3
direction = input

[Mapping stereo-in-ef]
description = Stereo Input 5/6 (S/PDIF)
device-strings = hw:%f,0,0
channel-map = aux0,aux1,aux2,aux3,left,right
direction = input

[Profile output:analog-stereo-out-ab+input:analog-stereo-in-ab]
description = Analog Stereo Output 1/2, Analog Stereo Input 1/2
output-mappings = analog-stereo-out-ab
input-mappings = analog-stereo-in-ab
priority = 100
skip-probe = yes

[Profile output:analog-stereo-out-ab+input:analog-mono-in-a]
description = Analog Stereo Output 1/2, Analog Mono Input 1
output-mappings = analog-stereo-out-ab
input-mappings = analog-mono-in-a
priority = 95
skip-probe = yes

[Profile output:analog-stereo-out-ab+input:analog-mono-in-b]
description = Analog Stereo Output 1/2, Analog Mono Input 2
output-mappings = analog-stereo-out-ab
input-mappings = analog-mono-in-b
priority = 90
skip-probe = yes

[Profile output:analog-stereo-out-cd+input:analog-stereo-in-cd]
description = Analog Stereo Output 3/4, Analog Stereo Input 3/4
output-mappings = analog-stereo-out-cd
input-mappings = analog-stereo-in-cd
priority = 80
skip-probe = yes

[Profile output:stereo-out-ef+input:stereo-in-ef]
description = Stereo Output 5/6 (S/PDIF), Stereo Input 5/6 (S/PDIF)
output-mappings = stereo-out-ef
input-mappings = stereo-in-ef
priority = 70
skip-probe = yes

