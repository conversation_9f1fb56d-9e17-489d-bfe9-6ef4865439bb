PulseAudio 17.0

Changes at a glance:
 * Notes for end users
   *  Updates to ALSA UCM-based setups
   *  Battery level indication to Bluetooth devices
   *  Support for the Bluetooth FastStream codec
   *  webrtc-audio-processing dependency updated
   *  Trigger role groups added to module-role-cork
   *  XDG base directory spec for profile-set loading
 * Notes for application developers
   * PA_RATE_MAX increased
 * Notes for packagers
   * webrtc-audio-processing dependency updated

Contributors

<PERSON>-Hughes
Alper Nebi Ya<PERSON>
Asier Sarasua Garmendia
Ataberk Özen
Balázs Meskó
Biswapriyo <PERSON>
Eero Nurkkala
Ettore Atalan
Fabrice Fontaine
Fran Diéguez
Georg Chini
Gioele <PERSON>
Gogo Gogsi
Hector <PERSON> V. Kovalenko
J<PERSON>chul <PERSON>
Jan Palus
<PERSON>
Joachim <PERSON>
Mart Raudsepp
<PERSON>-<PERSON>ler
<PERSON>ev
<PERSON>
Sabri Ünal
Sean <PERSON>-ho <PERSON><PERSON><PERSON>
<PERSON><PERSON><PERSON>vez
<PERSON>
Wim <PERSON>ymans
Yureka
acheronfail
flyingOwl
grimst
hashitaku
mooo
peijiankang
redfast00
wael
김인수


PulseAudio 16.1

A bug fix release.

 * Fix parsing of percentage volumes with decimal points in pactl
 * Fix crash with the "pacmd play-file" command when reads from the disk aren't frame-aligned
 * Fix module-rtp-recv sometimes thinking it's receiving an Opus stream when it's not
 * Fix frequent crashing in module-combine-sink, regression in 16.0
 * Fix crashing on 32-bit architectures when using the GStreamer codecs for LDAC and AptX

Contributors

Georg Chini
Igor V. Kovalenko
Jaechul Lee
Jan Palus
Sean Greenslade


PulseAudio 16.0

Changes at a glance:
 * Notes for end users
   * Opus support in RTP modules
   * Improved hardware support
     * EPOS/Sennheiser GSP 670 USB/wireless headset
     * SteelSeries GameDAC
     * Behringer UMC22 generalized to Texas Instruments PCM2902 to support more products
     * NI Komplete Audio 6 MK2 profiles
   * Tunnel latency is now configurable
   * Bluetooth device battery level reporting added
   * Tunnel and combine-sunk latency fixes
   * patcl can show information in JSON format
   * Channel remixing can be disabled for module-combine-sink
   * A lot of fixes to ALSA, bluetooth, and other components.
 * Notes for application developers
   * Stream latency reports now include resampler delay
   * Module installation location changed, remember to upgrade paprefs to the latest version!
 * Notes for packagers
   * Opus support in the RTP modules requires enabling GStreamer
   * Bluetooth battery level reporting via BlueZ requires enabling experimentals features in BlueZ
   * New time smoother implementation
   * Change Meson option 'daemon-only' to 'client'
   * glib and fftw are now common dependencies, not only for the client.
   * Enable GStreamer-based RTP by default when available

Detailed change log:

  https://www.freedesktop.org/wiki/Software/PulseAudio/Notes/16.0/

Contributors

Alexey Rubtsov
Anders Jonsson
Andika Triwidada
Arun Raghavan
Biswapriyo Nath
BtbN
Chengyi Zhao
Chupligin Sergey
Craig Howard
Daniel Dantur
Daniel Hernandez
Diederik de Haas
Dylan Van Assche
Emilio Herrera
Ettore Atalan
Fran Diéguez
Georg Chini
Gogo Gogsi
Göran Uddeborg
Hela Basa
Hui Wang
Igor V. Kovalenko
Jan Kuparinen
Jaroslav Kysela
Josef Haider
João Paulo Rechi Vita
Juho Hämäläinen
Karl Ove Hufthammer
Laurent Bigonville
Luna Jernberg
Lv Genggeng
Marijn Suijten
Mart Raudsepp
Mathy Vanvoorden
Olivier Gayot
Ovari
Oğuz Ersen
Piotr Drąg
Rafael Fontenelle
Rico Tzschichholz
Sanchayan Maity
Sebastian Reichel
Sergey A
Sibo Dong
Sungjoon Moon
Takashi Sakamoto
Takuro Onoue
Tanu Kaskinen
Temuri Doghonadze
Will Thompson
Yuri Chornoivan
acheronfail
baek inchan
guest271314
liaohanqin
simmon
simple
tensorknower69
김인수


PulseAudio 15.0

Changes at a glance:

 * Notes for end users
   * Support for LDAC and AptX bluetooth codecs, plus "SBC XQ" (SBC with higher-quality parameters)
   * Support for HFP bluetooth profiles
   * Support for Bluetooth A2DP AVRCP Absolute Volume
   * ALSA path configuration files can now be placed in user home directory
   * module-virtual-surround-sink rewritten
   * More options for module-jackdbus-detect
   * Improved hardware support
     * SteelSeries Arctis 9
     * HP Thunderbolt Dock 120W G2
     * Behringer U-Phoria UMC22
     * OnePlus Type-C Bullets
     * Sennheiser GSX 1000/1200 PRO
   * New udev variable: PULSE_MODARGS
   * max_latency_msec argument added to module-null-source
   * module-filter-apply can take filter parameters from device properties
   * module-match can now be loaded multiple times
   * Improvements to FreeBSD support
   * Windows support added to Meson
   * Additional commands for pactl
   * Card profiles can be set to sticky
 * Notes for application developers
   * New API for sending messages from clients to PulseAudio objects
   * New mechanism for applications to disable shared memory on their connection to PulseAudio
 * Notes for packagers
   * Autotools build system have been dropped
   * The startup script can now read additional configuration from the /etc/pulse/default.pa.d/ directory
   * Option to build client library and utilities only
   * Avoid loading X11 modules on Wayland (GNOME-only for now)
   * OSS support is now configurable in Meson
   * Valgrind support is now configurable in Meson

Detailed change log:

  https://www.freedesktop.org/wiki/Software/PulseAudio/Notes/15.0/

Contributors

Alexey Rubtsov
Alper Nebi Yasak
Anders Jonsson
Arun Raghavan
Ben Buchwald
Benjamin Valentin
Carlos Garnacho
Carmen Bianca Bakker
Christopher Arndt
Christopher Snowhill
David
Dusan Kazik
Edward Lee
Emilio Herrera
Evan Miller
Fabian Affolter
Faidon Liambotis
Felipe Sateler
Frédéric Danis
Georg Chini
Greg V
Göran Uddeborg
Hela Basa
Henri Chain
Hui Wang
Igor V. Kovalenko
Ilja van Sprundel
Jaechul Lee
James Bottomley
Jan Alexander Steffens (heftig)
Jan Kuparinen
Jaroslav Kysela
Jason Nader
Johannes Wolf
Julien Humbert
Kai-Heng Feng
Karl Ove Hufthammer
Klaas van Schelven
Laurent Bigonville
Laurențiu Nicola
Lyndon Brown
Marijn Suijten
Martin Wilck
Mattias Jernberg
Milo Casagrande
Nazar Mokrynskyi
Oğuz Ersen
Patrick Gaskin
Patrick McLean
Paul Seyfert
Pierre Ossman
Piotr Drąg
Pjotr Vertaalt
Ricky Tigg
Robin Lahtinen
Samuel Thibault
Sanchayan Maity
Scott Worley
Sebastian Krzyszkowiak
SimonP
Takashi Sakamoto
Tanu Kaskinen
Tobias Weise
Toni Estevez
Yaron Shahrabani
Yuri Chornoivan
morrishoresh
pseyfert
scootergrisen
simmon


PulseAudio 14.2

A bug fix release.

 * Fix port switching when unplugging headphones

Contributors

  Tanu Kaskinen


PulseAudio 14.1

A bug fix release.

 * Support upto 8 mixer channels on ALSA devices
 * Handle ALSA jacks with the same name but different index values
 * Switch to plugged-in headset when mic availability is unknown
 * Fix a potential segfault in the Bluetooth oFono HFP backend
 * Fix a problem with module-ladspa-sink when avoid-resampling=true
 * Fix database names containing canonical host for meson builds

Contributors

  Arun Raghavan
  Hui Wang
  Igor V. Kovalenko
  Jaroslav Kysela
  Kai-Heng Feng
  Patrick Gaskin
  Tanu Kaskinen
  morrishoresh


PulseAudio 14.0

Changes at a glance:

 * Notes for end users
   * Significant routing changes to default sinks/sources
     * Changing the default sink moves streams from the old default sink to the new
     * Moving a stream to the default sink removes the "manually routed" status of the stream
     * If a sink changes status to available, streams that prefer that sink move there automatically
     * The same changes have been applied to the source stream routing
     * Workaround for GNOME Sound Settings' stream routing behaviour
     * module-rescue-streams is deprecated, functionality moved to the core
     * New rescue-streams option in daemon.conf
   * Automatic switching to HDMI is now disabled by default
   * Better support for some USB gaming headsets
   * Flat volumes are now disabled by default
   * The RAOP sink can be configured to automatically reconnect on connection failures
   * Separate sink_channels and source_channels for module-jackdbus-detect
   * Improved support for ALSA UCM
   * Support for ALSA mixer controls with non-zero index
   * It's now possible to set intended roles for devices in the ALSA profile configuration
   * Ports now have a type associated with them
   * Mappings have a new "description-key" option in the ALSA profile configuration
   * New xauthority argument for X11 modules
   * module-null-sink's compressed format support can now be configured at run-time
   * The enable-lfe-remixing option in daemon.conf was split into remixing-produce-lfe and remixing-consume-lfe
   * New channel_map argument for module-raop-sink
 * Notes for application developers
   * pa_mainloop_prepare interprets the timeout argument as microseconds again
   * New availability_group and type fields in the port info structs
   * New macros: PA_LIKELY(), PA_UNLIKELY(), PA_CLAMP() and PA_CLAMP_UNLIKELY()
 * Notes for packagers
   * New GStreamer-based RTP implementation
   * qpaeq switched from Python 2 to Python 3
   * Compile-time option to forget pre-14.0 stream routing
   * The install path of the ALSA configuration files is now configurable
   * GNU gettext minimum version requirement bumped from 0.19.3 to 0.19.8
   * Heads-up: dropping autotools build system
   * Heads-up: dropping EsounD support is considered, tell us if you still need it
   * Heads-up: dropping GConf support

Detailed change log:

  https://www.freedesktop.org/wiki/Software/PulseAudio/Notes/14.0/

Contributors

  Alexander E. Patrakov
  Arun Raghavan
  Balázs Meskó
  Baurzhan Muftakhidinov
  Ben Buchwald
  Daniil Kovalev
  Dave Chiluk
  David Heidelberg
  Dusan Kazik
  Eero Nurkkala
  Emanuil Novachev
  Emilio Herrera
  Felipe Sateler
  Felix Yan
  Geert Warrink
  Georg Chini
  Göran Uddeborg
  Hugo Osvaldo Barrera
  Hui Wang
  Igor V. Kovalenko
  Jan Alexander Steffens
  Jarno Suni
  Jaroslav Kysela
  Jaska Uimonen
  Jean-Baptiste Holcroft
  Josh
  Juliano de Souza Camargo
  Kai-Heng Feng
  Karl Ove Hufthammer
  Khem Raj
  Krzysztof Stasiowski
  Laurent Bigonville
  Libin Yang
  Marc Ranolfi
  Michael Pivonka
  Milo Casagrande
  Milo Ivir
  Nick Moriarty
  Oğuz Ersen
  Pali Rohár
  Peter Levine
  Peter Meerwald
  Philip Withnall
  Piotr Drąg
  RODRIGUEZ Christophe
  Rafael Fontenelle
  Ralph Seichter
  Rasmus Thomsen
  Rickie Schroeder
  Rosen Penev
  Ryszard Knop
  Sanchayan Maity
  Sebastian Dröge
  Sebastien
  StefanBruens
  Taahir Ahmed
  Tanu Kaskinen
  Timo Gurr
  Tom Yan
  Tomasz Kontusz
  Vasilis Tsiligiannis
  Wim Taymans
  Yi-Jyun Pan
  Yuri Chornoivan
  itsthem
  muzena
  roshal
  zhaochengyi


PulseAudio 13.0

Changes at a glance:

 * Added support for Dolby TrueHD and DTS-HD Master Audio
 * Improved 5.1 surround channel routing
 * Improved initial card profile selection for ALSA cards
 * Automatically switch away from unavailable card profiles
 * Bluetooth card profile choices aren't persistent any more by default
 * Added support for some SteelSeries Arctis 5 USB headsets
 * Removed KDE specific routing configuration
 * New information collection script for bug reports: pa-info
 * New "max_latency_msec" module argument for module-loopback
 * New "fast_adjust_threshold_msec" module argument for module-loopback
 * New "stream_name" module argument for module-rtp-send
 * Fixed S/PDIF for CMEDIA USB2.0 High-Speed True HD Audio
 * Use source sample spec and channel map by default in module-loopback
 * module-role-cork and module-role-ducking can now use recording streams as triggers
 * New "avoid_resampling" module argument for module-udev-detect and module-alsa-card
 * "avoid_resampling" also tries to avoid format conversion if the ALSA device supports it
 * Added support for non-PCM formats in module-null-sink
 * More efficient channel remapping in some cases for 32-bit streams
 * Removed the "latency_time" option from module-null-source
 * New function to enable realtime scheduling for client threads
 * New convenience functions for getting parameters from pa_format_info
 * New function for pa_threaded_mainloop to run a callback in the mainloop thread without the mainloop lock being held
 * Const-ification of parameters across headers
 * Removed BlueZ 4 support
 * Dropped intltool
 * Introduction of the Meson build system
 * New build option for improving build reproducibility
 * Minor bug-fixes, bindings updates and several translation updates

Detailed change log:

  https://www.freedesktop.org/wiki/Software/PulseAudio/Notes/13.0/

Contributors

  Alan Coopersmith
  Alexander E. Patrakov
  Alexander Potashev
  Andika Triwidada
  Arnaud Rebillout
  Arun Raghavan
  Bert Hekman
  Carlos Garnacho
  Danny O'Brien
  David Emett
  David Heidelberg
  David Hewitt
  Diego Viola
  Felipe Sateler
  Fran Diéguez
  Friedel Wolff
  Frédéric Danis
  Georg Chini
  Hans de Goede
  Hongxu Jia
  Hui Wang
  Ing. Jaroslav Safka
  Jan Alexander Steffens (heftig)
  Javier Jardón
  Jean-Philippe Guillemin
  Jordi Mas
  Josef Andersson
  Joseph Herlant
  João Paulo Rechi Vita
  Kaleb Ercanbrack
  Lucas Stach
  Luiz Augusto von Dentz
  Lyndon Brown
  Marek Cernocky
  Mark Filion
  Milo Casagrande
  Moo
  Mr. M
  Nate Brown
  Nazar Mokrynskyi
  Nicolas Fella
  Olaf Hering
  Pali Rohár
  Pierre-Louis Bossart
  Piotr Drąg
  RODRIGUEZ Christophe
  Rafael Fontenelle
  Raman Shyshniou
  Russell Treleaven
  S Page
  Sangchul Lee
  Sascha Silbe
  Stefan Huber
  Takashi Iwai
  Takashi Sakamoto
  Tanu Kaskinen
  Thomas Hutschenreuther
  Tomaz Solc
  Topi Miettinen
  Wellington Wallace
  Will Stott
  Yclept Nemo
  Yuri Chornoivan
  Zakhary Husak
  jorisc90
  muzena
  scootergrisen
  shdown


PulseAudio 12.2

The previous release tarball contained a broken configure script, this release
fixes the tarball. There are no changes in the source, except for this NEWS
file update.

Contributors

  Tanu Kaskinen


PulseAudio 12.1

A bug fix release.

 * Fixed crash when switching to A2DP bluetooth profile
 * Fixed plugin search path in module-ladspa-sink
 * Fixed file permissions for the pipes created by module-pipe-sink and
   module-pipe-source

Contributors

  Mr. M
  Tanu Kaskinen


PulseAudio 12.0

Changes at a glance:

 * Better latency reporting (and hence better A/V sync) with the A2DP bluetooth
   profile
 * Much more accurate latency reporting for AirPlay devices
 * module-switch-on-connect now ignores virtual devices
 * When using passthrough for compressed audio, set the "non-audio" bit
 * Prioritize HDMI output over S/PDIF output
 * HSP support for more bluetooth headsets
 * Choose the A2DP bluetooth profile by default instead of HSP
 * New "sink_input_properties" module argument for module-ladspa-sink
 * New "use_system_clock_for_timing" module argument for module-pipe-sink
 * module-pipe-sink can now use an existing pipe
 * Fixed a crash or high CPU use problem with Intel HDMI LPE
 * Steelseries Arctis 7 USB headset stereo output support
 * Dell Thunderbolt Dock TB16 speaker jack support
 * Fixed digital input support for some USB sound cards
 * Fixed Native Instruments Traktor Audio 6 detection
 * Ability to disable input or output on macOS
 * New "dereverb" option for the Speex echo canceller
 * New module: module-always-source
 * State files not any more readable by all users in the system mode
 * module-augment-properties now uses XDG_DATA_DIRS to find .desktop files
 * Updates for the Vala bindings
 * The GConf dependency can now be avoided
 * qpaeq license changed from AGPL to LGPL
 * qpaeq ported to Qt 5
 * Compatibility with glibc 2.27
 * The esdcompat tool isn't any more installed if esound support is disabled

Detailed change log:

  https://www.freedesktop.org/wiki/Software/PulseAudio/Notes/12.0/

Contributors

  Andika Triwidada
  Andrius Štikonas
  Arun Raghavan
  Cheng-Chia Tseng
  Colin Leroy
  Constantine Kharlamov
  Daniel Rusek
  David Henningsson
  David Hewitt
  Felipe Sateler
  Georg Chini
  Guenter Milde
  Hui Wang
  Ian Ray
  Iceyer
  jnqnfe
  Johan Heikkilä
  Josef Andersson
  Jungsup Lee
  Khem Raj
  KimJeongYeon
  Kristian Klausen
  Laurent Bigonville
  Mihai Moldovan
  Mr. M
  Peter Meerwald-Stadler
  Pierre-Louis Bossart
  Piotr Drąg
  Roliga
  Russell Treleaven
  Salvador Fandino
  Samo Pogačnik
  Sangchul Lee
  Sebastian Dröge
  Sylvain Baubeau
  Tanu Kaskinen
  Vivek Dasmohapatra
  wellington wallace
  Yuri Chornoivan


PulseAudio 11.1

A bug fix release.

 * Fix a crash in filter modules related to flat volumes and volume sharing
 * Fix a crash when the bluetooth adapter reports weird MTU size
 * Disable bluetooth MTU autodetection by default
 * Add mixer handling back for hardware that doesn't have any alsa-lib
   configuration
 * Prioritize USB devices over built-in sound cards (11.0 was supposed to have
   this feature, but the implementation turned out to be incomplete)

Contributors

  Arun Raghavan
  Tanu Kaskinen


PulseAudio 11.0

Changes at a glance:

 * Support for newer AirPlay hardware
 * USB and bluetooth devices preferred over internal sound cards
 * Bluetooth HSP headset role implemented
 * Bluetooth HFP audio gateway role implemented (requires oFono)
 * Bluetooth HSP audio gateway and HFP hands-free unit roles can be enabled
   simultaneously
 * Upmixing can now be disabled without bad side effects
 * Avoid having unavailable sinks or sources as the default
 * Option to avoid resampling more often
 * Option to automatically switch bluetooth profile to HSP more often
 * Better latency control in module-loopback
 * Changed module argument names in module-ladspa-sink and
   module-virtual-surround-sink
 * Fixed input device handling in module-waveout
 * Improved bluetooth MTU configuration
 * GNU Hurd support
 * Applications can request LADSPA or virtual surround filtering for their
   streams
 * Support for 32-bit applications on 64-bit systems in padsp

Detailed change log:

  https://www.freedesktop.org/wiki/Software/PulseAudio/Notes/11.0/

Contributors

  Arun Raghavan
  ced2c
  Christian Kellner
  Colin Leroy
  Corentin Noël
  David Mandelberg
  David Michael
  Denis Shulyaka
  Felipe Sateler
  Georg Chini
  Grzegorz Kolodziejczyk
  Hajime Fujita
  Hui Wang
  Imre Vadász
  Jungsup Lee
  Karl Ove Hufthammer
  KimJeongYeon
  Luiz Augusto von Dentz
  Martin Blanchard
  Matthias Wabersich
  Mihai Moldovan
  Milo Casagrande
  Moo
  Moritz Bruder
  Muhammet Kara
  muzena
  Pali Rohár
  Paul Seyfert
  Peter Meerwald-Stadler
  Philip Chimento
  Piotr Drąg
  Rafael Fontenelle
  Renjith Thomas
  Stephen Paul Weber
  Takashi Sakamoto
  Tanu Kaskinen
  Ted Ying
  Vadim Troshchinskiy
  Wim Taymans
  Yuri Chornoivan


PulseAudio 10.0

Changes at a glance:

 * Automatically switch Bluetooth profile when using VoIP applications
 * New module for prioritizing passthrough streams (module-allow-passthrough)
 * Fix hotplugging support for USB surround sound cards
 * Separate volumes for Bluetooth A2DP and HSP profiles
 * memfd-based shared memory mechanism enabled by default
 * Removed module-xenpv-sink
 * Dropped dependency to json-c
 * When using systemd to start PulseAudio, pulseaudio.socket is always started
   first
 * Compatibility with OpenSSL 1.1.0
 * Clarified qpaeq license

Detailed change log:

  https://www.freedesktop.org/wiki/Software/PulseAudio/Notes/10.0/

Contributors

  Ahmed S. Darwish
  Aidan Gauland
  Alexander E. Patrakov
  Anton Lundin
  Arun Raghavan
  Balázs Úr
  Chris Billington
  Cédric Valmary
  Deepak Srivastava
  Dušan Kazik
  Felipe Sateler
  Fran Dieguez
  Georg Chini
  Guillaume Desmottes
  Jan Alexander Steffens
  John Paul Adrian Glaubitz
  Juha Kuikka
  Juho Hämäläinen
  KimJeongYeon
  Marcin Lewandowski
  Mario Blättermann
  Moo
  Nils Schneider
  Pali Rohár
  Peter Meerwald-Stadler
  Philip Withnall
  Pierre Ossman
  Piotr Drąg
  Rikard Söderström
  Romain Naour
  Sylvain Baubeau
  Tanu Kaskinen
  Ulrich Eckhardt
  Viktar Vaŭčkievič


PulseAudio 9.0

Changes at a glance:

 * Automatic routing improvements
 * Beamforming and various other new features in the WebRTC echo canceller
 * Various improvements in module-role-cork and module-role-ducking
 * LFE remixing disabled by default
 * memfd-backed shared memory transport
 * Support for sample rates up to 384 kHz
 * webrtc-audio-processing dependency minimum version bumped to 0.2
 * Changed the C standard from C99 to C11.

Detailed change log:

  http://www.freedesktop.org/wiki/Software/PulseAudio/Notes/9.0

Contributors

  Ahmed S. Darwish
  Alexander E. Patrakov
  Arun Raghavan
  Barun Kumar Singh
  David Henningsson
  Deepak Srivastava
  Gabor Kelemen
  Georg Chini
  Jeremy Huddleston Sequoia
  Jonathan Perkin
  Juho Hämäläinen
  Jungsup Lee
  Kamil Rytarowski
  Marcin Lewandowski
  Milo Casagrande
  Muhammet Kara
  Nazar Mokrynskyi
  Peter Meerwald
  Piotr Drąg
  Sachin Kumar Chauhan
  Sangchul Lee
  Tanu Kaskinen
  YunQiang Su


PulseAudio 8.0

Changes at a glance:

 * Automatic routing more likely to change profile
 * OS X and NetBSD support improvements
 * Systemd journal logging for clients
 * New LFE balance programming interface
 * Module-dbus-protocol improvements
 * More flexible configuration file handling
 * pulsecore-8.0.so moved to a private directory
 * New script for measuring memory consumption
 * Various bug fixes and small improvements

Detailed change log:

  http://www.freedesktop.org/wiki/Software/PulseAudio/Notes/8.0

Contributors

  Ahmed S. Darwish
  Alexander E. Patrakov
  Andrey Semashev
  Arun Raghavan
  David Henningsson
  Deepak Srivastava
  Felipe Sateler
  Georg Chini
  Jason Gerecke
  John Horan
  Juho Hämäläinen
  Julien Isorce
  Kamil Rytarowski
  KimJeongYeon
  Kiran Krishnappa
  Lev Melnikovsky
  Manish Sogi
  Mihai Moldovan
  Mingye Wang
  Nazar Mokrynskyi
  Peter Mattern
  Peter Meerwald
  Pierre Ossman
  Ross Burton
  Shawn Walker
  Takashi Iwai
  Tanu Kaskinen
  Thomas Petazzoni
  Yuri Chornoivan
  Zbigniew Kempczyński
  jungsup lee


PulseAudio 7.0

Changes at a glance:

  * LFE channel synthesis with low-pass filtering
  * New libsoxr based resamplers
  * Socket activation support for TCP
  * The "srbchannel" IPC mechanism enabled by default
  * More flexible jack detection support when using UCM
  * Exiting due to SIGTERM isn't considered a failure
  * Better support for Creative SoundBlaster Omni Surround 5.1

Detailed change log:

  http://www.freedesktop.org/wiki/Software/PulseAudio/Notes/7.0

Contributors:

  Ahmed S. Darwish
  Alexander E. Patrakov
  Andrey Semashev
  Andrey Zholos
  Arun Raghavan
  Cédric Valmary
  David Henningsson
  David Herrmann
  Deepak Srivastava
  Dušan Kazik
  Felipe Sateler
  Hui Wang
  Jaska Uimonen
  Juho Hämäläinen
  Manish Sogi
  Michael Cree
  Milo Casagrande
  Muhammet Kara
  Nazar Mokrynskyi
  Paul Menzel
  Peter Meerwald
  Pierre Ossman
  Piotr Drąg
  René J. V. Bertin
  Sagar Nageshmurthy
  Shawn Walker
  Sukesh Adiga
  Takashi Sakamoto
  Tanu Kaskinen
  Ville Skyttä
  Wim Taymans
  Yuri Chornoivan
  Zavadovsky Yan


PulseAudio 6.0

Changes at a glance:

  * Bluez 5 HSP (headset profile) support, both with and without oFono
  * SystemD socket activation support
  * Better support for multichannel and 2.1 profiles
  * Protocol optimisations
  * Remap optimisations
  * Lots of other enhancements, bug fixes, and documentation and i18n updates

Detailed change log:

  http://www.freedesktop.org/wiki/Software/PulseAudio/Notes/6.0

Contributors:

  Alexander Couzens
  Alexander E. Patrakov
  Alexander Kurtz
  Alexander Potashev
  Andrey Semashev
  Arun Raghavan
  Bradley Broom
  Cheng-Chia Tseng
  Colin Guthrie
  David Henningsson
  Dimitris Spingos
  Dušan Kazik
  Evan Nemerson
  Felipe Sateler
  Georg Chini
  Hemanth Meenakshisundaram
  Hui Wang
  James Bunton
  Jason Newton
  Javier Jardón
  Joe Marcus Clarke
  Josef Andersson
  João Paulo Rechi Vita
  Juho Hämäläinen
  kozdincer
  Luiz Augusto von Dentz
  Lukas Peleska
  Lukasz Marek
  Mauro Guerrera
  Michał Górny
  Niels Ole Salscheider
  Pali Rohár
  Parin Porecha
  Pete Beardmore
  Peter Meerwald
  Peter Ujfalusi
  Pierre Ossman
  Piotr Drąg
  poljar (Damir Jelić)
  Rafael Ferreira
  Rex Dieter
  Ricardo Salveti de Araujo
  Sajeesh Sidharthan
  Sjoerd Simons
  Tanu Kaskinen
  Wim Taymans
  Yuri Chornoivan


PulseAudio 5.0

Changes at a glance:

  * BlueZ 5 support (A2DP only)
  * Reimplementation of the tunnel modules
  * Native log target support for systemd-journal
  * Resampler refactoring
  * --monitor-stream option for parecord and parec
  * "latency_msec" argument for module-rtp-recv
  * "inhibit_auto_suspend" argument for module-rtp-send
  * "auto" argument for module-tunnel-sink and module-tunnel-source
  * Removed module-bluetooth-proximity
  * Jack detection for line out
  * Laptop internal surround speaker volume support
  * Improved float->s16 and s16->float sample conversion for ARM NEON
  * "Available" flag for card profiles
  * Removed module-dbus-protocol from the default configuration
  * Lots of other enhancements, bug fixes, and documentation and i18n updates

Detailed change log:

  http://www.freedesktop.org/wiki/Software/PulseAudio/Notes/5.0

Contributors:

  Alexander Couzens
  Alexander E. Patrakov
  Alexander Potashev
  Anton Lundin
  Arun Raghavan
  Ben Brewer
  Cheng-Chia Tseng
  Colin Guthrie
  Damir Jelić
  Daniel Albers
  David Henningsson
  Dominique Leuenberger
  Héctor Martín
  Ismo Puustinen
  Jan Alexander Steffens
  Javier Jardón
  João Paulo Rechi Vita
  Juho Hämäläinen
  Kenneth Perry
  Kerrick Staley
  Kiran Krishnappa
  Koop Mast
  Lars-Peter Clausen
  Laurentiu Nicola
  Luiz Augusto von Dentz
  Magnus Ekhall
  Mikel Astiz
  Mitchell Fang
  Nikolay Amiantov
  Parin Porecha
  Peter Meerwald
  Pierre Ossman
  Piotr Drąg
  Rafael Ferreira
  Ryan Lortie
  Scott Reeves
  Shuai Fan
  Stefan Sperling
  Tanu Kaskinen
  Wim Taymans
  Yuri Chornoivan
  victory


PulseAudio 4.0

Changes at a glance:

  * Better handling of low latency requests
  * Optimisations while mixing (generic, ARM NEON)
  * Default resampler is now speex-float-1 (lower CPU usage)
  * Major Bluetooth refactoring for better reliability and easier maintenance
  * Fixes for graceful hand-off to/from JACK
  * New module to apply ducking based on stream roles
  * Echo canceller infrastructure fixes
  * Bash and zsh completion for command line tools
  * Solaris and OS X fixes
  * Lots of other enhancements, bug fixes, and documentation and i18n updates

Detailed change log:

  http://www.freedesktop.org/wiki/Software/PulseAudio/Notes/4.0

Contributors:

  Akihiro Tsukada
  Albert Zeyer
  Arun Raghavan
  Christoph Gysin
  David Henningsson
  Flavio Ceolin
  Jarkko Suontausta
  Javier Jardón
  João Paulo Rechi Vita
  Jyri Sarha
  Luiz Augusto von Dentz
  Maarten Lankhorst
  Marc-Antoine Perennou
  Martin Pitt
  Matěj Laitl
  Mikel Astiz
  Paul Meng
  Peter Meerwald
  Peter Nelson
  Piotr Drąg
  poljar (Damir Jelić)
  Robin H. Johnson
  Stefan Huber
  Ștefan Săftescu
  Tanu Kaskinen
  Vadim Troshchinskiy
  Wang Xingchao
  Yuri Chornoivan

----

Change logs for previous versions can be seen at:

  http://www.freedesktop.org/wiki/Software/PulseAudio/Notes
