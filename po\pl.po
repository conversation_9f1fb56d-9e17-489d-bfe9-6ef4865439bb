# Polish translation for pulseaudio.
# Copyright © 2008-2019 the pulseaudio authors.
# This file is distributed under the same license as the pulseaudio package.
# <PERSON>otr <PERSON> <<EMAIL>>, 2008, 2012-2019.
#
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2022-05-21 10:06+0000\n"
"Last-Translator: Piotr <PERSON>g <<EMAIL>>\n"
"Language-Team: Polish <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/pl/>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.12.2\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [opcje]\n"
"\n"
"POLECENIA:\n"
"  -h, --help                            Wyświetla tę pomoc\n"
"      --version                         Wyświetla wersję\n"
"      --dump-conf                       Zrzuca domyślną konfigurację\n"
"      --dump-modules                    Zrzuca listę dostępnych modułów\n"
"      --dump-resample-methods           Zrzuca dostępne metody resamplingu\n"
"      --cleanup-shm                     Czyści stare fragmenty pamięci\n"
"                                        współdzielonej\n"
"      --start                           Uruchamia usługę, jeśli nie\n"
"                                        jest uruchomiona\n"
"  -k  --kill                            Niszczy uruchomioną usługę\n"
"      --check                           Sprawdza, czy usługa jest\n"
"                                        uruchomiona (zwraca tylko\n"
"                                        kod wyjścia)\n"
"\n"
"OPCJE:\n"
"      --system[=ZMIENNALOGICZNA]        Uruchamia w trybie systemowym\n"
"  -D, --daemonize[=ZMIENNALOGICZNA]     Tworzy usługę po uruchomieniu\n"
"      --fail[=ZMIENNALOGICZNA]          Wyłącza, kiedy uruchomienie\n"
"                                        się nie powiedzie\n"
"      --high-priority[=ZMIENNALOGICZNA] Próbuje ustawić wysoki poziom nice\n"
"                                        (dostępne tylko jako root, na SUID\n"
"                                        lub z podniesionym RLIMIT_NICE)\n"
"      --realtime[=ZMIENNALOGICZNA]      Próbuje ustawić szeregowanie\n"
"                                        w czasie rzeczywistym\n"
"                                        (dostępne tylko jako root, na SUID\n"
"                                        lub z podniesionym RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=ZMIENNALOGICZNA] Nie zezwala użytkownikowi\n"
"                                        na żądanie wczytania/usunięcia\n"
"                                        modułu po uruchomieniu\n"
"      --disallow-exit[=ZMIENNALOGICZNA] Nie zezwala użytkownikowi\n"
"                                        na żądanie wyłączenia\n"
"      --exit-idle-time=SEKUNDY          Kończy usługę, kiedy jest bezczynna\n"
"                                        i upłynął podany czas\n"
"      --scache-idle-time=SEKUNDY        Usuwa automatycznie wczytane\n"
"                                        próbki, kiedy jest bezczynny\n"
"                                        i upłynął podany czas\n"
"      --log-level[=POZIOM]              Zwiększa lub ustawia poziom\n"
"                                        wyświetlanych informacji\n"
"  -v  --verbose                         Zwiększa poziom wyświetlanych\n"
"                                        informacji\n"
"      --log-target={auto,syslog,stderr,file:ŚCIEŻKA,newfile:ŚCIEŻKA}\n"
"                                        Określa dziennik docelowy\n"
"      --log-meta[=ZMIENNALOGICZNA]      Dołącza położenie kodu\n"
"                                        do komunikatów dziennika\n"
"      --log-time[=ZMIENNALOGICZNA]      Dołącza czas w komunikatach\n"
"                                        dziennika\n"
"      --log-backtrace=RAMKI             Dołącza błąd w komunikatach\n"
"                                        dziennika\n"
"  -p, --dl-search-path=ŚCIEŻKA          Ustawia ścieżkę wyszukiwania dla\n"
"                                        dynamicznie współdzielonych\n"
"                                        obiektów (wtyczek)\n"
"      --resample-method=METODA          Używa podanej metody resamplingu\n"
"                                        (zobacz --dump-resample-methods,\n"
"                                        aby poznać możliwe wartości)\n"
"      --use-pid-file[=ZMIENNALOGICZNA]  Tworzy plik PID\n"
"      --no-cpu-limit[=ZMIENNALOGICZNA]  Nie instaluje ograniczenia zasobów\n"
"                                        procesora na obsługujących\n"
"                                        je platformach.\n"
"      --disable-shm[=ZMIENNALOGICZNA]   Wyłącza obsługę pamięci\n"
"                                        współdzielonej.\n"
"      --enable-memfd[=ZMIENNALOGICZNA]  Włącza obsługę pamięci\n"
"                                        współdzielonej memfd.\n"
"\n"
"SKRYPT STARTOWY:\n"
"  -L, --load=\"PARAMETRY MODUŁU\"         Wczytuje podany moduł wtyczki\n"
"                                        z podanym parametrem\n"
"  -F, --file=NAZWAPLIKU                 Wykonuje podany skrypt\n"
"  -C                                    Otwiera wiersz poleceń na\n"
"                                        uruchomionym TTY po uruchomieniu\n"
"\n"
"  -n                                    Nie wczytuje domyślnego\n"
"                                        pliku skryptu\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:265
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level oczekuje parametru poziomu dziennika (numeryczny w zakresie 0..4 "
"lub jeden z error, warn, notice, info, debug)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Nieprawidłowy dziennik docelowy: należy użyć „syslog”, „journal”, „stderr”, "
"„auto” lub prawidłowej nazwy pliku „file:<ścieżka>”, „newfile:<ścieżka>”."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Nieprawidłowy dziennik docelowy: należy użyć „syslog”, „stderr”, „auto” lub "
"prawidłowej nazwy pliku „file:<ścieżka>”, „newfile:<ścieżka>”."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Nieprawidłowa metoda resamplingu „%s”."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm oczekuje parametru zmiennej logicznej"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd oczekuje parametru zmiennej logicznej"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Nieprawidłowy dziennik docelowy „%s”."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Nieprawidłowy poziom dziennika „%s”."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Nieprawidłowa metoda resamplingu „%s”."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] Nieprawidłowy rlimit „%s”."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Nieprawidłowy format próbki „%s”."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Nieprawidłowa częstotliwość próbki „%s”."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Nieprawidłowe kanały próbki „%s”."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Nieprawidłowa mapa kanałów „%s”."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Nieprawidłowa liczba fragmentów „%s”."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Nieprawidłowy rozmiar fragmentu „%s”."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Nieprawidłowy poziom nice „%s”."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Nieprawidłowy typ serwera „%s”."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Otwarcie pliku konfiguracji się nie powiodło: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"Podana domyślna mapa kanałów ma inną liczbę kanałów niż podana domyślna "
"liczba kanałów."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Odczytano z pliku konfiguracji: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Nazwa: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Brak dostępnych informacji o module\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Wersja: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Opis: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Autor: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Użycie: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Wczytanie jednorazowe: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "OSTRZEŻENIE O PRZESTARZAŁOŚCI: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Ścieżka: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "Otwarcie modułu %s się nie powiodło: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr ""
"Odnalezienie pierwotnego programu wczytującego lt_dlopen się nie powiodło."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Przydzielenie nowego programu wczytującego dl się nie powiodło."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Dodanie bind-now-loader się nie powiodło."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Odnalezienie użytkownika „%s” się nie powiodło."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Odnalezienie grupy „%s” się nie powiodło."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "GID użytkownika „%s” i grupy „%s” się nie zgadzają."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "Katalogiem domowym użytkownika „%s” nie jest „%s”, ignorowanie."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Utworzenie „%s” się nie powiodło: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Zmiana listy grup się nie powiodła: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Zmiana GID się nie powiodła: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Zmiana UID się nie powiodła: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "Tryb systemowy nie jest obsługiwany na tej platformie."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Przetworzenie wiersza poleceń się nie powiodło."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Odmówiono trybu systemowego dla użytkownika niebędącego rootem. Uruchamianie "
"tylko usługi wyszukiwania serwera D-Bus."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Zniszczenie usługi się nie powiodło: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Ten program nie powinien być uruchamiany jako root (chyba że podano opcję --"
"system)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Wymagane są uprawnienia roota."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start nie jest obsługiwane przy uruchamianiu systemowym."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""
"Serwer skonfigurowany przez użytkownika w %s, odmawianie uruchomienia/"
"automatycznego wznowienia."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Serwer skonfigurowany przez użytkownika w %s, który jest lokalny. Dalsze "
"wykrywanie."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr ""
"Uruchamianie w trybie systemowym, ale --disallow-exit nie jest ustawione."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"Uruchamianie w trybie systemowym, ale --disallow-module-loading nie jest "
"ustawione."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "Uruchamianie w trybie systemowym, wymuszanie wyłączenia trybu SHM."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"Uruchamianie w trybie systemowym, wymuszanie wyłączenia czasu bezczynności "
"przed zakończeniem."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Uzyskanie standardowego wejścia/wyjścia się nie powiodło."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() się nie powiodło: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() się nie powiodło: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() się nie powiodło: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Uruchomienie usługi się nie powiodło."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() się nie powiodło: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Uzyskanie identyfikatora komputera się nie powiodło"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"PulseAudio jest uruchomione w trybie systemowym. Proszę się upewnić, że na "
"pewno tak ma być.\n"
"Proszę przeczytać http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ , gdzie wyjaśniono, dlaczego "
"tryb systemowy jest zwykle złym pomysłem."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() się nie powiodło."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() się nie powiodło."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "parametry wiersza poleceń"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"Zainicjowanie usługi się nie powiodło z powodu błędów podczas wykonywania "
"poleceń startowych. Źródło poleceń: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr ""
"Uruchamianie usługi bez żadnych wczytanych modułów, odmawianie działania."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "System dźwięku PulseAudio"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Uruchomienie systemu dźwięku PulseAudio"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Wejście"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Wejście stacji dokującej"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Mikrofon stacji dokującej"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "Wejście liniowe stacji dokującej"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "Wejście liniowe"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Przedni mikrofon"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Tylny mikrofon"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Zewnętrzny mikrofon"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Wewnętrzny mikrofon"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Radio"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Wideo"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Automatyczne sterowanie natężeniem"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Brak automatycznego sterowania natężeniem"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Podbicie"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Brak podbicia"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Amplituner"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Brak amplitunera"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Podbicie basów"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Brak podbicia basów"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Głośnik"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Słuchawki"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Wejście analogowe"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Mikrofon stacji dokującej"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Mikrofon na słuchawkach"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Wyjście analogowe"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "Słuchawki 2"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "Wyjście mono słuchawek"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "Wyjście liniowe"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Analogowe wyjście mono"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Głośniki"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI/DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Wyjście cyfrowe (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Wejście cyfrowe (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "Wejście wielokanałowe"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "Wyjście wielokanałowe"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "Wyjście gry"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "Wyjście rozmowy"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "Wejście rozmowy"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "Wirtualne przestrzenne 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Analogowe mono"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "Analogowe mono (lewy)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "Analogowe mono (prawy)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Analogowe stereo"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Mono"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Stereo"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Słuchawki z mikrofonem"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "Telefon głośnomówiący"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Wielokanałowe"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Analogowe przestrzenne 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Analogowe przestrzenne 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Analogowe przestrzenne 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Analogowe przestrzenne 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Analogowe przestrzenne 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Analogowe przestrzenne 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Analogowe przestrzenne 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Analogowe przestrzenne 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Analogowe przestrzenne 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Analogowe przestrzenne 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Analogowe przestrzenne 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Cyfrowe stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Cyfrowe przestrzenne 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Cyfrowe przestrzenne 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Cyfrowe przestrzenne 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Cyfrowe stereo (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Cyfrowe przestrzenne 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "Rozmowa"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "Gra"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Analogowy dupleks mono"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Analogowy dupleks stereo"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Cyfrowy dupleks stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "Dupleks wielokanałowy"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "Dupleks stereo"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "Rozmowa mono + przestrzenne 7.1"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Wyłączone"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "Wyjście %s"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "Wejście %s"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"Usługa ALSA została wybudzona, aby zapisać nowe dane do urządzenia, ale nie "
"było nic do zapisania.\n"
"Prawdopodobnie jest to błąd w sterowniku ALSA „%s”. Proszę zgłosić ten "
"problem programistom usługi ALSA.\n"
"Wybudzono za pomocą ustawienia POLLOUT — ale jednoczesne wywołanie "
"snd_pcm_avail() zwróciło zero lub inną wartość < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"Usługa ALSA została wybudzona, aby odczytać nowe dane z urządzenia, ale nie "
"było nic do odczytania.\n"
"Prawdopodobnie jest to błąd w sterowniku ALSA „%s”. Proszę zgłosić ten "
"problem programistom usługi ALSA.\n"
"Wybudzono za pomocą ustawienia POLLIN — ale jednoczesne wywołanie "
"snd_pcm_avail() zwróciło zero lub inną wartość < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() zwróciło wyjątkowo dużą wartość: %lu bajt (%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."
msgstr[1] ""
"snd_pcm_avail() zwróciło wyjątkowo dużą wartość: %lu bajty (%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."
msgstr[2] ""
"snd_pcm_avail() zwróciło wyjątkowo dużą wartość: %lu bajtów (%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() zwróciło wyjątkowo dużą wartość: %li bajt (%s%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."
msgstr[1] ""
"snd_pcm_delay() zwróciło wyjątkowo dużą wartość: %li bajty (%s%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."
msgstr[2] ""
"snd_pcm_delay() zwróciło wyjątkowo dużą wartość: %li bajtów (%s%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() zwróciło dziwne wartości: opóźnienie %lu jest mniejsze "
"niż korzyść %lu.\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() zwróciło wyjątkowo dużą wartość: %lu bajt (%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."
msgstr[1] ""
"snd_pcm_mmap_begin() zwróciło wyjątkowo dużą wartość: %lu bajty (%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."
msgstr[2] ""
"snd_pcm_mmap_begin() zwróciło wyjątkowo dużą wartość: %lu bajtów (%lu ms).\n"
"Prawdopodobnie jest to błąd sterownika ALSA „%s”. Proszę zgłosić ten problem "
"programistom usługi ALSA."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Wejście Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Wyjście Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Zestaw głośnomówiący"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Słuchawki"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Przenośne"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Samochód"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "HiFi"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Telefon"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "Odtwarzanie o wysokiej dokładności (odpływ A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "Przechwytywanie o wysokiej dokładności (źródło A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "Jednostka główna słuchawek z mikrofonem (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "Zestaw słuchawek z mikrofonem (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "Jednostka główna zestawu głośnomówiącego (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Zestaw głośnomówiący (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<nazwa źródła> source_properties=<właściwości źródła> "
"source_master=<nazwa źródła do filtrowania> sink_name=<nazwa odpływu> "
"sink_properties=<właściwości odpływu> sink_master=<nazwa odpływu do "
"filtrowania> adjust_time=<jak często odczytywać częstotliwości w sekundach> "
"adjust_threshold=<jak daleko odchodzić do odczytania w milisekundach> "
"format=<format próbki> rate=<częstotliwość próbki> channels=<liczba kanałów> "
"channel_map=<map kanałów> aec_method=<używana implementacja> "
"aec_args=<parametry dla mechanizmu AEC> save_aec=<zapisywanie danych AEC w /"
"tmp> autoloaded=<należy ustawić, jeśli moduł jest automatycznie wczytywany> "
"use_volume_sharing=<yes lub no> use_master_format=<yes lub no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "Włączone"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Głuche wyjście"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr ""
"Utrzymywanie zawsze co najmniej jednego wczytanego odpływu, nawet jeśli to "
"pusty odpływ"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr ""
"Utrzymywanie zawsze co najmniej jednego wczytanego źródła, nawet jeśli to "
"puste źródło"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "Korektor graficzny ogólnego przeznaczenia"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<nazwa odpływu> sink_properties=<właściwości odpływu> "
"sink_master=<nazwa odpływu do filtrowania> format=<format próbki> "
"rate=<częstotliwość próbki> channels=<liczba kanałów> channel_map=<mapa "
"kanałów> autoloaded=<należy ustawić, jeśli ten moduł jest automatycznie "
"uruchamiany> use_volume_sharing=<yes lub no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "Korektor graficzny na podstawie FFT na %s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<automatycznie usuwać nieużywane filtry?>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Wirtualny odpływ LADSPA"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<nazwa odpływu> sink_properties=<właściwości odpływu> "
"sink_input_properties=<właściwości wejścia odpływu> master=<nazwa odpływu do "
"filtrowania> sink_master=<nazwa odpływu do filtrowania> format=<format "
"próbki> rate=<częstotliwość próbki> channels=<liczba kanałów> "
"channel_map=<mapa kanałów wejściowych> plugin=<nazwa wtyczki ladspa> "
"label=<etykieta wtyczki ladspa> control=<lista wartości sterowania wejściem "
"oddzielona przecinkami> input_ladspaport_map=<lista nazw wejściowych portów "
"LADSPA oddzielona przecinkami> output_ladspaport_map=<lista nazw wyjściowych "
"portów LADSPA oddzielona przecinkami> autoloaded=<należy ustawić, jeśli ten "
"moduł jest wczytywany automatycznie> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Zegarowy PUSTY odpływ"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Puste wyjście"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Ustawienie formatu się nie powiodło: nieprawidłowy ciąg formatu %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Urządzenia wyjściowe"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Urządzenia wejściowe"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "Dźwięk na @HOSTNAME@"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "Tunel dla %s@%s"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "Tunel do %s/%s"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Wirtualny odpływ przestrzenny"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<nazwa odpływu> sink_properties=<właściwości odpływu> "
"master=<nazwa odpływu do filtrowania> sink_master=<nazwa odpływu do "
"filtrowania> format=<format próbki> rate=<częstotliwość próbki> "
"channels=<liczba kanałów> channel_map=<mapa kanałów> use_volume_sharing=<yes "
"lub no> force_flat_volume=<yes lub no> hrir=/ścieżka/do/pliku/left_hrir.wav "
"hrir_left=/ścieżka/do/pliku/left_hrir.wav hrir_right=/ścieżka/do/"
"opcjonalnego/pliku/right_hrir.wav autoloaded=<należy ustawić, jeśli ten "
"moduł jest wczytywany automatycznie> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "Nieznany model urządzenia"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "Standardowy profil RAOP"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "Serwer dźwięku PulseAudio"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Przedni środkowy"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Przedni lewy"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Przedni prawy"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Tylny środkowy"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Tylny lewy"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Tylny prawy"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Głośnik niskotonowy"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Przedni lewy pośrodku"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Przedni prawy pośrodku"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Boczny lewy"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Boczny prawy"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "0. pomocniczy"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "1. pomocniczy"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "2. pomocniczy"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "3. pomocniczy"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "4. pomocniczy"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "5. pomocniczy"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "6. pomocniczy"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "7. pomocniczy"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "8. pomocniczy"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "9. pomocniczy"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "10. pomocniczy"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "11. pomocniczy"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "12. pomocniczy"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "13. pomocniczy"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "14. pomocniczy"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "15. pomocniczy"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "16. pomocniczy"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "17. pomocniczy"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "18. pomocniczy"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "19. pomocniczy"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "20. pomocniczy"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "21. pomocniczy"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "22. pomocniczy"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "23. pomocniczy"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "24. pomocniczy"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "25. pomocniczy"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "26. pomocniczy"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "27. pomocniczy"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "28. pomocniczy"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "29. pomocniczy"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "30. pomocniczy"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "31. pomocniczy"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Górny środkowy"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Górny przedni środkowy"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Górny przedni lewy"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Górny przedni prawy"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Górny tylny środkowy"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Górny tylny lewy"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Górny tylny prawy"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(nieprawidłowe)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Przestrzenne 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Przestrzenne 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Przestrzenne 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Przestrzenne 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Przestrzenne 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() się nie powiodło: %s"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() zwróciło wartość „true”"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Przetworzenie danych ciasteczka się nie powiodło"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Otrzymano komunikat dla nieznanego rozszerzenia „%s”"

#: src/pulse/direction.c:37
msgid "input"
msgstr "wejście"

#: src/pulse/direction.c:39
msgid "output"
msgstr "wyjście"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "dwukierunkowe"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "nieprawidłowe"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"Właścicielem XDG_RUNTIME_DIR (%s) nie jest UID %d, ale UID %d (może to być "
"spowodowane próbą połączenia do kopii PulseAudio niebędącej rootem jako "
"użytkownik root przez natywny protokół, czego nie należy robić)."

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "tak"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "nie"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Nie można uzyskać dostępu do blokady automatycznego wznawiania."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Otwarcie pliku docelowego „%s” się nie powiodło."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Próbowano otworzyć plik docelowy „%s”, „%s.1”, „%s.2” … „%s.%d”, ale "
"wszystkie się nie powiodły."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Nieprawidłowy dziennik docelowy."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Wbudowany dźwięk"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Odmowa dostępu"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Nieznane polecenie"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Nieprawidłowy parametr"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "Jednostka istnieje"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Brak jednostki"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Odrzucono połączenie"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Błąd protokołu"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Przekroczono czas oczekiwania"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Brak klucza uwierzytelnienia"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Wewnętrzny błąd"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Zakończono połączenie"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Zniszczono jednostkę"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Nieprawidłowy serwer"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Zainicjowanie modułu się nie powiodło"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Błędny stan"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Brak danych"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Niezgodna wersja protokołu"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Za duże"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Nieobsługiwane"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Nieznany kod błędu"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Nie ma takiego rozszerzenia"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Przestarzała funkcjonalność"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Brak implementacji"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Rozdzielono klienta"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Błąd wejścia/wyjścia"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Urządzenie lub zasób jest zajęty"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %u k %u Hz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Opróżnienie potoku się nie powiodło: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Opróżniono potok odtwarzania."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Opróżnianie połączenia z serwerem."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() się nie powiodło: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() się nie powiodło: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Pomyślnie utworzono potok."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() się nie powiodło: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Metryka bufora: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Metryka bufora: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "Używanie przykładowego określenia „%s”, mapa kanałów „%s”."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Połączono z urządzeniem %s (indeks: %u, wstrzymane: %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Błąd potoku: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Wstrzymano urządzenie potoku.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Wznowiono urządzenie potoku.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Niedopełniony potok.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Przepełniony potok.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Utworzono potok.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Potok został przeniesiony do urządzenia %s (%u, %swstrzymane).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "nie "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Zmieniono atrybuty bufora potoku.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Stos żądań korka jest pusty: zatykanie potoku"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Stos żądań korka jest pusty: odtykanie potoku"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr "Ostrzeżenie: otrzymano więcej żądań odetkania niż żądań zatkania."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Nawiązano połączenie.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() się nie powiodło: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() się nie powiodło: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Ustawienie potoku monitora się nie powiodło: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() się nie powiodło: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Połączenie się nie powiodło: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "Otrzymano EOF."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() się nie powiodło: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() się nie powiodło: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Otrzymano sygnał, kończenie działania."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Uzyskanie opóźnienia się nie powiodło: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Czas: %0.3f s, opóźnienie: %0.0f us."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() się nie powiodło: %s"

#: src/utils/pacat.c:676
#, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [opcje]\n"
"%s\n"
"\n"
"  -h, --help                            Wyświetla tę pomoc\n"
"      --version                         Wyświetla wersję\n"
"\n"
"  -r, --record                          Tworzy połączenie do nagrywania\n"
"  -p, --playback                        Tworzy połączenie do odtwarzania\n"
"\n"
"  -v, --verbose                         Wyświetla więcej informacji\n"
"                                        o działaniu\n"
"\n"
"  -s, --server=SERWER                   Nazwa serwera do połączenia się\n"
"  -d, --device=URZĄDZENIE               Nazwa odpływu/źródła\n"
"                                        do połączenia się. Specjalne nazwy "
"@DEFAULT_SINK@, @DEFAULT_SOURCE@ i @DEFAULT_MONITOR@ mogą być używane do "
"podawania domyślnego odpływu, źródła i monitora.\n"
"  -n, --client-name=NAZWA               Jak nazywać tego klienta\n"
"                                        na serwerze\n"
"      --stream-name=NAZWA               Jak nazwać ten potok na serwerze\n"
"      --volume=POZIOMGŁOŚNOŚCI          Określa początkowy (liniowy)\n"
"                                        poziom głośności w zakresie\n"
"                                        0…65536\n"
"      --rate=CZĘSTOTLIWOŚĆPRÓBKI        Częstotliwość próbki w Hz\n"
"                                        (domyślnie 44100)\n"
"      --format=FORMATPRÓBKI             Format próbki,\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        zawiera listę możliwych wartości\n"
"                                        (domyślnie s16ne)\n"
"      --channels=KANAŁY                 Liczba kanałów, 1 dla mono, 2 dla\n"
"                                        stereo\n"
"                                        (domyślnie 2)\n"
"      --channel-map=MAPAKANAŁÓW         Mapa kanałów używa zamiast\n"
"                                        domyślnej\n"
"      --fix-format                      Pobiera format próbki z odpływu/\n"
"                                        źródła, z jakim połączony jest\n"
"                                        potok.\n"
"      --fix-rate                        Pobiera częstotliwość sampli\n"
"                                        z odpływu/źródła, z jakim połączony\n"
"                                        jest potok.\n"
"      --fix-channels                    Pobiera liczbę kanałów i mapę\n"
"                                        kanałów z odpływu/źródła, z jakim\n"
"                                        połączony jest potok.\n"
"      --no-remix                        Nie miesza kanałów w górę\n"
"                                        lub w dół.\n"
"      --no-remap                        Mapuje kanały przez indeks zamiast\n"
"                                        przez nazwę.\n"
"      --latency=BAJTY                   Żąda określonego opóźnienia\n"
"                                        w bajtach.\n"
"      --process-time=BAJTY              Żąda określonego czasu procesu\n"
"                                        na żądanie w bajtach.\n"
"      --latency-msec=MSEKUNDY           Żąda określonego opóźnienia\n"
"                                        w milisekundach.\n"
"      --process-time-msec=MSEKUNDY      Żąda określonego czasu procesu\n"
"                                        na żądanie w milisekundach.\n"
"      --property=WŁAŚCIWOŚĆ=WARTOŚĆ     Ustawia podaną właściwość na podaną\n"
"                                        wartość.\n"
"      --raw                             Nagrywa/odtwarza surowe dane PCM.\n"
"      --passthrough                     Przekazuje dane.\n"
"      --file-format=[=FFORMAT]          Nagrywa/odtwarza sformatowane dane\n"
"                                        PCM.\n"
"      --list-file-formats               Wyświetla listę dostępnych formatów\n"
"                                        plików.\n"
"      --monitor-stream=INDEKS           Nagrywa z odpływu wejścia\n"
"                                        o INDEKSIE.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""
"Odtwarza zakodowane pliki dźwiękowe za pomocą serwera dźwięku PulseAudio."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""
"Przechwytuje dane dźwiękowe z serwera dźwięku PulseAudio i zapisuje je do "
"pliku."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"Przechwytuje dane dźwiękowe z serwera dźwięku PulseAudio i zapisuje je do "
"STANDARDOWEGO-WYJŚCIA lub podanego pliku."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"Odtwarza dane dźwiękowe ze STANDARDOWEGO-WEJŚCIA lub podanego pliku za "
"pomocą serwera dźwięku PulseAudio."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Skompilowane za pomocą libpulse %s\n"
"Skonsolidowane za pomocą libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Nieprawidłowa nazwa klienta „%s”"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Nieprawidłowa nazwa potoku „%s”"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Nieprawidłowa mapa kanałów „%s”"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Nieprawidłowe określenie opóźnienia „%s”"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Nieprawidłowe określenie czasu procesu „%s”"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Nieprawidłowa właściwość „%s”"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Nieznany format pliku %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "Przetworzenie parametru dla --monitor-stream się nie powiodło"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Nieprawidłowe określenie próbki"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Za dużo parametrów."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Utworzenie określenia próbki dla pliku się nie powiodło."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Otwarcie pliku dźwiękowego się nie powiodło."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Ostrzeżenie: podane określenie próbki zostanie zastąpione przez określenie "
"z pliku."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Ustalenie określenia próbki z pliku nie się nie powiodło."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "Ostrzeżenie: ustalenie mapy kanałów z pliku się nie powiodło."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "Mapa kanałów nie zgadza się z określeniem próbki"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Ostrzeżenie: zapisanie mapy kanałów do pliku się nie powiodło."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Otwieranie potoku %s za pomocą określenie próbki „%s” i mapy kanałów „%s”."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "nagrywanie"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "odtwarzanie"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Ustawienie nazwy nośnika się nie powiodło."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() się nie powiodło."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() się nie powiodło."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() się nie powiodło."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() się nie powiodło: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() się nie powiodło."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() się nie powiodło."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "NAZWA [PARAMETRY…]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "NAZWA|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "NAZWA"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "NAZWA|#N GŁOŚNOŚĆ"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N GŁOŚNOŚĆ"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "NAZWA|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "NAZWA|#N KLUCZ=WARTOŚĆ"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N KLUCZ=WARTOŚĆ"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "NAZWA ODPŁYW|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "NAZWA NAZWA-PLIKU"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "NAZWA-ŚCIEŻKI"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "NAZWA-PLIKU ODPŁYW|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N ODPŁYW|ŹRÓDŁO"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "PROFIL KARTY"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "NAZWA|#N PORT"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "NAZWA-KARTY|KARTA-#N PORT OFFSET"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "CEL"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "POZIOM-NUMERYCZNY"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "RAMKI"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "ODBIORCA KOMUNIKAT [PARAMETRY_KOMUNIKATU]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Wyświetla tę pomoc\n"
"      --version                         Wyświetla wersję\n"
"Jeśli nie podano polecenia, to program pacmd zostaje uruchomiony w trybie "
"interaktywnym.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"Skompilowane za pomocą libpulse %s\n"
"Skonsolidowane za pomocą libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"Usługa PulseAudio nie jest uruchomiona, lub nie jest uruchomiona jako usługa "
"sesji."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "gniazdo(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Zniszczenie usługi PulseAudio się nie powiodło."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Usługa nie odpowiada."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Uzyskanie statystyk się nie powiodło: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Obecnie używane: %u blok zawierający %s B.\n"
msgstr[1] "Obecnie używane: %u bloki zawierające razem %s B.\n"
msgstr[2] "Obecnie używane: %u bloków zawierających razem %s B.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] ""
"Przydzielono podczas całego czasu uruchomienia: %u blok zawierający %s B.\n"
msgstr[1] ""
"Przydzielono podczas całego czasu uruchomienia: %u bloki zawierające razem "
"%s B.\n"
msgstr[2] ""
"Przydzielono podczas całego czasu uruchomienia: %u bloków zawierających "
"razem %s B.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Rozmiar pamięci podręcznej próbek: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Uzyskanie informacji o serwerze się nie powiodło: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr "%s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"Ciąg serwera: %s\n"
"Wersja protokołu biblioteki: %u\n"
"Wersja protokołu serwera: %u\n"
"Czy jest lokalny: %s\n"
"Indeks klienta: %u\n"
"Rozmiar kafla: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Nazwa użytkownika: %s\n"
"Nazwa komputera: %s\n"
"Nazwa serwera: %s\n"
"Wersja serwera: %s\n"
"Domyślne określenie próbki: %s\n"
"Domyślna mapa kanałów: %s\n"
"Domyślny odpływ: %s\n"
"Domyślne źródło: %s\n"
"Ciasteczko: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "dostępność jest nieznana"

#: src/utils/pactl.c:321
msgid "available"
msgstr "dostępne"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "niedostępne"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "Nieznany"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Pomocniczy"

#: src/utils/pactl.c:335
msgid "Line"
msgstr "Liniowy"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Mikrofon"

#: src/utils/pactl.c:338
msgid "Handset"
msgstr "Słuchawka telefonu"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "Słuchawka douszna"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "S/PDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "Telewizor"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "Bluetooth"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "Sieć"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "Analogowe"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Uzyskanie informacji o odpływie się nie powiodło: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. odpływ\n"
"\tStan: %s\n"
"\tNazwa: %s\n"
"\tOpis: %s\n"
"\tSterownik: %s\n"
"\tOkreślenie próbki: %s\n"
"\tMapa kanałów: %s\n"
"\tWłaściciel modułu: %u\n"
"\tWyciszenie: %s\n"
"\tPoziom głośności: %s\n"
"\t                  balans %0.2f\n"
"\tGłośność podstawowa: %s\n"
"\tŹródło monitora: %s\n"
"\tOpóźnienie: %0.0f us, skonfigurowano %0.0f us\n"
"\tFlagi: %s%s%s%s%s%s%s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tPorty:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (typ: %s, priorytet: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ", grupa dostępności: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tAktywny port: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tFormaty:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Uzyskanie informacji o źródle się nie powiodło: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. źródło\n"
"\tStan: %s\n"
"\tNazwa: %s\n"
"\tOpis: %s\n"
"\tSterownik: %s\n"
"\tOkreślenie próbki: %s\n"
"\tMapa kanałów: %s\n"
"\tWłaściciel modułu: %u\n"
"\tWyciszenie: %s\n"
"\tPoziom głośności: %s\n"
"\t                  balans %0.2f\n"
"\tGłośność podstawowa: %s\n"
"\tMonitor odpływu: %s\n"
"\tOpóźnienie: %0.0f us, skonfigurowano %0.0f us\n"
"\tFlagi: %s%s%s%s%s%s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "nie dotyczy"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Uzyskanie informacji o module się nie powiodło: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. moduł\n"
"\tNazwa: %s\n"
"\tParametr: %s\n"
"\tLicznik użycia: %s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Uzyskanie informacji o kliencie się nie powiodło: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. klient\n"
"\tSterownik: %s\n"
"\tWłaściciel modułu: %s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Uzyskanie informacji o karcie się nie powiodło: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. karta\n"
"\tNazwa: %s\n"
"\tSterownik: %s\n"
"\tWłaściciel modułu: %s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tProfile:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr "\t\t%s: %s (odpływy: %u, źródła: %u, priorytet: %u, dostępne: %s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tAktywny profil: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (typ: %s, priorytet: %u, offset opóźnienia: %<PRId64> us%s%s, "
"%s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tWłaściwości:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tCzęść profilu: %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Uzyskanie informacji o odpływie wejścia się nie powiodło: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. odpływ wejścia\n"
"\tSterownik: %s\n"
"\tWłaściciel modułu: %s\n"
"\tKlient: %s\n"
"\tOdpływ: %u\n"
"\tOkreślenie próbki: %s\n"
"\tMapa kanałów: %s\n"
"\tFormat: %s\n"
"\tZakorkowane: %s\n"
"\tWyciszenie: %s\n"
"\tPoziom głośności: %s\n"
"\t                  balans %0.2f\n"
"\tOpóźnienie bufora: %0.0f us\n"
"\tOpóźnienie odpływu: %0.0f us\n"
"\tMetoda resamplingu: %s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "Uzyskanie informacji o wyjściu źródła się nie powiodło: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. odpływ wejścia\n"
"\tSterownik: %s\n"
"\tWłaściciel modułu: %s\n"
"\tKlient: %s\n"
"\tOdpływ: %u\n"
"\tOkreślenie próbki: %s\n"
"\tMapa kanałów: %s\n"
"\tFormat: %s\n"
"\tZakorkowane: %s\n"
"\tWyciszenie: %s\n"
"\tPoziom głośności: %s\n"
"\t                  balans %0.2f\n"
"\tOpóźnienie bufora: %0.0f us\n"
"\tOpóźnienie odpływu: %0.0f us\n"
"\tMetoda resamplingu: %s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Uzyskanie informacji o próbce się nie powiodło: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"%u. próbka\n"
"\tNazwa: %s\n"
"\tOkreślenie próbki: %s\n"
"\tMapa kanałów: %s\n"
"\tPoziom głośności: %s\n"
"\t                  balans %0.2f\n"
"\tCzas trwania: %0.1f s\n"
"\tRozmiar: %s\n"
"\tLazy: %s\n"
"\tNazwa pliku: %s\n"
"\tWłaściwości:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Niepowodzenie: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "Komunikat wysłania się nie powiódł: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr "Komunikat „list-handlers” się nie powiódł: %s"

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "nie można poprawnie przetworzyć odpowiedzi komunikatu „list-handlers”"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "odpowiedź komunikatu „list-handlers” nie jest macierzą JSON"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""
"Element %d macierzy odpowiedzi komunikatu „list-handlers” nie jest obiektem "
"JSON"

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr ""
"Usunięcie modułu z pamięci się nie powiodło: moduł %s nie jest wczytany"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"Uzyskanie głośności się nie powiodło: próbowano ustawić głośność dla %d "
"kanału, kiedy obsługiwane kanały = %d\n"
msgstr[1] ""
"Uzyskanie głośności się nie powiodło: próbowano ustawić głośność dla %d "
"kanałów, kiedy obsługiwane kanały = %d\n"
msgstr[2] ""
"Uzyskanie głośności się nie powiodło: próbowano ustawić głośność dla %d "
"kanałów, kiedy obsługiwane kanały = %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Wysłanie próbki się nie powiodło: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Przedwczesny koniec pliku"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "nowy"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "zmień"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "usuń"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "nieznany"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "odpływ"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "źródło"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "wejście-odpływu"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "wyjście-źródła"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "moduł"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "klient"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "bufor-próbki"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "serwer"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "karta"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "Zdarzenie „%s” w %s #%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "Otrzymano SIGINT, kończenie działania."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Nieprawidłowe określenie głośności"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "Głośność jest poza dozwolonym zakresem.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Nieprawidłowa liczba określeń głośności.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Niespójne określenie głośności.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[opcje]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[TYP]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "NAZWA-PLIKU [NAZWA]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "NAZWA [ODPŁYW]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "NAZWA|#N GŁOŚNOŚĆ [GŁOŚNOŚĆ…]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N GŁOŚNOŚĆ [GŁOŚNOŚĆ…]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "NAZWA|#N 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N FORMATY"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"Specjalne nazwy @DEFAULT_SINK@, @DEFAULT_SOURCE@ i @DEFAULT_MONITOR@\n"
"mogą być używane do podania domyślnego odpływu, źródła i monitora.\n"

#: src/utils/pactl.c:2664
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Wyświetla tę pomoc\n"
"      --version                         Wyświetla wersję\n"
"\n"
"  -f, --format=FORMAT                   Format wyjścia, „normal” lub „json”\n"
"  -s, --server=SERWER                   Nazwa serwera do połączenia się\n"
"  -n, --client-name=NAZWA               Jak nazwać tego klienta w serwerze\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Skompilowane za pomocą libpulse %s\n"
"Skonsolidowane za pomocą libpulse %s\n"

#: src/utils/pactl.c:2751
#, c-format
msgid "Invalid format value '%s'"
msgstr "Nieprawidłowa wartość formatu „%s”"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Należy podać nic lub jedno z: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Proszę podać plik próbki do wczytania"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Otwarcie pliku dźwiękowego się nie powiodło."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "Ostrzeżenie: ustalenie określenia próbki z pliku się nie powiodło."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Należy podać nazwę próbki do odtworzenia"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Należy podać nazwę próbki do usunięcia"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "Należy podać indeks odpływu wejścia i odpływ"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "Należy podać indeks źródła wyjścia i źródło"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Należy podać nazwę modułu i parametry."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "Należy podać indeks lub nazwę modułu"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Nie można podać więcej niż jednego odpływu. Należy podać wartość logiczną."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "Nieprawidłowe określenie wstrzymania."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Nie można podać więcej niż jednego źródła. Należy podać wartość logiczną."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Należy podać nazwę karty/indeks i nazwę profilu"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Należy podać nazwę odpływu/indeks i nazwę portu"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "Należy podać nazwę odpływu"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Należy podać nazwę źródła/indeks i nazwę portu"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "Należy podać nazwę źródła"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "Należy podać nazwę/indeks odpływu"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Należy podać nazwę odpływu/indeks i głośność"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "Należy podać nazwę/indeks źródła"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Należy podać nazwę źródła/indeks i głośność"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Należy podać indeks odpływu wejścia i głośność"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Nieprawidłowy indeks odpływ wejścia"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "Należy podać indeks źródła wyjścia i głośność"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Nieprawidłowy indeks wejścia źródła"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Należy podać nazwę odpływu/indeks i działanie wyciszenia (0, 1 lub „toggle”)"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Nieprawidłowe określenie wyciszenia"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Należy podać nazwę źródła/indeks i działanie wyciszenia (0, 1 lub „toggle”)"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Należy podać indeks odpływu wejścia i działanie wyciszenia (0, 1 lub "
"„toggle”)"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Nieprawidłowe określenie indeksu odpływu wejścia"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr ""
"Należy podać nazwę indeks wyjścia źródła i działanie wyciszenia (0, 1 lub "
"„toggle”)"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Nieprawidłowe określenie indeksu wyjścia źródła"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr "Należy podać co najmniej ścieżkę do obiektu i nazwę komunikatu"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"Podano nadmiarowe parametry, które zostaną zignorowane. Proszę pamiętać, że "
"wszystkie parametry komunikatu muszą być podawane jako jeden ciąg."

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"Należy podać nazwę indeks odpływu listę obsługiwanych formatów oddzielonych "
"średnikami"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "Należy podać nazwę karty/indeks, nazwę portu i offset opóźnienia"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Nie można przetworzyć offsetu opóźnienia"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Nie podano prawidłowego polecenia."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Wznowienie się nie powiodło: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Wstrzymanie się nie powiodło: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr ""
"OSTRZEŻENIE: serwer dźwięku nie jest lokalny, nie zostanie wstrzymany.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Połączenie się nie powiodło: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "Otrzymano SIGINT, kończenie działania.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "OSTRZEŻENIE: proces potomny został zakończony przez sygnał %u\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [opcje] — PROGRAM [PARAMETRY…]\n"
"\n"
"Tymczasowo wstrzymuje usługę PulseAudio podczas działania PROGRAMU.\n"
"\n"
"  -h, --help                            Wyświetla tę pomoc\n"
"      --version                         Wyświetla wersję\n"
"  -s, --server=SERWER                   Nazwa serwera do połączenia się\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Skompilowane za pomocą libpulse %s\n"
"Skonsolidowane za pomocą libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() się nie powiodło.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() się nie powiodło.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() się nie powiodło.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D ekran] [-S serwer] [-O odpływ] [-I źródło] [-c plik]  [-d|-e|-i|-r]\n"
"\n"
" -d    Wyświetla dane PulseAudio dołączone do ekranu X11 (domyślne)\n"
" -e    Eksportuje lokalne dane PulseAudio na ekran X11\n"
" -i    Importuje dane PulseAudio z ekranu X11 do lokalnych zmiennych\n"
"       środowiskowych i pliku ciasteczka.\n"
" -r    Usuwa dane PulseAudio z ekranu X11\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Przetworzenie wiersza poleceń się nie powiodło.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Serwer: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Źródło: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Odpływ: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Ciasteczko: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Przetworzenie danych ciasteczka się nie powiodło\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Zapisanie danych ciasteczka się nie powiodło\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Uzyskanie FQDN się nie powiodło.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Wczytanie danych ciasteczka się nie powiodło\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Niezaimplementowane.\n"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "Głośnik niskotonowy na oddzielnym wyjściu mono"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Cyfrowe przekazywanie (S/PDIF)"

#~ msgid "Digital Passthrough (IEC958)"
#~ msgstr "Cyfrowe przekazywanie (IEC958)"
