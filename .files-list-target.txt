./usr/include/pulse/subscribe.h
./usr/include/pulse/mainloop-signal.h
./usr/include/pulse/sample.h
./usr/include/pulse/volume.h
./usr/include/pulse/error.h
./usr/include/pulse/direction.h
./usr/include/pulse/mainloop-api.h
./usr/include/pulse/scache.h
./usr/include/pulse/context.h
./usr/include/pulse/util.h
./usr/include/pulse/simple.h
./usr/include/pulse/timeval.h
./usr/include/pulse/stream.h
./usr/include/pulse/pulseaudio.h
./usr/include/pulse/ext-device-manager.h
./usr/include/pulse/format.h
./usr/include/pulse/ext-stream-restore.h
./usr/include/pulse/cdecl.h
./usr/include/pulse/thread-mainloop.h
./usr/include/pulse/proplist.h
./usr/include/pulse/operation.h
./usr/include/pulse/ext-device-restore.h
./usr/include/pulse/glib-mainloop.h
./usr/include/pulse/xmalloc.h
./usr/include/pulse/utf8.h
./usr/include/pulse/rtclock.h
./usr/include/pulse/gccmacro.h
./usr/include/pulse/def.h
./usr/include/pulse/introspect.h
./usr/include/pulse/channelmap.h
./usr/include/pulse/mainloop.h
./usr/include/pulse/version.h
./usr/share/zsh/site-functions/_pulseaudio
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-1.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-mic-line.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output-lineout.conf
./usr/share/pulseaudio/alsa-mixer/paths/iec958-stereo-output.conf
./usr/share/pulseaudio/alsa-mixer/paths/virtual-surround-7.1.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output-chat.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-linein.conf
./usr/share/pulseaudio/alsa-mixer/paths/usb-gaming-headset-input.conf
./usr/share/pulseaudio/alsa-mixer/paths/steelseries-arctis-output-chat-common.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-0.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-7.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-mic.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-dock-mic.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output-headphones-2.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-10.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-internal-mic.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output.conf.common
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-aux.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-2.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-8.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input.conf.common
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-internal-mic-always.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-9.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-tvtuner.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-headphone-mic.conf
./usr/share/pulseaudio/alsa-mixer/paths/iec958-stereo-input.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-rear-mic.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-4.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-headset-mic.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-front-mic.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-fm.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-3.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output-speaker.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-5.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output-mono.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-mic.conf.common
./usr/share/pulseaudio/alsa-mixer/paths/steelseries-arctis-output-game-common.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output-speaker-always.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-output-headphones.conf
./usr/share/pulseaudio/alsa-mixer/paths/usb-gaming-headset-output-stereo.conf
./usr/share/pulseaudio/alsa-mixer/paths/analog-input-video.conf
./usr/share/pulseaudio/alsa-mixer/paths/hdmi-output-6.conf
./usr/share/pulseaudio/alsa-mixer/paths/usb-gaming-headset-output-mono.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/hp-tbt-dock-audio-module.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-traktor-audio6.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/cmedia-high-speed-true-hdaudio.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/force-speaker.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/audigy.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/simple-headphones-mic.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-komplete-audio6.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/hp-tbt-dock-120w-g2.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/force-speaker-and-int-mic.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/sennheiser-gsx.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-traktorkontrol-s4.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-audio8dj.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/texas-instruments-pcm2902.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/default.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/maudio-fasttrack-pro.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-traktor-audio2.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/dell-dock-tb16-usb-audio.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/sb-omni-surround-5.1.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-traktor-audio10.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-audio4dj.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/asus-xonar-se.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/steelseries-arctis-common-usb-audio.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/usb-gaming-headset.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/native-instruments-korecontroller.conf
./usr/share/pulseaudio/alsa-mixer/profile-sets/kinect-audio.conf
./usr/share/dbus-1/system.d/pulseaudio-system.conf
./usr/share/locale/ka/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/gl/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/pt/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/ru/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/he/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/tr/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/pa/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/pt_BR/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/ca/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/af/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/sk/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/or/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/sr@latin/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/ko/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/nl/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/sv/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/kn/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/ml/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/gu/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/hu/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/bg/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/id/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/ja/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/fi/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/it/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/hr/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/eu/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/de/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/pl/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/si/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/be/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/uk/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/bn_IN/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/kk/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/ta/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/da/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/nn/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/es/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/hi/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/zh_TW/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/as/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/te/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/mr/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/eo/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/el/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/zh_CN/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/sr/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/cs/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/fr/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/oc/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/lt/LC_MESSAGES/pulseaudio.mo
./usr/share/locale/de_CH/LC_MESSAGES/pulseaudio.mo
./usr/share/bash-completion/completions/pasuspender
./usr/share/bash-completion/completions/parecord
./usr/share/bash-completion/completions/pulseaudio
./usr/share/bash-completion/completions/pacmd
./usr/share/bash-completion/completions/paplay
./usr/share/bash-completion/completions/pacat
./usr/share/bash-completion/completions/padsp
./usr/share/bash-completion/completions/parec
./usr/share/bash-completion/completions/pactl
./usr/bin/pasuspender
./usr/bin/parecord
./usr/bin/pulseaudio
./usr/bin/pa-info
./usr/bin/pacmd
./usr/bin/paplay
./usr/bin/pacat
./usr/bin/padsp
./usr/bin/parec
./usr/bin/pactl
./usr/bin/pamon
./usr/lib/libpulse.so.0
./usr/lib/libpulse-mainloop-glib.so.0.0.6
./usr/lib/libpulse.so.0.24.3
./usr/lib/pulseaudio/libpulsedsp.so
./usr/lib/pulseaudio/modules/module-remap-sink.so
./usr/lib/pulseaudio/modules/module-simple-protocol-unix.so
./usr/lib/pulseaudio/modules/module-device-manager.so
./usr/lib/pulseaudio/modules/module-alsa-source.so
./usr/lib/pulseaudio/modules/module-card-restore.so
./usr/lib/pulseaudio/modules/module-default-device-restore.so
./usr/lib/pulseaudio/modules/module-null-source.so
./usr/lib/pulseaudio/modules/libprotocol-cli.so
./usr/lib/pulseaudio/modules/module-cli-protocol-unix.so
./usr/lib/pulseaudio/modules/module-volume-restore.so
./usr/lib/pulseaudio/modules/module-rescue-streams.so
./usr/lib/pulseaudio/modules/libraop.so
./usr/lib/pulseaudio/modules/module-tunnel-sink-new.so
./usr/lib/pulseaudio/modules/libprotocol-http.so
./usr/lib/pulseaudio/modules/libalsa-util.so
./usr/lib/pulseaudio/modules/module-cli-protocol-tcp.so
./usr/lib/pulseaudio/modules/module-tunnel-source-new.so
./usr/lib/pulseaudio/modules/module-native-protocol-unix.so
./usr/lib/pulseaudio/modules/module-bluetooth-policy.so
./usr/lib/pulseaudio/modules/module-switch-on-connect.so
./usr/lib/pulseaudio/modules/module-http-protocol-unix.so
./usr/lib/pulseaudio/modules/module-position-event-sounds.so
./usr/lib/pulseaudio/modules/module-loopback.so
./usr/lib/pulseaudio/modules/module-virtual-sink.so
./usr/lib/pulseaudio/modules/module-sine.so
./usr/lib/pulseaudio/modules/module-mmkbd-evdev.so
./usr/lib/pulseaudio/modules/module-null-sink.so
./usr/lib/pulseaudio/modules/module-rtp-recv.so
./usr/lib/pulseaudio/modules/module-console-kit.so
./usr/lib/pulseaudio/modules/module-bluez5-discover.so
./usr/lib/pulseaudio/modules/libprotocol-native.so
./usr/lib/pulseaudio/modules/libprotocol-simple.so
./usr/lib/pulseaudio/modules/module-filter-apply.so
./usr/lib/pulseaudio/modules/module-ladspa-sink.so
./usr/lib/pulseaudio/modules/module-rygel-media-server.so
./usr/lib/pulseaudio/modules/module-combine.so
./usr/lib/pulseaudio/modules/module-allow-passthrough.so
./usr/lib/pulseaudio/modules/module-bluez5-device.so
./usr/lib/pulseaudio/modules/module-augment-properties.so
./usr/lib/pulseaudio/modules/module-always-sink.so
./usr/lib/pulseaudio/modules/module-echo-cancel.so
./usr/lib/pulseaudio/modules/module-udev-detect.so
./usr/lib/pulseaudio/modules/module-role-cork.so
./usr/lib/pulseaudio/modules/module-detect.so
./usr/lib/pulseaudio/modules/module-dbus-protocol.so
./usr/lib/pulseaudio/modules/module-tunnel-source.so
./usr/lib/pulseaudio/modules/module-always-source.so
./usr/lib/pulseaudio/modules/liboss-util.so
./usr/lib/pulseaudio/modules/libcli.so
./usr/lib/pulseaudio/modules/module-stream-restore.so
./usr/lib/pulseaudio/modules/module-tunnel-sink.so
./usr/lib/pulseaudio/modules/module-sine-source.so
./usr/lib/pulseaudio/modules/module-bluetooth-discover.so
./usr/lib/pulseaudio/modules/module-raop-sink.so
./usr/lib/pulseaudio/modules/module-pipe-source.so
./usr/lib/pulseaudio/modules/librtp.so
./usr/lib/pulseaudio/modules/module-intended-roles.so
./usr/lib/pulseaudio/modules/module-native-protocol-tcp.so
./usr/lib/pulseaudio/modules/module-rtp-send.so
./usr/lib/pulseaudio/modules/module-switch-on-port-available.so
./usr/lib/pulseaudio/modules/module-cli.so
./usr/lib/pulseaudio/modules/module-remap-source.so
./usr/lib/pulseaudio/modules/module-simple-protocol-tcp.so
./usr/lib/pulseaudio/modules/libbluez5-util.so
./usr/lib/pulseaudio/modules/module-pipe-sink.so
./usr/lib/pulseaudio/modules/module-http-protocol-tcp.so
./usr/lib/pulseaudio/modules/module-filter-heuristics.so
./usr/lib/pulseaudio/modules/module-native-protocol-fd.so
./usr/lib/pulseaudio/modules/module-device-restore.so
./usr/lib/pulseaudio/modules/module-combine-sink.so
./usr/lib/pulseaudio/modules/module-virtual-source.so
./usr/lib/pulseaudio/modules/module-oss.so
./usr/lib/pulseaudio/modules/module-suspend-on-idle.so
./usr/lib/pulseaudio/modules/module-match.so
./usr/lib/pulseaudio/modules/module-role-ducking.so
./usr/lib/pulseaudio/modules/module-alsa-card.so
./usr/lib/pulseaudio/modules/module-alsa-sink.so
./usr/lib/pulseaudio/modules/module-hal-detect.so
./usr/lib/pulseaudio/libpulsecore-17.0.so
./usr/lib/pulseaudio/libpulsecommon-17.0.so
./usr/lib/libpulse-simple.so
./usr/lib/pkgconfig/libpulse-mainloop-glib.pc
./usr/lib/pkgconfig/libpulse.pc
./usr/lib/pkgconfig/libpulse-simple.pc
./usr/lib/libpulse-mainloop-glib.so.0
./usr/lib/libpulse-simple.so.0
./usr/lib/udev/rules.d/90-pulseaudio.rules
./usr/lib/libpulse-mainloop-glib.so
./usr/lib/libpulse-simple.so.0.1.1
./usr/lib/cmake/PulseAudio/PulseAudioConfig.cmake
./usr/lib/cmake/PulseAudio/PulseAudioConfigVersion.cmake
./usr/lib/libpulse.so
./etc/init.d/S50pulseaudio
./etc/profile.d/pulse.sh
./etc/pulse/daemon.conf
./etc/pulse/default.pa
./etc/pulse/client.conf
./etc/pulse/system.pa
