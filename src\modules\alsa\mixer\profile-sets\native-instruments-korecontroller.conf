# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Native Instruments Kore Controller
;
; This card has one stereo pairs of input and two stereo pairs of
; output, named "Master" and "Headphone". The master channel has
; an additional Coax S/PDIF connector which is always on.
;
; We knowingly only define a subset of the theoretically possible
; mapping combinations as profiles here.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-master-out]
description = Analog Stereo Master Channel
device-strings = hw:%f,0,0
channel-map = left,right

[Mapping analog-stereo-headphone-out]
description = Analog Stereo Headphone Channel
device-strings = hw:%f,0,1
channel-map = left,right
direction = output

[Mapping analog-stereo-input]
description = Analog Stereo
device-strings = hw:%f,0,0
channel-map = left,right
direction = input

[Profile output:analog-stereo-all+input:analog-stereo-all]
description = Analog Stereo Duplex Master Output, Headphones Output
output-mappings = analog-stereo-master-out analog-stereo-headphone-out
input-mappings = analog-stereo-input
priority = 100
skip-probe = yes

[Profile output:analog-stereo-master+input:analog-stereo-input]
description = Analog Stereo Duplex Master Output
output-mappings = analog-stereo-master-out
input-mappings = analog-stereo-input
priority = 40
skip-probe = yes

[Profile output:analog-stereo-headphone-out+input:analog-stereo-input]
description = Analog Stereo Headphones Output
output-mappings = analog-stereo-headphone-out
input-mappings = analog-stereo-input
priority = 30
skip-probe = yes

[Profile output:analog-stereo-master]
description = Analog Stereo Master Output
output-mappings = analog-stereo-master-out
priority = 3
skip-probe = yes

[Profile output:analog-stereo-headphone]
description = Analog Stereo Headphones Output
output-mappings = analog-stereo-headphone-out
priority = 2
skip-probe = yes

[Profile input:analog-stereo-input]
description = Analog Stereo Input
input-mappings = analog-stereo-input
priority = 1
skip-probe = yes
