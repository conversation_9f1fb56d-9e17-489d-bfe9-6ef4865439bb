# Indonesian translation of pulseaudio
# Copyright (C) 2011 THE pulseaudio'S COPYRIGHT HOLDER
# This file is distributed under the same license as the pulseaudio package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011, 2012, 2018.
msgid ""
msgstr ""
"Project-Id-Version: PulseAudio master\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2021-09-24 12:05+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Indonesian <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/id/>\n"
"Language: id\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.8\n"

#: src/daemon/cmdline.c:113
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [opsi]\n"
"\n"
"PERINTAH:\n"
"  -h, --help                            Tampilkan bantuan ini\n"
"      --version                         Tampilkan versi\n"
"      --dump-conf                       Semua konfigurasi baku\n"
"      --dump-modules                    Daftar semua modul tersedia\n"
"      --dump-resample-methods           Semua metoda cuplik ulang yang "
"tersedia\n"
"      --cleanup-shm                     Bersihkan segmen memori dipakai "
"bersama yang basi\n"
"      --start                           Mulai daemon bila tidak sedang "
"berjalan\n"
"  -k  --kill                            Matikan daemon yang sedang berjalan\n"
"      --check                           Periksa apakah ada daemon yang "
"sedang berjalan\n"
"                                        (hanya mengembalikan kode keluar)\n"
"\n"
"OPSI:\n"
"      --system[=BOOL]                   Jalankan sebagai instansi seluruh "
"sistem\n"
"  -D, --daemonize[=BOOL]                Jadikan daemon setelah awalan\n"
"      --fail[=BOOL]                     Keluar ketika awalan gagal\n"
"      --high-priority[=BOOL]            Coba tata ke aras nice tinggi\n"
"                                        (hanya tersedia sebagai root, ketika "
"SUID\n"
"                                        atau RLIMIT_NICE yang dinaikkan)\n"
"      --realtime[=BOOL]                 Coba fungsikan penjadwalan waktu-"
"nyata\n"
"                                        (hanya tersedia sebagai root, ketika "
"SUID\n"
"                                        atau RLIMIT_RTPRIO yang dinaikkan)\n"
"      --disallow-module-loading[=BOOL]  Larang muat/bongkar modul yang "
"diminta\n"
"                                        pengguna setelah awalan\n"
"      --disallow-exit[=BOOL]            Larang permintaan keluar dari "
"pengguna\n"
"      --exit-idle-time=SECS             Matikan daemon ketika menganggur "
"dan\n"
"                                        waktu ini habis\n"
"      --module-idle-time=SECS           Bongkar modul yang dimuat sendiri "
"ketika\n"
"                                        menganggur dan waktu ini habis\n"
"      --scache-idle-time=SECS           Bongkar contoh yang dimuat sendiri "
"ketika\n"
"                                        menganggur dan waktu ini habis\n"
"      --log-level[=LEVEL]               Naikkan atau tata aras kerincian\n"
"  -v                                    Naikkan aras kerincian\n"
"      --log-target={auto,syslog,stderr} Nyatakan target log\n"
"      --log-meta[=BOOL]                 Sertakan lokasi kode dalam pesan "
"log\n"
"      --log-time[=BOOL]                 Sertakan penanda waktu dalam pesan "
"log\n"
"      --log-backtrace=FRAMES            Sertakan suatu backtrace dalam pesan "
"log\n"
"  -p, --dl-search-path=PATH             Tata path pencarian bagi objek "
"dipakai\n"
"                                        bersama yang dinamis (plugin)\n"
"      --resample-method=METHOD          Gunakan metoda cuplik ulang yang "
"dinyatakan\n"
"                                        (Lihat --dump-resample-methods "
"untuk\n"
"                                        nilai yang mungkin)\n"
"      --use-pid-file[=BOOL]             Buat suatu berkas PID\n"
"      --no-cpu-limit[=BOOL]             Jangan pasang pembatas beban CPU\n"
"                                        pada platform yang mendukungannya.\n"
"      --disable-shm[=BOOL]              Matikan dukungan memori bersama.\n"
"\n"
"SKRIP AWALAN:\n"
"  -L, --load=\"ARGUMEN MODUL\"            Muat modul plugin yang dinyatakan\n"
"                                        dengan argumen yang disertakan\n"
"  -F, --file=NAMABERKAS                 Jalankan skrip yang dinyatakan\n"
"  -C                                    Buka baris perintah pada TTY "
"berjalan\n"
"                                        setelah awalan\n"
"\n"
"  -n                                    Jangan muat berkas skrip baku\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize mengharapkan argumen bool"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail mengharapkan argumen bool"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level mengharapkan argumen aras log (bisa berupa angka 0..4 atau salah "
"satu dari debug, info, notice, warn, error)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority mengharapkan argumen bool"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime mengharapkan argumen bool"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading mengharapkan argumen bool"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit mengharapkan argumen bool"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file mengharapkan argumen bool"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Target log tidak valid: gunakan 'syslog', 'journal','stderr', atau 'auto' "
"atau suatu nama berkas yang valid 'file:<path>', 'newfile:<path>'."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Target log tak valid: gunakan 'syslog', 'stderr', atau 'auto', atau suatu "
"nama berkas yang valid 'file:<path>', 'newfile:<path>'."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time mengharapkan argumen bool"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta mengharapkan argumen bool"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Metode cuplik ulang tak valid '%s'."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system mengharapkan argumen bool"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit mengharapkan argumen bool"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm mengharapkan argumen bool"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd mengharapkan argumen boolean"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Tujuan log tak valid '%s'."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Aras log tak valid '%s'."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Metoda cuplikan ulang tak valid '%s'."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] rlimit tak valid'%s'."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Bentuk cuplikan tak valid '%s'."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Laju cuplikan tak valid '%s'."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Kanal cuplikan tak valid '%s'."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Peta kanal tak valid '%s'."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Cacah fragmen tak valid '%s'."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Ukuran fragmen tak valid '%s'."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Aras nice tak valid '%s'."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Tipe server '%s' tak valid."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Gagal membaca berkas konfigurasi: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"Peta kanal baku yang dinyatakan memiliki cacah kanal yang berbeda dengan "
"cacah kanal baku yang dinyatakan."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Membaca dari berkas konfigurasi: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Nama: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Informasi modul tak tersedia\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Versi: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Keterangan: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Penulis: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Cara pakai: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Muat Sekali: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "PERINGATAN KADALUARSA: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Path: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "Gagal membuka modul %s: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Gagal menemukan pemuat lt_dlopen asli."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Gagal mengalokasikan pemuat dl baru."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Gagal menambah bind-now-loader."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Gagal mencari pengguna '%s'."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Gagal mencari grup '%s'."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "GID dari pengguna '%s' dan dari grup '%s' tak cocok."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "Direktori rumah dari pengguna '%s' bukan '%s', mengabaikan."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Gagal membuat '%s': %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Gagal mengubah daftar grup: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Gagal mengubah GID: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Gagal mengubah UID: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "Mode seluruh-sistem tak didukung pada platform ini."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Gagal mengurai baris perintah."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Mode sistem ditolak bagi pengguna non root. Hanya memulai layanan pencarian "
"server D-Bus."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Gagal membunuh daemon: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Program ini tidak diinginkan dijalankan sebagai root (kecuali dinyatakan --"
"system)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Dibutuhkan hak root."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start tak didukung bagi instansi sistem."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr "Server gubahan pengguna pada %s, menolak mulai/spawn sendiri."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Server yang dikonfigurasi pengguna pada %s, yang nampaknya lokal. Menggali "
"lebih dalam."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "Berjalan dalam mode sistem, tapi --disallow-exit tidak ditata."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"Berjalan dalam mode sistem, tapi --disallow-module-loading tidak ditata."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "Berjalan dalam mode sistem, mematikan paksa mode SHM."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr "Berjalan dalam mode sistem, mematikan paksa keluar waktu menganggur."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Gagal memperoleh stdio."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() gagal: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() gagal: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() gagal: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Gagal memulai daemon."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() gagal: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Gagal memperoleh ID mesin"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"OK, jadi Anda menjalankan PA dalam mode sistem. Mohon pastikan bahwa Anda "
"memang benar-benar ingin melakukannya.\n"
"Silakan baca http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ bagi penjelasan mengapa mode "
"sistem biasanya adalah ide buruk."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() gagal."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() gagal."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "argumen baris perintah"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"Gagal menginisialisasi daemon karena kesalahan saat mengeksekusi perintah "
"awal mula. Sumber perintah: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "Daemon dimulai tanpa modul apapun yang dimuat, menolak bekerja."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "Sistem Suara PulseAudio"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Memulai Sistem Suara PulseAudio"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Masukan"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Masukan Docking Station"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Mikrofon Docking Station"

#: src/modules/alsa/alsa-mixer.c:2711
#, fuzzy
msgid "Docking Station Line In"
msgstr "Jalur Masuk Docking Station"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
#, fuzzy
msgid "Line In"
msgstr "Jalur Masuk"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Mikrofon"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Mikrofon Depan"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Mikrofon Belakang"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Mikrofon Eksternal"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Mikrofon Internal"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Radio"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Video"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Kendali Penguatan Otomatis (AGC)"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Tanpa Kendali Penguatan Otomatis (AGC)"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Boost"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Tanpa Boost"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Penguat"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Tanpa Penguat"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Boost Bass"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Tanpa Boost Bass"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Speaker"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Headphone"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Masukan Analog"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Mikrofon Dok"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Mikrofon Headset"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Keluaran Analog"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "Headphone 2"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "Keluaran Mono Headphone"

#: src/modules/alsa/alsa-mixer.c:2810
#, fuzzy
msgid "Line Out"
msgstr "Jalur Keluar"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Keluaran Mono Analog"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Speaker"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Keluaran Digital (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Masukan Digital (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "Masukan Multikanal"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "Keluaran Multikanal"

#: src/modules/alsa/alsa-mixer.c:2818
#, fuzzy
msgid "Game Output"
msgstr "Keluaran %s"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "Keluaran Obrolan"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "Masukan Obrolan"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "Virtual Surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "Analog Mono (Kiri)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "Analog Mono (Kanan)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Analog Stereo"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Mono"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Stereo"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Headset"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "Speakerphone"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Multikanal"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Analog Surround 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Analog Surround 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Analog Surround 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Analog Surround 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Analog Surround 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Analog Surround 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Analog Surround 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Analog Surround 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Analog Surround 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Analog Surround 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Analog Surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Digital Stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Digital Surround 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Digital Surround 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Surround 5.1 Digital (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Digital Stereo (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Surround 5.1 Digital (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "Obrolan"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "Permainan"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Analog Mono Dupleks"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Analog Stereo Dupleks"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Digital Stereo Dupleks (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "Dupleks Multikanal"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "Dupleks Stereo"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "Mono Chat + 7.1 Surround"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Mati"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "Keluaran %s"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "Masukan %s"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA membangunkan kita untuk menulis data baru ke peranti, tapi sebenarnya "
"tak ada sesuatu untuk ditulis!\n"
"Sangat mungkin ini adalah kutu pada driver ALSA '%s'. Silakan laporkan "
"masalah ini ke para pengembang ALSA.\n"
"Kami dibangunkan dengan POLLOUT diset -- namun snd_pcm_avail() setelahnya "
"mengembalikan 0 atau nilai lain < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA membangunkan kita untuk membaca data baru dari peranti, tapi sebenarnya "
"tak ada sesuatu untuk dibaca!\n"
"Sangat mungkin ini adalah kutu pada driver ALSA '%s'. Silakan laporkan "
"masalah ini ke para pengembang ALSA.\n"
"Kami dibangunkan dengan POLLIN diset -- namun snd_pcm_avail() setelahnya "
"mengembalikan 0 atau nilai lain < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, fuzzy, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() mengembalikan nilai yang luar biasa besar: %lu byte (%lu "
"ms).\n"
"Sangat mungkin ini adalah kutu pada driver ALSA '%s'. Silakan laporkan hal "
"ini ke para pengembang ALSA."

#: src/modules/alsa/alsa-util.c:1249
#, fuzzy, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() mengembalikan nilai yang luar biasa besar: %li byte (%s%lu "
"ms).\n"
"Sangat mungkin ini adalah kutu pada driver ALSA '%s'. Silakan laporkan hal "
"ini ke para pengembang ALSA."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() mengembalikan nilai yang aneh: tundaan %lu kurang dari "
"yang tersedia %lu.\n"
"Paling mungkin ini adalah kutu dalam penggerak ALSA '%s'. Harap laporkan "
"kasus ini ke para pengembang ALSA."

#: src/modules/alsa/alsa-util.c:1339
#, fuzzy, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() mengembalikan nilai yang luar biasa besar: %lu byte "
"(%lu ms).\n"
"Sangat mungkin ini adalah kutu pada driver ALSA '%s'. Silakan laporkan hal "
"ini ke para pengembang ALSA."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Masukan Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Keluaran Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Handsfree"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Headphone"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Portabel"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Mobil"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "HiFi"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Telepon"

#: src/modules/bluetooth/module-bluez5-device.c:2042
#, fuzzy
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "High Fidelity Playback (Muara A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
#, fuzzy
msgid "High Fidelity Capture (A2DP Source)"
msgstr "High Fidelity Capture (Sumber A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
#, fuzzy
msgid "Headset Head Unit (HSP)"
msgstr "Headset Head Unit (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
#, fuzzy
msgid "Headset Audio Gateway (HSP)"
msgstr "Headset Audio Gateway (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
#, fuzzy
msgid "Handsfree Head Unit (HFP)"
msgstr "Headset Head Unit (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
#, fuzzy
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Headset Audio Gateway (HSP/HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
#, fuzzy
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "Nyala"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Keluaran Dummy"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "Selalu jaga paling tidak satu muara bermuatan bahkan jika berupa null"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "Selalu jaga paling tidak satu muara bermuatan bahkan jika berupa null"

#: src/modules/module-equalizer-sink.c:68
#, fuzzy
msgid "General Purpose Equalizer"
msgstr "Equalizer Tujuan Umum"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<nama muara> sink_properties=<properti muara> sink_master=<muara "
"tempat menyambung> format=<format cuplikan> rate=<laju cuplikan> "
"channels=<cacah kanal> channel_map=<peta kanal> autoloaded=<tata bila modul "
"ini dimuat secara otomatis> use_volume_sharing=<yes atau no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "Ekualiser berbasis FFT pada %s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<secara otomatis bongkar penyaring yang tak dipakai?>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Muara virtual LADSPA"

#: src/modules/module-ladspa-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<nama bagi muara> sink_properties=<properti bagi muara> "
"sink_input_properties=<properti bagi masukan muara> master=<nama muara untuk "
"disaring> sink_master=<nama muara yang akan disaring> format=<format "
"cuplikan> rate=<laju cuplikan> channels=<cacah kanal> channel_map=<peta "
"kanal> plugin=<nama plugin ladspa> label=<label plugin ladspa> "
"control=<daftar nilai kendali masukan yang dipisahkan dengan koma> \n"
"input_ladspaport_map=<comma separated list of input LADSPA port names> "
"output_ladspaport_map=<comma separated list of output LADSPA port names> "
"autoloaded=<set if this module is being loaded automatically> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Muara NULL dengan clock"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Keluaran Null"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Gagal menata format: string format tak valid %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Perangkat Keluaran"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Perangkat Masukan"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "Audio pada @HOSTNAME@"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "Tunnel untuk %s@%s"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "Tunnel ke %s/%s"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Muara virtual surround"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<nama bagi muara> sink_properties=<properti bagi muara> "
"master=<nama muara untuk disaring> sink_master=<nama muara untuk disaring> "
"format=<format cuplikan> rate=<laju cuplikan> channels=<cacah kanal> "
"channel_map=<peta kanal> use_volume_sharing=<yes atau no> "
"force_flat_volume=<yes atau no> hrir=/path/ke/left_hrir.wav hrir_left=/path/"
"ke/left_hrir.wav hrir_right=/path/ke/opsional/right_hrir.wav "
"autoloaded=<ditata bila modul ini dimuat secara otomatis> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "Model peranti tak dikenal"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "Profil standar RAOP"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "Server Suara PulseAudio"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Depan Tengah"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Depan Kiri"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Depan Kanan"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Belakang Tengah"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Belakang Kiri"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Belakang Kanan"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Subwoofer"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Depan Tengah agak ke kiri"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Depan Tengah agak ke kanan"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Samping Kiri"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Samping Kanan"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Tambahan 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Tambahan 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Tambahan 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Tambahan 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Tambahan 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Tambahan 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Tambahan 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Tambahan 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Tambahan 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Tambahan 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Tambahan 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Tambahan 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Tambahan 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Tambahan 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Tambahan 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Tambahan 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Tambahan 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Tambahan 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Tambahan 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Tambahan 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Tambahan 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Tambahan 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Tambahan 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Tambahan 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Tambahan 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Tambahan 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Tambahan 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Tambahan 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Tambahan 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Tambahan 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Tambahan 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Tambahan 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Puncak Tengah"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Puncak Depan Tengah"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Puncak Depan Kiri"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Puncak Depan Kanan"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Puncak Belakang Tengah"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Puncak Belakang Kiri"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Puncak Belakang Kanan"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(tak valid)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Surround 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Surround 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Surround 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Surround 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Surround 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() gagal"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() mengembalikan true"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Gagal mengurai data cookie"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Menerima pesan bagi pengaya tak dikenal '%s'"

#: src/pulse/direction.c:37
msgid "input"
msgstr "masukan"

#: src/pulse/direction.c:39
msgid "output"
msgstr "keluaran"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "dua arah"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "tidak valid"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) tidak dimiliki oleh kita (uid %d), tapi oleh uid %d! "
"(Ini bisa terjadi mis. bila Anda mencoba menyambung ke suatu PulseAudio "
"bukan root sebagai pengguna root, melalui protokol native. Jangan lakukan "
"itu.)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "ya"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "tidak"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Tak bisa akses kunci spawn sendiri."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Gagal membuka berkas target '%s'."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Mencoba membuka berkas target '%s', '%s.1', '%s.2' ... '%s.%d', tapi semua "
"gagal."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Target log tidak valid."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Audio Bawaan"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Akses ditolak"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Perintah tak dikenal"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Argumen tak valid"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "Entitas ada"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Entitas tak ada"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Koneksi ditolak"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Galat protokol"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Habis waktu"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Tidak ada kunci otentikasi"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Galat internal"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Sambungan diakhiri"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Entitas dimatikan"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Server tak valid"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Inisialisasi modul gagal"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Kondisi buruk"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Tak ada data"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Versi protokol tak kompatibel"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Terlalu besar"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Tak didukung"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Kode galat tak dikenal"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Tak ada ekstensi demikian"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Fungsionalitas yang tak berlaku lagi"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Tak ada implementasi"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Klien di-fork"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Galat masukan/keluaran"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Perangkat atau sumber daya sibuk"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Gagal menguras stream: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Stream main ulang terkuras."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Menguras sambungan ke server."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() gagal: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() gagal: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Stream sukses dibuat."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() gagal: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Metrik penyangga: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Metrik penyangga: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "Memakai spek cuplikan '%s', peta kanal '%s'."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Tersambung ke perangkat %s (indeks: %u, disuspensi: %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Galat stream: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Perangkat stream disuspensi.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Perangkat stream dilanjutkan.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Stream underrun.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Stream tertimpa.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Stream dimulai.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Stream dipindah ke perangkat %s (%u, %sdisuspensi).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "tidak "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Atribut penyangga stream diubah.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Tumpukan permintaan cork kosong: meng-cork stream"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Tumpukan permintaan cork kosong: meng-uncork stream"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""
"Peringatan: Menerima lebih banyak permintaan uncork daripada permintaan cork."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Koneksi terbentuk.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() gagal: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() gagal: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Gagal menata stream pantau: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() gagal: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Kegagalan koneksi: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "Mendapat EOF."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() gagal: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() gagal: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Mendapat sinyal, keluar."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Gagal mendapat latensi: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Waktu: %0.3f dtk; Latensi: %0.0f udtk."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() gagal: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [opsi]\n"
"%s\n"
"\n"
"  -h, --help                            Tampilkan bantuan ini\n"
"      --version                         Tampilkan versi\n"
"\n"
"  -r, --record                          Buat koneksi untuk perekaman\n"
"  -p, --playback                        Buat koneksi untuk main ulang\n"
"\n"
"  -v, --verbose                         Aktifkan operasi cerewet\n"
"\n"
"  -s, --server=SERVER                   Nama server untuk dihubungi\n"
"  -d, --device=DEVICE                   Nama muara/sumber untuk dihubungi\n"
"  -n, --client-name=NAME                Bagaimana memanggil klien ini di "
"server\n"
"      --stream-name=NAME                Bagaimana memanggil stream ini di "
"server\n"
"      --volume=VOLUME                   Nyatakan volume awal (linier) dalam "
"jangkauan 0...65536\n"
"      --rate=SAMPLERATE                 Laju cuplikan dalam Hz (nilai baku "
"44100)\n"
"      --format=SAMPLEFORMAT             Jenis cuplikan, salah satu dari "
"s16le, s16be, u8, float32le,\n"
"                                        float32be, ulaw, alaw, s32le, s32be, "
"s24le, s24be,\n"
"                                        s24-32le, s24-32be (nilai baku "
"s16ne)\n"
"      --channels=CHANNELS               Cacah kanal, 1 untuk mono, 2 untuk "
"stereo\n"
"                                        (nilai baku 2)\n"
"      --channel-map=CHANNELMAP          Peta kanal yang dipakai sebagai "
"pengganti baku\n"
"      --fix-format                      Ambil format cuplikan dari muara "
"stream\n"
"                                        yang sedang tersambung.\n"
"      --fix-rate                        Ambil laju cuplikan dari muara "
"stream\n"
"                                        yang sedang tersambung.\n"
"      --fix-channels                    Ambil cacah kanal dan peta kanal "
"dari muara stream\n"
"                                        yang sedang tersambung.\n"
"      --no-remix                        Jangan upmix atau downmix kanal.\n"
"      --no-remap                        Petakan kanal berdasar indeks bukan "
"nama.\n"
"      --latency=BYTE                    Minta latensi yang dinyatakan, dalam "
"byte.\n"
"      --process-time=BYTE               Minta waktu proses yang dinyatakan "
"bagi tiap permintaan\n"
"                                        dalam byte.\n"
"      --latency-msec=MSEC               Minta latensi yang dinyatakan, dalam "
"milidetik.\n"
"      --process-time-msec=MSEC          Minta waktu proses yang dinyatakan "
"bagi tiap permintaan\n"
"                                        dalam milidetik.\n"
"      --property=PROPERTI=NILAI         Tata properti yang dinyatakan ke "
"nilai yang dinyatakan.\n"
"      --raw                             Rekam/mainkan data PCM mentah.\n"
"      --passthrough                     Data passthrough.\n"
"      --file-format[=FFORMAT]           Rekam/mainkan data PCM terformat.\n"
"      --list-file-formats               Tampilkan daftar format berkas yang "
"tersedia.\n"
"      --monitor-stream=INDEKS           Rekam dari masukan muara dengan "
"indeks INDEKS.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr "Memutar berkas audio terenkode pada server suara PulseAudio."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""
"Menangkap data audio dari suatu server suara PulseAudio dan menulisnya ke "
"sebuah berkas."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"Menangkap data audio dari suatu server suara PulseAudio dan menulisnya ke "
"STDOUT atau berkas yang dinyatakan."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"Memutar data audio dari STDIN atau berkas yang dinyatakan pada suatu server "
"suara PulseAudio."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Dikompail dengan libpulse %s\n"
"Ditaut dengan libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Nama klien '%s' tak valid"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Nama stream '%s' tak valid"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Peta kanal '%s' tak valid"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Spesifikasi latensi '%s' tak valid"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Spesifikasi waktu proses '%s' tak valid"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Properti '%s' tak valid"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Format berkas %s tak dikenal."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "Gagal mengurai argumen untuk --monitor-stream"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Spesifikasi cuplikan tak valid"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Terlalu banyak argumen."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Gagal menjangkitkan spesifikasi cuplikan bagi berkas."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Gagal membuka berkas audio."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Peringatan: spesifikasi cuplikan yang dinyatakan akan ditimpa oleh "
"spesifikasi dari berkas."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Gagal menentukan spesifikasi cuplikan dari berkas."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "Peringatan: Gagal menentukan peta kanal dari berkas."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "Peta kanan tak cocok dengan spesifikasi cuplikan"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Peringatan: gagal menulis peta kanal ke berkas."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Sedang membuka stream %s dengan spesifikasi cuplikan '%s' dan peta kanal "
"'%s'."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "merekam"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "memainkan"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Gagal menata nama media."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() gagal."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() gagal."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() gagal."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() gagal: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() gagal."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() gagal."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "NAMA [ARG ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "NAMA|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "NAMA"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "NAMA|#N VOLUME"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N VOLUME"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "NAMA|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "NAMA|#N KUNCI=NILAI"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N KUNCI=NILAI"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "NAMA MUARA|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "NAMA NAMABERKAS"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "NAMAPATH"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "NAMABERKAS MUARA|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N MUARA|SUMBER"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "PROFIL KARTU"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "NAMA|#N PORT"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "NAMA-KARTU|KARTU-#N PORT OFSET"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "TARGET"

#: src/utils/pacmd.c:76
#, fuzzy
msgid "NUMERIC-LEVEL"
msgstr "NUMERIC-LEVEL"

#: src/utils/pacmd.c:79
#, fuzzy
msgid "FRAMES"
msgstr "BINGKAI"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "PENERIMA PESAN [PARAMETER_PESAN]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Tampilkan bantuan ini\n"
"      --version                         Tampilkan versi\n"
"Ketika tidak ada perintah yang diberikan pacmd memulai dalam mode "
"interaktif.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"Dikompail dengan libpulse %s\n"
"Di-link dengan libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"Tak ada daemon PulseAudio yang berjalan, atau tak dijalankan sebagai daemon "
"sesi."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Gagal mematikan daemon PulseAudio."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Daemon tidak merespon."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Gagal mendapat statistik: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Sedang dipakai: %u blok memuat total %s byte.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] ""
"Dialokasikan dalam seluruh masa hidup: %u blok memuat total %s byte.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Ukuran singgahan cuplikan: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Gagal mendapat informasi server: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr "%s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"String Server: %s\n"
"Versi Protokol Pustaka: %u\n"
"Versi Protokol Server: %u\n"
"Di Lokal: %s\n"
"Indeks Klien: %u\n"
"Ukuran Ubin: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Nama Pengguna: %s\n"
"Nama Host: %s\n"
"Nama Server: %s\n"
"Versi Server: %s\n"
"Spesifikasi Cuplikan Baku: %s\n"
"Peta Kanal Baku: %s\n"
"Muara Baku: %s\n"
"Sumber Baku: %s\n"
"Cookie: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "ketersediaan tak diketahui"

#: src/utils/pactl.c:321
msgid "available"
msgstr "tersedia"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "tidak tersedia"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "Tak dikenal"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Aux"

#: src/utils/pactl.c:335
#, fuzzy
msgid "Line"
msgstr "Jalur Masuk"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Mik"

#: src/utils/pactl.c:338
msgid "Handset"
msgstr "Handset"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "Earpiece"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "SPDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "TV"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "Bluetooth"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "Jaringan"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "Analog"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Gagal mendapat informasi muara: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Muara #%u\n"
"\tKeadaan: %s\n"
"\tNama: %s\n"
"\tDeskripsi: %s\n"
"\tDriver: %s\n"
"\tSpesifikasi Cuplikan: %s\n"
"\tPeta Kanal: %s\n"
"\tModul Pemilik: %u\n"
"\tBisu: %s\n"
"\tVolume: %s\n"
"\t        balans %0.2f\n"
"\tVolume Dasar: %s\n"
"\tPemantauan Sumber: %s\n"
"\tLatensi: %0.0f udtk, dikonfigurasi %0.0f udtk\n"
"\tFlag: %s%s%s%s%s%s%s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "»Port:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (tipe: %s, prioritas: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ", grup ketersediaan: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "»Port Aktif: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tFormat:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Gagal mendapat informasi sumber: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sumber #%u\n"
"\tKeadaan: %s\n"
"\tNama: %s\n"
"\tDeskripsi: %s\n"
"\tDriver: %s\n"
"\tSpesifikasi Cuplikan: %s\n"
"\tPeta Kanal: %s\n"
"\tModul Pemilik: %u\n"
"\tBisu: %s\n"
"\tVolume: %s\n"
"\t        balans %0.2f\n"
"\tVolume Dasar: %s\n"
"\tPemantauan Muara: %s\n"
"\tLatensi: %0.0f udtk, dikonfigurasi %0.0f udtk\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "t/t"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Gagal mendapat informasi modul: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Modul #%u\n"
"\tNama: %s\n"
"\tArgumen: %s\n"
"\tPencacah pemakaian: %s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Gagal mendapat informasi klien: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Klien #%u\n"
"\tDriver: %s\n"
"\tModul Pemilik: %s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Gagal mendapat informasi kartu: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Kartu #%u\n"
"\tNama: %s\n"
"\tDriver: %s\n"
"\tModul Pemilik: %s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tProfil:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr "\t\t%s: %s (muara: %u, sumber: %u, prioritas: %u, tersedia: %s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tProfil Aktif: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (tipe: %s, prioritas: %u, ofset latensi: %<PRId64> udtk%s%s, %s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tProperti:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tBagian dari profil: %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Gagal mendapat informasi masukan muara: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Masukan Muara #%u\n"
"\tDriver: %s\n"
"\tModul Pemilik: %s\n"
"\tKlien: %s\n"
"\tMuara: %u\n"
"\tSpesifikasi Cuplikan: %s\n"
"\tPeta Kanal: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tBisu: %s\n"
"\tVolume: %s\n"
"\t        balans %0.2f\n"
"\tLatensi Penyangga: %0.0f usec\n"
"\tLatensi Muara: %0.0f usec\n"
"\tMetoda cuplik ulang: %s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "Gagal mendapat informasi keluaran sumber: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Keluaran Sumber #%u\n"
"\tDriver: %s\n"
"\tModul Pemilik: %s\n"
"\tKlien: %s\n"
"\tSumber: %u\n"
"\tSpesifikasi Cuplikan: %s\n"
"\tPeta Kanal: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tBisu: %s\n"
"\tVolume: %s\n"
"\t        balans %0.2f\n"
"\tLatensi Penyangga: %0.0f usec\n"
"\tLatensi Sumber: %0.0f usec\n"
"\tMetoda cuplik ulang: %s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Gagal mendapat informasi cuplikan: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Cuplikan #%u\n"
"\tNama: %s\n"
"\tSpesifikasi Cuplikan: %s\n"
"\tPeta Kanal: %s\n"
"\tVolume: %s\n"
"\t        balans %0.2f\n"
"\tDurasi: %0.1fs\n"
"\tUkuran: %s\n"
"\tMalas: %s\n"
"\tNama Berkas: %s\n"
"\tProperti:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Kegagalan: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "Kirim pesan gagal: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr "pesan penangan-daftar gagal: %s"

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "respon pesan penangan-daftar tak dapat diurai dengan benar"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "respon pesan penangan-daftar bukan suatu larik JSON"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr "elemen larik respon pesan penangan-pesan %d bukan suatu objek JSON"

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Gagal membongkar modul: Modul %s tak dimuat"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"Gagal menata volume: Anda mencoba menata volume untuk %d kanal, padahal "
"kanal yang didukung = %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Gagal mengunggah cuplikan: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Akhir berkas dini"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "baru"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "ubah"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "hapus"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "tak dikenal"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "muara"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "sumber"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "masukan-muara"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "sumber-keluaran"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "modul"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "klien"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "singgahan-cuplikan"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "server"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "kartu"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "Kejadian '%s' pada %s #%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "Mendapat SIGINT, keluar."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Spesifikasi volume tak valid"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "Volume di luar rentang yang diizinkan.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Banyaknya spesifikasi volume tidak valid.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Spesifikasi volume tidak konsisten.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[opsi]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[TIPE]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "NAMABERKAS [NAMA]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "NAMA [MUARA]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "NAMA|#N VOLUME [VOLUME ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N VOLUME [VOLUME ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "NAMA|#N 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N FORMAT"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"Nama-nama khusus @DEFAULT_SINK@, @DEFAULT_SOURCE@, dan @DEFAULT_MONITOR@\n"
"dapat dipakai untuk menyatakan muara, sumber, dan pemantau baku.\n"

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Tampilkan bantuan ini\n"
"      --version                         Tampilkan versi\n"
"\n"
"  -s, --server=SERVER                   Nama server tujuan koneksi\n"
"  -n, --client-name=NAMA                Bagaimana memanggil klien ini pada "
"server\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Dikompail dengan libpulse %s\n"
"Ditaut dengan libpulse %s\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "Nama stream '%s' tak valid"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Jangan nyatakan apapun, atau satu dari: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Nyatakan berkas cuplikan untuk dimuat"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Gagal membuka berkas suara."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "Peringatan: Gagal menentukan spesifikasi cuplikan dari berkas."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Anda mesti menyatakan nama cuplikan untuk diputar"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Anda mesti menyatakan nama cuplikan untuk dihapus"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "Anda mesti menyatakan suatu indeks masukan muara dan suatu muara"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "Anda mesti menyatakan suatu indeks keluaran sumber dan suatu sumber"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Anda mesti menyatakan nama modul dan argumen."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "Anda mesti menyatakan suatu nama atau indeks modul"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Anda tak boleh menyatakan lebih dari satu muara. Anda mesti menyatakan suatu "
"nilai bool."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "Spesifikasi suspensi tak valid."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Anda tak boleh menyatakan lebih dari satu sumber. Anda mesti menyatakan "
"suatu nilai bool."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Anda mesti menyatakan suatu indeks/nama kartu dan suatu nama profil"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Anda mesti menyatakan suatu indeks/nama muara dan suatu nama port"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "Anda mesti menyatakan suatu nama muara"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Anda mesti menyatakan suatu indeks/nama sumber dan suatu nama port"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "Anda mesti menyatakan suatu nama sumber"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "Anda mesti menyatakan suatu nama/indeks muara"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Anda mesti menyatakan suatu indeks/nama muara dan suatu volume"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "Anda mesti menyatakan suatu nama/indeks sumber"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Anda mesti menyatakan suatu indeks/nama sumber dan suatu volume"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Anda mesti menyatakan suatu indeks masukan muara dan suatu volume"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Indeks masukan muara tak valid"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "Anda mesti menyatakan suatu indeks keluaran sumber dan suatu volume"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Indeks keluaran sumber yang tak valid"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Anda harus menyatakan suatu nama muara/indeks dan suatu aksi bisu (0, 1, "
"atau 'toggle')"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Spesifikasi bisu tak valid"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Anda harus menyatakan suatu nama sumber/indeks dan suatu aksi bisu (0, 1, "
"atau 'toggle')"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Anda harus menyatakan suatu indeks masukan muara dan suatu aksi bisu (0, 1, "
"atau 'toggle')"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Spesifikasi index masukan muara tak valid"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr ""
"Anda harus menyatakan suatu indeks keluaran sumber dan suatu aksi bisu (0, "
"1, atau 'toggle')"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Spesifikasi index keluaran sumber tak valid"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr ""
"Anda mesti menyatakan paling tidak suatu path objek dan suatu nama pesan"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"Argumen berlebih diberikan, mereka akan diabaikan. Perhatikan bahwa semua "
"parameter pesan mesti diberikan sebagai sebuah string tunggal."

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"Anda mesti menyatakan suatu indeks muara dan daftar format yang didukung "
"yang dipisah titik koma"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr ""
"Anda mesti menyatakan suatu indeks/nama kartu, suatu nama port, dan suatu "
"ofset latensi"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Tak bisa mengurai ofset latensi"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Tak ada perintah valid yang dinyatakan."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Gagal melanjutkan: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Gagal mensuspensi: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "PERINGATAN: Server suara bukan lokal, tidak mensuspensi.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Kegagalan sambungan: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "Mendapat SIGINT, keluar.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "PERINGATAN: Proses anak diakhiri oleh sinyal %u\n"

#: src/utils/pasuspender.c:228
#, fuzzy, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [opsi] ... \n"
"\n"
"  -h, --help                            Tampilkan bantuan ini\n"
"      --version                         Tampilkan versi\n"
"  -s, --server=SERVER                   Nama server untuk dihubungi\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Dikompail dengan libpulse %s\n"
"Ditaut dengan libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() gagal.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() gagal.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() gagal.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O muara] [-I sumber] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Tampilkan data PulseAudio yang kini dicantolkan ke tampilan X11 "
"(baku)\n"
" -e    Ekspor data PulseAudio lokal ke tampilan X11\n"
" -i    Impor data PulseAudio dari tampilan X11 ke variabel lingkungan lokal "
"dan berkas cookie.\n"
" -r    Hapus data PulseAudio dari tampilan X11\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Gagal mengurai baris perintah.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Server: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Sumber: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Muara: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Cookie: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Gagal mengurai data cookie\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Gagal menyimpan data cookie\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Gagal mendapatkan FQDN.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Gagal memuat data cookie\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Belum diimplementasikan.\n"

#~ msgid ""
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ msgstr ""
#~ "\n"
#~ "  -h, --help                            Tampilkan bantuan ini\n"
#~ "      --version                         Tampilkan versi\n"
#~ "\n"
#~ "  -s, --server=SERVER                   Nama server tujuan koneksi\n"
#~ "  -n, --client-name=NAMA                Bagaimana memanggil klien ini "
#~ "pada server\n"

#~ msgid "Failed to initialize daemon."
#~ msgstr "Gagal menginisialisasi daemon."

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "LFE pada Keluaran Mono Terpisah"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Passthrough Digital (S/PDIF)"

#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "Passthrough Digital (IEC958)"

#~ msgid ""
#~ "ALSA woke us up to write new data to the device, but there was actually "
#~ "nothing to write!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA bangun untuk menulis data baru ke perangkat, tapi sebenarnya tak ada "
#~ "sesuatu untuk ditulis!\n"
#~ "Sangat mungkin ini adalah kutu pada driver ALSA '%s'. Silakan laporkan "
#~ "masalah ini ke para pengembang ALSA.\n"
#~ "Kami dibangunkan dengan POLLOUT diset -- namun snd_pcm_avail() setelahnya "
#~ "mengembalikan 0 atau nilai lain < min_avail."

#~ msgid ""
#~ "ALSA woke us up to read new data from the device, but there was actually "
#~ "nothing to read!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "ALSA bangun untuk membaca data baru dari perangkat, tapi sebenarnya tak "
#~ "ada sesuatu untuk dibaca!\n"
#~ "Sangat mungkin ini adalah kutu pada driver ALSA '%s'. Silakan laporkan "
#~ "masalah ini ke para pengembang ALSA.\n"
#~ "Kami dibangunkan dengan POLLIN diset -- namun snd_pcm_avail() setelahnya "
#~ "mengembalikan 0 atau nilai lain < min_avail."
