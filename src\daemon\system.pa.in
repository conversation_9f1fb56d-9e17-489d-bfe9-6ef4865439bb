#!@PA_BINARY@ -nF
#
# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify it
# under the terms of the GNU Lesser General Public License as published by
# the Free Software Foundation; either version 2 of the License, or
# (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

# This startup script is used only if PulseAudio is started in system
# mode.
changequote(`[', `]')dnl Set up m4 quoting

### Automatically restore the volume of streams and devices
load-module module-device-restore
load-module module-stream-restore
load-module module-card-restore

### Automatically load driver modules depending on the hardware available
ifelse(@HAVE_UDEV@, 1, [dnl
.ifexists module-udev-detect@PA_SOEXT@
load-module module-udev-detect
.else
], @HAVE_COREAUDIO@, 1, [dnl
.ifexists module-coreaudio-detect@PA_SOEXT@
load-module module-coreaudio-detect
.else
], [dnl
.ifexists module-detect@PA_SOEXT@
])dnl
### Use the static hardware detection module (for systems that lack udev/hal support)
load-module module-detect
.endif
ifelse(@OS_IS_FREEBSD@, 1, [dnl
### FreeBSD devd is used in addition to static detection (only handles hotplug)
.ifexists module-devd-detect@PA_SOEXT@
load-module module-devd-detect
.endif
])dnl

### Load several protocols
.ifexists module-esound-protocol-unix@PA_SOEXT@
load-module module-esound-protocol-unix
.endif
load-module module-native-protocol-unix

### Automatically restore the default sink/source when changed by the user
### during runtime
### NOTE: This should be loaded as early as possible so that subsequent modules
### that look up the default sink/source get the right value
load-module module-default-device-restore

### Make sure we always have a sink around, even if it is a null sink.
load-module module-always-sink

### Automatically suspend sinks/sources that become idle for too long
load-module module-suspend-on-idle

### Enable positioned event sounds
load-module module-position-event-sounds

### Allow including a system.pa.d directory, which if present, can be used
### for additional configuration snippets.
### Note that those snippet files must have a .pa file extension, not .conf
.nofail
.include @PA_DEFAULT_CONFIG_DIR_UNQUOTED@/system.pa.d
