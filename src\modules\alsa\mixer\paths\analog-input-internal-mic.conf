# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; For devices where a 'Internal Mic' or 'Internal Mic Boost' element exists
; 'Int Mic' and 'Int Mic Boost' are for compatibility with kernels < 2.6.38
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 89
description-key = analog-input-microphone-internal

[Jack Mic]
state.plugged = no
state.unplugged = unknown

[<PERSON>c]
state.plugged = no
state.unplugged = unknown

[Jack <PERSON>]
state.plugged = no
state.unplugged = unknown

[Jack <PERSON>]
state.plugged = no
state.unplugged = unknown

[Jack Internal Mic Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Internal Mic Boost]
required-any = any
switch = select
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Option Internal Mic Boost:on]
name = input-boost-on

[Option Internal Mic Boost:off]
name = input-boost-off

[Element Int Mic Boost]
required-any = any
switch = select
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Option Int Mic Boost:on]
name = input-boost-on

[Option Int Mic Boost:off]
name = input-boost-off

[Element Internal Mic]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Int Mic]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Input Source]
enumeration = select

[Option Input Source:Internal Mic]
name = analog-input-microphone-internal
required-any = any

[Option Input Source:Int Mic]
name = analog-input-microphone-internal
required-any = any

[Element Capture Source]
enumeration = select

[Option Capture Source:Internal Mic]
name = analog-input-microphone-internal
required-any = any

[Option Capture Source:Int Mic]
name = analog-input-microphone-internal
required-any = any

[Element Mic]
switch = off
volume = off

[Element Dock Mic]
switch = off
volume = off

[Element Front Mic]
switch = off
volume = off

[Element Rear Mic]
switch = off
volume = off

[Element Headphone Mic]
switch = off
volume = off

[Element Headphone Mic Boost]
switch = off
volume = off

[Element Mic Boost]
switch = off
volume = off

[Element Dock Mic Boost]
switch = off
volume = off

[Element Front Mic Boost]
switch = off
volume = off

[Element Rear Mic Boost]
switch = off
volume = off

.include analog-input-mic.conf.common
