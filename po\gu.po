# translation of PulseAudio.po to Gujarati
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2009, 2012.
#
msgid ""
msgstr ""
"Project-Id-Version: PulseAudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2012-01-30 09:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Gujarati\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: KBabel 1.11.4\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#: src/daemon/cmdline.c:113
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            આ મદદ બતાવો\n"
"      --version                         આવૃત્તિ બતાવો\n"
"      --dump-conf                       મૂળભૂત રૂપરેખાંકનને ડમ્પ કરો\n"
"      --dump-modules                    ઉપલ્બધ મોડ્યુલોની યાદીને ડમ્પ કરો\n"
"      --dump-resample-methods           ઉપલ્બધ resample પદ્દતિઓને ડમ્પ કરો\n"
"      --cleanup-shm                     વાપરેલ વહેંચાયેલ મેમરી સેગમેન્ટોને સાફ કરો\n"
"      --start                           ડિમન ને શરૂ કરો જો તે ચાલી રહ્યુ ન હોય "
"તો\n"
"  -k  --kill                            ચાલી રહેલ ડિમનને મારો\n"
"      --check                           ચાલતી ડિમન માટે ચકાસો (ફક્ત નીકાળેલ કોડ "
"પાછો મળે છે)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   system-wide નમૂના તરીકે ચલાવો\n"
"  -D, --daemonize[=BOOL]                શરૂઆત પછી Daemonize\n"
"      --fail[=BOOL]                     બહાર નીકળો જ્યારે શરૂઆત જ નિષ્ફળ થાય\n"
"      --high-priority[=BOOL]            ઊંચા સરસ સ્તરને સુયોજિત કરવાનો પ્રયત્ન "
"કરો\n"
"                                        (ફક્ત રુટ તરીકે, જ્યારે SUID અથવા\n"
"                                        ઉચ્ચસ્તર થયેલ RLIMIT_NICE સાથે)\n"
"      --realtime[=BOOL]                 રીઅલટાઇમ ગોઠવવાનું સક્રિય કરવા માટે "
"પ્રયત્ન કરો\n"
"                                        (ફક્ત રુટ તરીકે ઉપલ્બધ, જ્યારે SUID અથવા\n"
"                                        ઉચ્ચસ્તર થયેલ RLIMIT_RTPRIO સાથે)\n"
"      --disallow-module-loading[=BOOL]  સૂચિત મોડ્યુલ લોડીંગ/અનલોડીંગ ને શરૂઆત પછી\n"
"                                        મોડ્યુલ વપરાશકર્તાને પરવાનગી ન આપો\n"
"      --disallow-exit[=BOOL]            સૂચિત બહાર નીકળવા વપરાશકર્તાને પરવાનગી "
"ન આપો\n"
"      --exit-idle-time=SECS             ડિમનને બહાર કાઢો જ્યારે નિષ્ક્રિય હોય અને "
"આ\n"
"                                        સમય સમાપ્ત થયેલ હોય તો\n"
"      --module-idle-time=SECS           આપમેળે લોડ થયેલ મોડ્યુલોને લોડ ન કરો જ્યારે "
"તે નિષ્ક્રિય હોય અને\n"
"                                        આ સમય પસાર થયેલ હોય\n"
"      --scache-idle-time=SECS           આપમેળે લોડ થયેલ નમૂનાઓનો લોડ ન કરો જ્યારે "
"નિષ્ક્રિય હોય અને\n"
"                                        આ સમય પસાર થયેલ હોય\n"
"      --log-level[=LEVEL]               વધારો અથવા વર્બોસીટી સ્તરને સુયોજિત કરો\n"
"  -v                                    વર્બોસીટી સ્તરને વધારો\n"
"      --log-target={auto,syslog,stderr} લોગ લક્ષ્યને સ્પષ્ટ કરો\n"
"      --log-meta[=BOOL]                 સંદેશાઓમાં કોડ સ્થાનને સમાવો\n"
"      --log-time[=BOOL]                 લોગ સંદેશાઓમાં ટાઇમસ્ટેમ્પોને સમાવો\n"
"      --log-backtrace=FRAMES            લોગ સંદેશાઓમાં backtrace ને સમાવો\n"
"  -p, --dl-search-path=PATH             ડાયનેમિક વહેંચાયેલ માટે શોધ પાથને સુયોજિત "
"કરો\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          સ્પષ્ટ થયેલ resampling પદ્દતિને વાપરો\n"
"                                        (શક્ય કિંમતો માટે --dump-resample-"
"methods\n"
"                                        ને જુઓ)\n"
"      --use-pid-file[=BOOL]             PID ફાઇલને બનાવો\n"
"      --no-cpu-limit[=BOOL]             પ્લેટફોર્મો પર CPU લોડ મર્યાદા રાખનારને "
"સ્થાપિત ન કરો\n"
"                                        કે જે તેને આધાર આપે છે.\n"
"      --disable-shm[=BOOL]              વહેંચાયેલ મેમરી આધારને નિષ્ક્રિય કરો.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         સ્પષ્ટ થયેલ દલીલ સાથે સ્પષ્ટ થયેલ પલ્ગઇન "
"મોડ્યુલને લોડ\n"
"                                        કરો\n"
"  -F, --file=FILENAME                   સ્પષ્ટ થયેલ સ્ક્રિપ્ટને ચલાવો\n"
"  -C                                    શરૂઆત પછી TTY ચલાવવા પર આદેશ વાક્યને\n"
"                                        ખોલો\n"
"\n"
"  -n                                    મૂળભૂત સ્ક્રિપ્ટ ફાઇલને લોડ કરો નહિં\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level એ લોગ સ્તર દલીલની ઇચ્છા રાખે છે (ક્યાંતો સીમા 0..4 માં પૂર્ણસંખ્યા છે અથવા "
"ડિબગ, જાણકારી, સૂચના, ચેતવણી, ભૂલ નું એક)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:328
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr "અયોગ્ય લોગ લક્ષ્ય: ક્યાંતો 'syslog', 'stderr' અથવા 'auto' ને વાપરો."

#: src/daemon/cmdline.c:330
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr "અયોગ્ય લોગ લક્ષ્ય: ક્યાંતો 'syslog', 'stderr' અથવા 'auto' ને વાપરો."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "અયોગ્ય resample પદ્દતિ '%s'."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/cmdline.c:397
#, fuzzy
msgid "--enable-memfd expects boolean argument"
msgstr "--realtime એ બુલિયન દલીલની ઇચ્છા રાખે છે"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] અયોગ્ય લોગ લક્ષ્ય '%s'."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] અયોગ્ય લોગ સ્તર '%s'."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] અયોગ્ય resample પદ્દતિ '%s'."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] અયોગ્ય rlimit '%s'."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] અયોગ્ય નમૂના બંધારણ '%s'."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] અયોગ્ય નમૂના દર '%s'."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] અયોગ્ય નમૂના ચેનલો '%s'."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] અયોગ્ય ચેનલ મેપ '%s'."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] અયોગ્ય ફ્રેગમેન્ટોનાં નંબર '%s'."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] અયોગ્ય ફ્રેગમેન્ટ માપ '%s'."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] અયોગ્ય સારુ સ્તર '%s'."

#: src/daemon/daemon-conf.c:552
#, fuzzy, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] અયોગ્ય નમૂના દર '%s'."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "રૂપરેખાંકન ફાઇલને ખોલવાનું નિષ્ફળ: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"સ્પષ્ટ થયેલ મૂળભૂત ચેનલ મેપ પાસે સ્પષ્ટ થયેલ ચેનલોની મૂળભૂત સંખ્યા કરતા વિવિધ ચેનલોની સંખ્યા છે."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### રૂપરેખાંકન ફાઇલમાંથી વાંચો: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "નામ: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "મોડ્યુલ જાણકારી ઉપલ્બધ નથી\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "આવૃત્તિ: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "વર્ણન: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "લેખક: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "વપરાશ: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "એકવાર લોડ કરો: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "DEPRECATION WARNING: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "પાથ: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, fuzzy, c-format
msgid "Failed to open module %s: %s"
msgstr "રૂપરેખાંકન ફાઇલ '%s' ને ખોલવામાં નિષ્ફળ: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "મૂળ lt_dlopen લોડરને શોધવામાં નિષ્ફળ."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "નવા dl લોડરને ફાળવવાનું નિષ્ફળ."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "bind-now-loader ને ઉમેરવાનું નિષ્ફળ."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "વપરાશકર્તા '%s' ને શોધવામાં નિષ્ફળ."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "જૂથ '%s' ને શોધવામાં નિષ્ફળ."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "વપરાશકર્તા '%s' અને જૂથ '%s' ની GID બંધબેસતુ નથી."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "વપરાશકર્તાઓ '%s' ની ઘર ડિરેક્ટરી '%s' નથી, અવગણી રહ્યા છે."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "'%s' ને બનાવવામાં નિષ્ફળ: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "જૂથ યાદીને બદલવામાં નિષ્ફળ: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "GID ને બદલવામાં નિષ્ફળ: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "UID ને બદલવામાં નિષ્ફળ: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "આ પ્લેટફોર્મ પર બિનઆધારભૂત સિસ્ટમ વિશાળ સ્થિતિ."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "આદેશ વાક્યને પદચ્છેદન કરવામાં નિષ્ફળ."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "ડિમનને મારવાનું નિષ્ફળ: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"આ પ્રક્રિયાને રુટ તરીકે ચલાવવા માટે વિચાર થયેલ નથી (નહિં તો --system એ સ્પષ્ટ થયેલ છે)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "રુટ અધિકારો જરૂરી છે."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start એ સિસ્ટમ ઉદાહરણો માટે આધારભૂત નથી."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""

#: src/daemon/main.c:878
#, fuzzy
msgid "Running in system mode, but --disallow-exit not set."
msgstr "સિસ્ટમ સ્થિતિમાં ચાલી રહ્યુ છે, પરંતુ --disallow-exit સુયોજિત નથી!"

#: src/daemon/main.c:881
#, fuzzy
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr "સિસ્ટમ સ્થિતિમાં ચાલી રહ્યુ છે, પરંતુ --disallow-module-loading એ સુયોજિત નથી!"

#: src/daemon/main.c:884
#, fuzzy
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "સિસ્ટમ સ્થિતિમાં ચાલી રહ્યુ છે, SHM સ્થિતિને દબાણપૂર્વક નિષ્ક્રિય કરી રહ્યા છે!"

#: src/daemon/main.c:889
#, fuzzy
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"સિસ્ટમ સ્થિતિમાં ચાલી રહ્યુ છે, બહાર નીકળવનાં નિષ્કાર્ય સમયને દબાણપૂર્વક નિષ્ક્રિય કરી "
"રહ્યા છે!"

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "stdio ને મેળવવામાં નિષ્ફળ."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, fuzzy, c-format
msgid "pipe() failed: %s"
msgstr "પાઇપ નિષ્ફળ: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() નિષ્ફળ: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() નિષ્ફળ: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "ડિમન શરૂઆત નિષ્ફળ."

#: src/daemon/main.c:987
#, fuzzy, c-format
msgid "setsid() failed: %s"
msgstr "read() નિષ્ફળ: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "મશીન ID ને મેળવવામાં નિષ્ફળ"

#: src/daemon/main.c:1145
#, fuzzy
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"બરાબર, તેથી તમે સિસ્ટમ સ્થિતિમાં PA ચલાવી રહ્યા છો. મહેરબાની કરીને નોંધો કે જે તમારે "
"મોટેભાગે કરવુ જોઇએ નહિં.\n"
"જો તમે તેનાં વગર કરે તો પછી તે તમારી ભૂલ થે જો ઇચ્છિત રીતે તે કામ કરતુ ન હોય તો.\n"
"શા માટે સિસ્ટમ સ્થિતિ સામાન્ય રીતે ખરાબ વિચાર છે તે માટે વિગતવાર જાણકારી માટે મહેરબાની "
"કરીને http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/User/"
"WhatIsWrongWithSystemWide/ આને વાંચો."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() નિષ્ફળ."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() નિષ્ફળ."

#: src/daemon/main.c:1268
#, fuzzy
msgid "command line arguments"
msgstr "ઘણી બધી દલીલો છે."

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "કોઇપણ લોડ થયેલ મોડ્યુલો વગર ડિમનને શરૂ કરો, કામ કરવા માટે ફરી શરૂ કરી રહ્યા છે."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "PulseAudio સાઉન્ડ સિસ્ટમ"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "PulseAudio સાઉન્ડ સિસ્ટમને શરૂ કરો"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "ઇનપુટ"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "ડોકિંગ સ્ટેશન ઇનપુટ"

#: src/modules/alsa/alsa-mixer.c:2710
#, fuzzy
msgid "Docking Station Microphone"
msgstr "ડોકિંગ સ્ટેશન માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2711
#, fuzzy
msgid "Docking Station Line In"
msgstr "ડોકિંગ સ્ટેશન ઇનપુટ"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "લાઇન-ઇન"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
#, fuzzy
msgid "Front Microphone"
msgstr "ડોકિંગ સ્ટેશન માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
#, fuzzy
msgid "Rear Microphone"
msgstr "માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "બહારનાં માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "આંતરિક માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "રેડિયો"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "વિડિયો"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Automatic Gain Control"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Automatic Gain Control નથી"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "બુસ્ટ"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "બુસ્ટ નથી"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "પરિવર્ધક"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "પરિવર્ધક નથી"

#: src/modules/alsa/alsa-mixer.c:2726
#, fuzzy
msgid "Bass Boost"
msgstr "બુસ્ટ"

#: src/modules/alsa/alsa-mixer.c:2727
#, fuzzy
msgid "No Bass Boost"
msgstr "બુસ્ટ નથી"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "ઍનલૉગ હૅડફોનો"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "ઍનલૉગ ઇનપુટ"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "ડોકિંગ સ્ટેશન માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2802
#, fuzzy
msgid "Headset Microphone"
msgstr "માઇક્રોફોન"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "ઍનલૉગ આઉટપુટ"

#: src/modules/alsa/alsa-mixer.c:2808
#, fuzzy
msgid "Headphones 2"
msgstr "ઍનલૉગ હૅડફોનો"

#: src/modules/alsa/alsa-mixer.c:2809
#, fuzzy
msgid "Headphones Mono Output"
msgstr "ઍનલૉગ મોનો આઉટપુટ"

#: src/modules/alsa/alsa-mixer.c:2810
#, fuzzy
msgid "Line Out"
msgstr "લાઇન-ઇન"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "ઍનલૉગ મોનો આઉટપુટ"

#: src/modules/alsa/alsa-mixer.c:2812
#, fuzzy
msgid "Speakers"
msgstr "ઍનલૉગ સ્ટેરિઓ"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2814
#, fuzzy
msgid "Digital Output (S/PDIF)"
msgstr "ડિજિટલ સ્ટેરિઓ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2815
#, fuzzy
msgid "Digital Input (S/PDIF)"
msgstr "ડિજિટલ સ્ટેરિઓ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2817
#, fuzzy
msgid "Multichannel Output"
msgstr "શૂન્ય આઉટપુટ"

#: src/modules/alsa/alsa-mixer.c:2818
#, fuzzy
msgid "Game Output"
msgstr "શૂન્ય આઉટપુટ"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
#, fuzzy
msgid "Chat Output"
msgstr "શૂન્ય આઉટપુટ"

#: src/modules/alsa/alsa-mixer.c:2821
#, fuzzy
msgid "Chat Input"
msgstr "ઇનપુટ"

#: src/modules/alsa/alsa-mixer.c:2822
#, fuzzy
msgid "Virtual Surround 7.1"
msgstr "ઍનલૉગ સરાઉન્ડ 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "ઍનલૉગ મોનો"

#: src/modules/alsa/alsa-mixer.c:4564
#, fuzzy
msgid "Analog Mono (Left)"
msgstr "ઍનલૉગ મોનો"

#: src/modules/alsa/alsa-mixer.c:4565
#, fuzzy
msgid "Analog Mono (Right)"
msgstr "ઍનલૉગ મોનો"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "ઍનલૉગ સ્ટેરિઓ"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "મોનો"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "સ્ટેરિઓ"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
#, fuzzy
msgid "Speakerphone"
msgstr "ઍનલૉગ સ્ટેરિઓ"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "ઍનલૉગ સરાઉન્ડ 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "ઍનલૉગ સરાઉન્ડ 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "ઍનલૉગ સરાઉન્ડ 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "ઍનલૉગ સરાઉન્ડ 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "ઍનલૉગ સરાઉન્ડ 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "ઍનલૉગ સરાઉન્ડ 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "ઍનલૉગ સરાઉન્ડ 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "ઍનલૉગ સરાઉન્ડ 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "ઍનલૉગ સરાઉન્ડ 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "ઍનલૉગ સરાઉન્ડ 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "ઍનલૉગ સરાઉન્ડ 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "ડિજિટલ સ્ટેરિઓ (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "ડિજિટલ સરાઉન્ડ 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "ડિજિટલ સરાઉન્ડ 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
#, fuzzy
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "ડિજિટલ સરાઉન્ડ 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "ડિજિટલ સ્ટેરિઓ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
#, fuzzy
msgid "Digital Surround 5.1 (HDMI)"
msgstr "ડિજિટલ સરાઉન્ડ 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "ઍનલૉગ મોનો ડુપ્લેક્ષ"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "ઍનલૉગ સ્ટેરિઓ ડુપ્લેક્ષ"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "ડિજિટલ સ્ટેરિઓ ડુપ્લેક્ષ (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4738
#, fuzzy
msgid "Stereo Duplex"
msgstr "ઍનલૉગ સ્ટેરિઓ ડુપ્લેક્ષ"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "બંધ"

#: src/modules/alsa/alsa-mixer.c:4840
#, fuzzy, c-format
msgid "%s Output"
msgstr "શૂન્ય આઉટપુટ"

#: src/modules/alsa/alsa-mixer.c:4848
#, fuzzy, c-format
msgid "%s Input"
msgstr "ઇનપુટ"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, fuzzy, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA એ ઉપકરણમાં નવી માહિતીને લખવા માટે આપણને જગાડતુ હતુ, પરંતુ ત્યાં વાસ્તવમાં કંઇ જ લખાયુ "
"ન હતુ!\n"
"મોટેભાગે આ ALSA ડ્રાઇવર '%s' માં ભૂલ જેવુ છે. મહેરબાની કરીને આ મુદ્દાને ALSA ડેવલપરોમાં "
"અહેવાલ કરો.\n"
"POLLOUT સુયોજન સાથે આપણે જાગેલ હતા -- છતાંપણ ના પછીનું snd_pcm_avail() ને 0 પાછો મળે "
"છે અથવા બીજી કિંમત < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, fuzzy, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA એ ઉપકરણમાં નવી માહિતીને વાંચવા માટે આપણને જગાડતુ હતુ, પરંતુ ત્યાં વાસ્તવમાં કંઇ જ "
"વંચાયુ ન હતુ!\n"
"મોટેભાગે આ ALSA ડ્રાઇવર '%s' માં ભૂલ જેવુ છે. મહેરબાની કરીને આ મુદ્દાને ALSA ડેવલપરોમાં "
"અહેવાલ કરો.\n"
"POLLOUT સુયોજન સાથે આપણે જાગેલ હતા -- છતાંપણ ના પછીનું snd_pcm_avail() ને 0 પાછો મળે "
"છે અથવા બીજી કિંમત < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, fuzzy, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() કિંમતને પાછુ મળેલ છે કે જે અપવાદ રીતે વિશાળ છે: %lu bytes (%lu ms).\n"
"ALSA ડ્રાઇવર '%s' માં મોટેભાગે આ ભૂલ જેવુ છે. ALSA ડેવલ્પરોમાં આ સમસ્યાને મહેરબાની કરીને "
"અહેવાલ કરો."
msgstr[1] ""
"snd_pcm_avail() કિંમતને પાછુ મળેલ છે કે જે અપવાદ રીતે વિશાળ છે: %lu bytes (%lu ms).\n"
"ALSA ડ્રાઇવર '%s' માં મોટેભાગે આ ભૂલ જેવુ છે. ALSA ડેવલ્પરોમાં આ સમસ્યાને મહેરબાની કરીને "
"અહેવાલ કરો."

#: src/modules/alsa/alsa-util.c:1249
#, fuzzy, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() કિંમતને પાછુ મળેલ છે કે જે અપવાદ રીતે વિશાળ છે: %li bytes (%s%lu "
"ms).\n"
"ALSA ડ્રાઇવર '%s' માં મોટેભાગે આ ભૂલ જેવુ છે. ALSA ડેવલ્પરોમાં આ સમસ્યાને મહેરબાની કરીને "
"અહેવાલ કરો."
msgstr[1] ""
"snd_pcm_delay() કિંમતને પાછુ મળેલ છે કે જે અપવાદ રીતે વિશાળ છે: %li bytes (%s%lu "
"ms).\n"
"ALSA ડ્રાઇવર '%s' માં મોટેભાગે આ ભૂલ જેવુ છે. ALSA ડેવલ્પરોમાં આ સમસ્યાને મહેરબાની કરીને "
"અહેવાલ કરો."

#: src/modules/alsa/alsa-util.c:1296
#, fuzzy, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail() કિંમતને પાછુ મળેલ છે કે જે અપવાદ રીતે વિશાળ છે: %lu bytes (%lu ms).\n"
"ALSA ડ્રાઇવર '%s' માં મોટેભાગે આ ભૂલ જેવુ છે. ALSA ડેવલ્પરોમાં આ સમસ્યાને મહેરબાની કરીને "
"અહેવાલ કરો."

#: src/modules/alsa/alsa-util.c:1339
#, fuzzy, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() કિંમતને પાછુ મળેલ છે કે જે અપવાદ રીતે વિશાળ છે: %lu બાઇટો (%lu "
"ms).\n"
"ALSA ડ્રાઇવર '%s' માં મોટેભાગે આ ભૂલ જેવુ છે. ALSA ડેવલ્પરોમાં આ સમસ્યાને મહેરબાની કરીને "
"અહેવાલ કરો."
msgstr[1] ""
"snd_pcm_mmap_begin() કિંમતને પાછુ મળેલ છે કે જે અપવાદ રીતે વિશાળ છે: %lu બાઇટો (%lu "
"ms).\n"
"ALSA ડ્રાઇવર '%s' માં મોટેભાગે આ ભૂલ જેવુ છે. ALSA ડેવલ્પરોમાં આ સમસ્યાને મહેરબાની કરીને "
"અહેવાલ કરો."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
#, fuzzy
msgid "Bluetooth Output"
msgstr "ઍનલૉગ આઉટપુટ"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1971
#, fuzzy
msgid "Headphone"
msgstr "ઍનલૉગ હૅડફોનો"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2042
#, fuzzy
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "High Fidelity Playback (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
#, fuzzy
msgid "High Fidelity Capture (A2DP Source)"
msgstr "High Fidelity Capture (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr ""

#: src/modules/echo-cancel/module-echo-cancel.c:59
#, fuzzy
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr ""

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "ડમી આઉટપુટ"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "હંમેશા ઓછામાં ઓછુ એક સિંક લોડ થયેલ રાખો જો તે શૂન્ય હોય તો પણ"

#: src/modules/module-always-source.c:35
#, fuzzy
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "હંમેશા ઓછામાં ઓછુ એક સિંક લોડ થયેલ રાખો જો તે શૂન્ય હોય તો પણ"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr ""

#: src/modules/module-equalizer-sink.c:72
#, fuzzy
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr ""

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr ""

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "વર્ચ્યુઅલ LADSPA સિંક"

#: src/modules/module-ladspa-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "ક્લોક થયેલ NULL સિંક"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "શૂન્ય આઉટપુટ"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, fuzzy, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "સ્ત્રોત જાણકારીને મેળવવામાં નિષ્ફળતા: %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "આઉટપુટ ઉપકરણો"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "ઇનપુટ ઉપકરણો"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "@HOSTNAME@ પર ઓડિયો"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr ""

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr ""

#: src/modules/module-virtual-surround-sink.c:50
#, fuzzy
msgid "Virtual surround sink"
msgstr "વર્ચ્યુઅલ LADSPA સિંક"

#: src/modules/module-virtual-surround-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#: src/modules/raop/module-raop-discover.c:295
#, fuzzy
msgid "Unknown device model"
msgstr "અજ્ઞાત ભૂલ કોડ"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr ""

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "PulseAudio સાઉન્ડ સર્વર"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "આગળનું કેન્દ્ર"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "આગળ ડાબે"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "આગળ જમણે"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "રિઅર કેન્દ્ર"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "રિઅર ડાબે"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "રિઅર જમણે"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr ""

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "આગળ કેન્દ્રની ડાબે"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "આગળ કેન્દ્રની જમણે"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "ડાબી બાજુ"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "જમણી બાજુ"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "ઑગ્ઝિલિઅરિ 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "ઑગ્ઝિલિઅરિ 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "ઑગ્ઝિલિઅરિ 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "ઑગ્ઝિલિઅરિ 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "ઑગ્ઝિલિઅરિ 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "ઑગ્ઝિલિઅરિ 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "ઑગ્ઝિલિઅરિ 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "ઑગ્ઝિલિઅરિ 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "ઑગ્ઝિલિઅરિ 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "ઑગ્ઝિલિઅરિ 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "ઑગ્ઝિલિઅરિ 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "ઑગ્ઝિલિઅરિ 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "ઑગ્ઝિલિઅરિ 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "ઑગ્ઝિલિઅરિ 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "ઑગ્ઝિલિઅરિ 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "ઑગ્ઝિલિઅરિ 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "ઑગ્ઝિલિઅરિ 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "ઑગ્ઝિલિઅરિ 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "ઑગ્ઝિલિઅરિ 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "ઑગ્ઝિલિઅરિ 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "ઑગ્ઝિલિઅરિ 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "ઑગ્ઝિલિઅરિ 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "ઑગ્ઝિલિઅરિ 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "ઑગ્ઝિલિઅરિ 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "ઑગ્ઝિલિઅરિ 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "ઑગ્ઝિલિઅરિ 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "ઑગ્ઝિલિઅરિ 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "ઑગ્ઝિલિઅરિ 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "ઑગ્ઝિલિઅરિ 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "ઑગ્ઝિલિઅરિ 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "ઑગ્ઝિલિઅરિ 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "ઑગ્ઝિલિઅરિ 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "ઊંચે કેન્દ્ર"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "ઊંચે આગળ કેન્દ્ર"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "ઊંચે આગળ ડાબે"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "ઊંચે આગળ જમણે"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "ઊંચે રિઅર કેન્દ્ર"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "ઉપર રિઅર ડાબે"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "ઉપર રિઅર જમણે"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(અયોગ્ય)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "સરાઉન્ડ 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "સરાઉન્ડ 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "સરાઉન્ડ 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "સરાઉન્ડ 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "સરાઉન્ડ 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
#, fuzzy
msgid "xcb_connect() failed"
msgstr "pa_context_connect() નિષ્ફળ: %s"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr ""

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "કુકીની માહિતીને પદચ્છેદન કરવામાં નિષ્ફળ"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "અજ્ઞાત એક્સટેન્શન '%s' માટે મળેલ સંદેશ"

#: src/pulse/direction.c:37
#, fuzzy
msgid "input"
msgstr "ઇનપુટ"

#: src/pulse/direction.c:39
#, fuzzy
msgid "output"
msgstr "શૂન્ય આઉટપુટ"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr ""

#: src/pulse/direction.c:43
#, fuzzy
msgid "invalid"
msgstr "(અયોગ્ય)"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr ""

#: src/pulsecore/core-util.h:97
#, fuzzy
msgid "no"
msgstr "મોનો"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "autospawn તાળાને દાખલ કરી શકાતુ નથી."

#: src/pulsecore/log.c:165
#, fuzzy, c-format
msgid "Failed to open target file '%s'."
msgstr "સાઉન્ડ ફાઇલને ખોલવામાં નિષ્ફળતા."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""

#: src/pulsecore/log.c:651
#, fuzzy
msgid "Invalid log target."
msgstr "[%s:%u] અયોગ્ય લોગ લક્ષ્ય '%s'."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "આંતરિક ઓડિયો"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "મોડેમ"

#: src/pulse/error.c:38
msgid "OK"
msgstr "બરાબર"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "પ્રવેશનો સ્વીકાર કરેલ નથી"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "અજ્ઞાત આદેશ"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "અયોગ્ય દલીલ"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "વસ્તુ અસ્તિત્વ ધરાવે છે"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "આવી વસ્તુ નથી"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "જોડાણને માન્ય ન કરવુ"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "પ્રોટોકોલ ભૂલ"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "સમય સમાપ્ત"

#: src/pulse/error.c:47
#, fuzzy
msgid "No authentication key"
msgstr "સત્તાધિકરણ કી નથી"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "આંતરિક ભૂલ"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "જોડાણનો અંત થયેલ છે"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "વસ્તુને મારી નંખાયેલ છે"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "અયોગ્ય સર્વર"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "મોડ્યુલ શરૂઆત કરવાનું નિષ્ફળ"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "ખરાબ સ્થિતિ"

#: src/pulse/error.c:54
msgid "No data"
msgstr "માહિતી નથી"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "અસુસંગત પ્રોટોકોલ આવૃત્તિ"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "ઘણું લાંબુ છે"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "આધારભૂત નથી"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "અજ્ઞાત ભૂલ કોડ"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "આવુ એક્સટેન્શન નથી"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "અપ્રચલિત કાર્યત્મકતા"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "ગુમ થયેલ અમલીકરણ"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "ક્લાઇન્ટમાં ફાટા પડેલ છે"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "ઇનપુટ/આઉટપુટ ભૂલ"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "ઉપકરણ અથવા સ્ત્રોત વ્યસ્ત"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "સ્ટ્રીમને નિકાલ કરવામાં નિષ્ફળ: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "પ્લેબેક સ્ટ્રીમ ને નિકાલ કરેલ છે."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "સર્વરમાં જોડાણને નિકાલ કરી રહ્યા છે."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() નિષ્ફળ: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() નિષ્ફળ: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "સ્ટ્રીમ સફળતાપૂર્વક બનાવેલ છે."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() નિષ્ફળ: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "બફર મેટ્રિક્સ: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "બફર મેટ્રિક્સ: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "નમૂનો spec '%s' ને વાપરી રહ્યા છે, ચેનલ મેપ '%s'."

#: src/utils/pacat.c:342
#, fuzzy, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "ઉપકરણ %s (%u, %ssuspended) સાથે જોડાયેલ છે."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "સ્ટ્રીમ ભૂલ: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "સ્ટ્રીમ ઉપકરણ ને થોડા સમય માટે બંધ રાખેલ છે.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "સ્ટ્રીમ ઉપકરણને ફરી શરૂ કરેલ છે.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "સ્ટ્રીમ ચલાવવા હેઠળ છે.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "સ્ટ્રીમ ઉપર ચાલે છે.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "સ્ટ્રીમ શરૂ થયેલ છે.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "સ્ટ્રીમ એ ઉપકરણ %s (%u, %ssuspended) માં ખસેડેલ છે.%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "નથી "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "સ્ટ્રીમ બફર ગુણધર્મો બદલાયેલ છે.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr ""

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr ""

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "જોડાણ સ્થાપિત થયેલ છે.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() નિષ્ફળ: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() નિષ્ફળ: %s"

#: src/utils/pacat.c:497
#, fuzzy, c-format
msgid "Failed to set monitor stream: %s"
msgstr "સ્ટ્રીમને નિકાલ કરવામાં નિષ્ફળ: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() નિષ્ફળ: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "જોડાણ નિષ્ફળ: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF મળ્યુ."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() નિષ્ફળ: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() નિષ્ફળ: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "સંકેત મળ્યું, બહાર નીકળી રહ્યા છે."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "ગુપ્તતા મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Time: %0.3f sec; Latency: %0.0f usec."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() નિષ્ફળ: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [options]\n"
"\n"
"  -h, --help                            આ મદદને બતાવો\n"
"      --version                         આવૃત્તિને બતાવો\n"
"\n"
"  -r, --record                          રેકોર્ડીંગ માટે જોડાણને બનાવો\n"
"  -p, --playback                        પ્લેબેક માટે જોડાણને બનાવો\n"
"\n"
"  -v, --verbose                         વર્બોસ ક્રિયાઓ ને સક્રિય કરો\n"
"\n"
"  -s, --server=SERVER                   તેમાં જોડાવા માટે સર્વરનું નામ\n"
"  -d, --device=DEVICE                   તેમાં જોડાવા માટે સિંક/સ્ત્રોત નું નામ\n"
"  -n, --client-name=NAME                સર્વર પર આ ક્લાઇન્ટને કેવી રીતે કોલ કરવો\n"
"      --stream-name=NAME                સર્વર પર આ સ્ટ્રીમને કેવી રીતે કોલ કરવો\n"
"      --volume=VOLUME                   સીમા 0...65536 માં પ્રારંભનાં (સીધા) "
"વોલ્યુમને સ્પષ્ટ કરો\n"
"      --rate=SAMPLERATE                 Hz માં નમૂનો(44100 નાં મૂળભૂતો)\n"
"      --format=SAMPLEFORMAT             નમૂના પ્રકાર, s16le, s16be, u8, "
"float32le, માંનો એક\n"
"                                        float32be, ulaw, alaw, s32le, s32be, "
"s24le, s24be,\n"
"                                        s24-32le, s24-32be (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               ચેનલોની સંખ્યા, mono માટે 1, stereo માટે "
"2\n"
"                                        (2 એ મૂળભૂત છે)\n"
"      --channel-map=CHANNELMAP          મૂળભૂત ને બદલે વાપરવા માટે ચેનલ મેપ\n"
"      --fix-format                      સ્ટ્રીમ તેમાં જોડાયેલ છે તે સિંક માંથી નમૂના "
"બંધારણને\n"
"                                        લો.\n"
"      --fix-rate                        સ્ટ્રીમ તેમાં જોડાયેલ છે તે સિંક માંથી નમૂના "
"દરને લો.\n"
"      --fix-channels                    સ્ટ્રીમ તેમાં જોડાયેલ છે તે સિંક માંથી ચેનલો "
"અને ચેનલ માપને લો.\n"
"      --no-remix                        ચેનલોને upmix અથવા downmix કરો નહિં.\n"
"      --no-remap                        નામને બદલે અનુક્રમણિકા દ્દારા મેપ ચેનલો.\n"
"      --latency=BYTES                   બાઇટોમાં સ્પષ્ટ થયેલ ગુપ્તતા ની માંગણી "
"કરો.\n"
"      --process-time=BYTES              બાઇટોમાં માંગણી ની સાથે સ્પષ્ટ થયેલ "
"પ્રક્રિયા સમયની માંગણી કરો.\n"
"      --property=PROPERTY=VALUE         સ્પષ્ટ થયેલ કિંમતમાં સ્પષ્ટ થયેલ ગુણધર્મને "
"સુયોજિત કરો.\n"
"      --raw                             કાચી PCM માહિતીનો રેકોર્ડ કરો/વગાડો.\n"
"      --file-format=FFORMAT             બંધારણ થયેલ માહિતીનો રેકોર્ડ કરો/વગાડો.\n"
"      --list-file-formats               ઉપલ્બધ ફાઇલ બંધારણોની યાદી.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s સાથે કમ્પાઇલ થયેલ છે\n"
"libpulse %s સાથે કડી થયેલ છે\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "અયોગ્ય ક્લાઇન્ટ નામ '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "અયોગ્ય સ્ટ્રીમ નામ '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "અયોગ્ય ચેનલ મેપ '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "અયોગ્ય ગુપ્તતા સ્પષ્ટીકરણ '%s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "અયોગ્ય પ્રક્રિયા સમય સ્પષ્ટીકરણ '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "અયોગ્ય ગુણધર્મ '%s'"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "અજ્ઞાત ફાઇલ બંધારણ %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr ""

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "અયોગ્ય નમૂના સ્પષ્ટીકરણ"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "ઘણી બધી દલીલો છે."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "ફાઇલ માટે નમૂના સ્પષ્ટીકરણ ને ઉત્પન્ન કરવામાં નિષ્ફળ."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "સાઉન્ડ ફાઇલને ખોલવામાં નિષ્ફળતા."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr "ચેતવણી: સ્પષ્ટ થયેલ નમૂના સ્પષ્ટીકરણ ફાઇલ માંથી સ્પષ્ટીકરણ સાથે ઉપર લખાયેલ હશે."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "ફાઇલ માંથી નમૂના સ્પષ્ટીકરણને નક્કી કરવામાં નિષ્ફળતા."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "ચેતવણી: ફાઇલમાંથી ચેનલ મેપને નક્કી કરવામાં નિષ્ફળતા."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "ચેનલ મેપ એ સ્પષ્ટીકરણ નમૂનાને બંધબેસતુ નથી"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "ચેતણી: ફાઇલમાં ચેનલ મેપને લખવામાં નિષ્ફળતા."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr "નમૂના સ્પષ્ટીકરણ '%s' અને ચેનલ નક્ષા '%s' સાથે %s સ્ટ્રીમને ખોલી રહ્યા છે."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "રેકોર્ડ કરી રહ્યા છે"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "પ્લેબેક"

#: src/utils/pacat.c:1162
#, fuzzy
msgid "Failed to set media name."
msgstr "આદેશ વાક્યને પદચ્છેદન કરવામાં નિષ્ફળ."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() નિષ્ફળ."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() નિષ્ફળ."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() નિષ્ફળ."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() નિષ્ફળ: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_new() નિષ્ફળ."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() નિષ્ફળ."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr ""

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr ""

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr ""

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr ""

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr ""

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:61
msgid "#N"
msgstr ""

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr ""

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr ""

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr ""

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr ""

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr ""

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr ""

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr ""

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr ""

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr ""

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr ""

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr ""

#: src/utils/pacmd.c:82
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            આ મદદ ને બતાવો\n"
"      --version                         આવૃત્તિને બતાવો\n"
"  -s, --server=SERVER                   જોડાવવા માટે સર્વરનું નામ\n"
"\n"

#: src/utils/pacmd.c:129
#, fuzzy, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s સાથે કમ્પાઇલ થયેલ છે\n"
"libpulse %s સાથે કડી થયેલ છે\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr "PulseAudio ડિમન ચાલી રહ્યુ નથી, અથવા સત્ર ડિમન તરીકે ચાલી રહ્યુ નથી."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "PulseAudio ડિમનને મારવામાં નિષ્ફળ."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "ડિમન એ જવાબ આપતુ નથી."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "પરિસ્થિતિઓને મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:199
#, fuzzy, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "હાલમાં વપરાશમાં છે: %u બ્લોકો %s કુલ બાઇટોને સમાવી રહ્યા છે.\n"
msgstr[1] "હાલમાં વપરાશમાં છે: %u બ્લોકો %s કુલ બાઇટોને સમાવી રહ્યા છે.\n"

#: src/utils/pactl.c:205
#, fuzzy, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "આખી જીંદગી દરમ્યાન ફાળવેલ છે: %u બ્લોકો %s કુલ બાઇટોને સમાવી રહ્યા છે.\n"
msgstr[1] "આખી જીંદગી દરમ્યાન ફાળવેલ છે: %u બ્લોકો %s કુલ બાઇટોને સમાવી રહ્યા છે.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "નમૂના કેશ માપ: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "સર્વર જાણકારી મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, fuzzy, c-format
msgid "%s\n"
msgstr "%s %s"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""

#: src/utils/pactl.c:294
#, fuzzy, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"વપરાશકર્તા નામ: %s\n"
"યજમાન નામ: %s\n"
"સર્વર નામ: %s\n"
"સર્વર આવૃત્તિ: %s\n"
"મૂળભૂત નમૂના સ્પષ્ટીકરણ: %s\n"
"મૂળભૂત ચેનલ મેપ: %s\n"
"મૂળભૂત સિંક: %s\n"
"મૂળભૂત સ્ત્રોત: %s\n"
"કુકી: %08x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr ""

#: src/utils/pactl.c:321
msgid "available"
msgstr ""

#: src/utils/pactl.c:322
msgid "not available"
msgstr ""

#: src/utils/pactl.c:331 src/utils/pactl.c:355
#, fuzzy
msgid "Unknown"
msgstr "અજ્ઞાત આદેશ"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr ""

#: src/utils/pactl.c:335
#, fuzzy
msgid "Line"
msgstr "લાઇન-ઇન"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr ""

#: src/utils/pactl.c:338
msgid "Handset"
msgstr ""

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr ""

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr ""

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr ""

#: src/utils/pactl.c:342
msgid "TV"
msgstr ""

#: src/utils/pactl.c:345
msgid "USB"
msgstr ""

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr ""

#: src/utils/pactl.c:352
msgid "Network"
msgstr ""

#: src/utils/pactl.c:353
#, fuzzy
msgid "Analog"
msgstr "ઍનલૉગ મોનો"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "સિંક જાણકારી મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:664
#, fuzzy, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"સિંક #%u\n"
"\tસ્થિતિ: %s\n"
"\tનામ: %s\n"
"\tવર્ણન: %s\n"
"\tડ્રાઇવર: %s\n"
"\tનમૂના સ્પષ્ટીકરણ: %s\n"
"\tચેનલ મેપ: %s\n"
"\tમાલિક મોડ્યુલ: %u\n"
"\tમૂંગુ: %s\n"
"\tવોલ્યુમ: %s%s%s\n"
"\t        સમતુલન %0.2f\n"
"\tઆધાર વોલ્યુમ: %s%s%s\n"
"\tમોનિટર સ્ત્રોત: %s\n"
"\tગુપ્તતા: %0.0f usec, configured %0.0f usec\n"
"\tફ્લેગો: %s%s%s%s%s%s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tપોર્ટો:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ""

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tસક્રિય પોર્ટ: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, fuzzy, c-format
msgid "\tFormats:\n"
msgstr "\tપોર્ટો:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "સ્ત્રોત જાણકારીને મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:849
#, fuzzy, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"સ્ત્રોત #%u\n"
"\tસ્થિતિ: %s\n"
"\tનામ: %s\n"
"\tવર્ણન: %s\n"
"\tડ્રાઇવર: %s\n"
"\tનમૂના સ્પષ્ટીકરણ: %s\n"
"\tચેનલ મેપ: %s\n"
"\tમાલિક મોડ્યુલ: %u\n"
"\tમૂંગુ: %s\n"
"\tવોલ્યુમ: %s%s%s\n"
"\t        સમતુલન %0.2f\n"
"\tઆધાર વોલ્યુમ: %s%s%s\n"
"\tસિંકનું મોનિટર: %s\n"
"\tગુપ્તતા: %0.0f usec, configured %0.0f usec\n"
"\tફ્લેગો: %s%s%s%s%s%s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "n/a"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "મોડ્યુલની જાણકારી મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"મોડ્યુલ #%u\n"
"\tનામ: %s\n"
"\tદલીલ: %s\n"
"\tવપરાશ ગણતરી: %s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "ક્લાઇન્ટ જાણકારી મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"ક્લાઇન્ટ #%u\n"
"\tડ્રાઇવર: %s\n"
"\tમાલિક મોડ્યુલ: %s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "કાર્ડ જાણકારી મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"કાર્ડ #%u\n"
"\tનામ: %s\n"
"\tડ્રાઇવર: %s\n"
"\tમાલિક મોડ્યુલ: %s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tરૂપરેખાઓ:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tસક્રિય રૂપરેખા: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr ""

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "સિંક ઇનપુટ જાણકારી મેળવવામાં નિષ્ફળતા: %s"

#: src/utils/pactl.c:1366
#, fuzzy, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"સિંક ઇનપુટ #%u\n"
"\tડ્રાઇવર: %s\n"
"\tમાલિક મોડ્યુલ: %s\n"
"\tક્લાઇન્ટ: %s\n"
"\tસિંક: %u\n"
"\tનમૂના સ્પષ્ટીકરણ: %s\n"
"\tચેનલ મેપ %s\n"
"\tમૂંગુ: %s\n"
"\tવોલ્યુમ: %s\n"
"\t        %s\n"
"\t        સમતુલન %0.2f\n"
"\tબફર ગુપ્તતા: %0.0f usec\n"
"\tસિંક ગુપ્તતા: %0.0f usec\n"
"\tResampl પદ્દતિ: %s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "સ્ત્રોત આઉટપુટ જાણકારી મેળવવામાં નિષ્ફળ: %s"

#: src/utils/pactl.c:1489
#, fuzzy, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"સિંક ઇનપુટ #%u\n"
"\tડ્રાઇવર: %s\n"
"\tમાલિક મોડ્યુલ: %s\n"
"\tક્લાઇન્ટ: %s\n"
"\tસિંક: %u\n"
"\tનમૂના સ્પષ્ટીકરણ: %s\n"
"\tચેનલ મેપ %s\n"
"\tમૂંગુ: %s\n"
"\tવોલ્યુમ: %s\n"
"\t        %s\n"
"\t        સમતુલન %0.2f\n"
"\tબફર ગુપ્તતા: %0.0f usec\n"
"\tસિંક ગુપ્તતા: %0.0f usec\n"
"\tResampl પદ્દતિ: %s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "નમૂના જાણકારી મેળવવામાં નિષ્ફળ: %s"

#: src/utils/pactl.c:1604
#, fuzzy, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"નમૂના #%u\n"
"\tનામ: %s\n"
"\tનમૂના સ્પષ્ટીકરણ: %s\n"
"\tચેનલ મેપ: %s\n"
"\tવોલ્યુમ: %s\n"
"\t        %s\n"
"\t        સમતુલન %0.2f\n"
"\tસમયગાળો: %0.1fs\n"
"\tમાપ: %s\n"
"\tઆળસુ: %s\n"
"\tફાઇલનામ: %s\n"
"\tગુણધર્મો:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "નિષ્ફળતા: %s"

#: src/utils/pactl.c:1667
#, fuzzy, c-format
msgid "Send message failed: %s"
msgstr "read() નિષ્ફળ: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr ""

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr ""

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, fuzzy, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "નમૂનાને અપલોડ કરવામાં નિષ્ફળ: %s"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
msgstr[1] ""

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "નમૂનાને અપલોડ કરવામાં નિષ્ફળ: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "ફાઇલનો નિયત સમય પહેલા અંત"

#: src/utils/pactl.c:2144
msgid "new"
msgstr ""

#: src/utils/pactl.c:2147
msgid "change"
msgstr ""

#: src/utils/pactl.c:2150
msgid "remove"
msgstr ""

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr ""

#: src/utils/pactl.c:2161
msgid "sink"
msgstr ""

#: src/utils/pactl.c:2164
msgid "source"
msgstr ""

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr ""

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr ""

#: src/utils/pactl.c:2173
msgid "module"
msgstr ""

#: src/utils/pactl.c:2176
msgid "client"
msgstr ""

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr ""

#: src/utils/pactl.c:2182
#, fuzzy
msgid "server"
msgstr "અયોગ્ય સર્વર"

#: src/utils/pactl.c:2185
msgid "card"
msgstr ""

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr ""

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT મળ્યુ, બહાર નીકળી રહ્યા છે."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "અયોગ્ય નમૂના સ્પષ્ટીકરણ"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr ""

#: src/utils/pactl.c:2594
#, fuzzy
msgid "Invalid number of volume specifications.\n"
msgstr "અયોગ્ય નમૂના સ્પષ્ટીકરણ"

#: src/utils/pactl.c:2606
#, fuzzy
msgid "Inconsistent volume specification.\n"
msgstr "અયોગ્ય નમૂના સ્પષ્ટીકરણ"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr ""

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr ""

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr ""

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr ""

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr ""

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            આ મદદ ને બતાવો\n"
"      --version                         આવૃત્તિને બતાવો\n"
"  -s, --server=SERVER                   જોડાવવા માટે સર્વરનું નામ\n"
"\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"libpulse %s સાથે કમ્પાઇલ થયેલ છે\n"
"libpulse %s સાથે કડી થયેલ છે\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "અયોગ્ય સ્ટ્રીમ નામ '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr ""

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "મહેરબાની કરીને લોડ કરવા માટે નમૂના ફાઇલને સ્પષ્ટ કરો"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "સાઉન્ડ ફાઇલને ખોલવામાં નિષ્ફળ."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "ચેતવણી: ફાઇલ માંથી નમૂના સ્પષ્ટીકરણ કરવાનું નક્કી કરવામાં નિષ્ફળ."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "વગાડવા માટે તમારે નમૂના નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "દૂર કરવા માટે તમારે નમૂના નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "સિંક ઇનપુટ અનુક્રમણિકા અને સિંકને તમારે સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "તમારે સ્ત્રોત આઉટપુટ અનુક્રમણિકા અને સ્ત્રોતને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "તમારે મોડ્યુલ નામ અને દલીલોને સ્પષ્ટ કરવુ જ પડશે."

#: src/utils/pactl.c:2889
#, fuzzy
msgid "You have to specify a module index or name"
msgstr "તમારે મોડ્યુલ અનુક્રમણિકાને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"તમે એક સિંક કરતા વધારે સ્પષ્ટ કરી શકશો નહિં. તમારે બુલિયન કિંમતને સ્પષ્ટ કરવુ જ પડશે."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
#, fuzzy
msgid "Invalid suspend specification."
msgstr "અયોગ્ય નમૂના સ્પષ્ટીકરણ"

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"તમે એક સ્ત્રોત કરતા વધારે સ્પષ્ટ કરી શકશો નહિં. તમારે બુલિયન કિંમતને સ્પષ્ટ કરવુ જ પડશે."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "તમારે કાર્ડ નામ/અનુક્રમણિકા અને પોર્ટ નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "તમારે કાર્ડ નામ/અનુક્રમણિકા અને પોર્ટ નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2961
#, fuzzy
msgid "You have to specify a sink name"
msgstr "વગાડવા માટે તમારે નમૂના નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "તમારે કાર્ડ નામ/અનુક્રમણિકા અને પોર્ટ નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2985
#, fuzzy
msgid "You have to specify a source name"
msgstr "તમારે મોડ્યુલ અનુક્રમણિકાને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
#, fuzzy
msgid "You have to specify a sink name/index"
msgstr "વગાડવા માટે તમારે નમૂના નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "તમારે કાર્ડ નામ/અનુક્રમણિકા અને વોલ્યુમને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
#, fuzzy
msgid "You have to specify a source name/index"
msgstr "તમારે મોડ્યુલ અનુક્રમણિકાને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "તમારે કાર્ડ નામ/અનુક્રમણિકા અને વોલ્યુમ સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "સિંક ઇનપુટ અનુક્રમણિકા અને વોલ્યુમને તમારે સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "અયોગ્ય સિંક ઇનપુટ અનુક્રમણિકા"

#: src/utils/pactl.c:3060
#, fuzzy
msgid "You have to specify a source output index and a volume"
msgstr "તમારે સ્ત્રોત આઉટપુટ અનુક્રમણિકા અને સ્ત્રોતને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3065
#, fuzzy
msgid "Invalid source output index"
msgstr "અયોગ્ય સિંક ઇનપુટ અનુક્રમણિકા"

#: src/utils/pactl.c:3086
#, fuzzy
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "તમારે સિંક નામ/અનુક્રમણિકા અને મૂંગા બુલિયનને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
#, fuzzy
msgid "Invalid mute specification"
msgstr "અયોગ્ય નમૂના સ્પષ્ટીકરણ"

#: src/utils/pactl.c:3111
#, fuzzy
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "તમારે સિંક નામ/અનુક્રમણિકા અને મૂંગા બુલિયનને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3126
#, fuzzy
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr "સિંક ઇનપુટ અનુક્રમણિકા અને મૂંગા બુલિયનને તમારે સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "અયોગ્ય ઇનપુટ અનુક્રમણિકા સ્પષ્ટીકરણ"

#: src/utils/pactl.c:3144
#, fuzzy
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "તમારે સિંક નામ/અનુક્રમણિકા અને મૂંગા બુલિયનને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3149
#, fuzzy
msgid "Invalid source output index specification"
msgstr "અયોગ્ય ઇનપુટ અનુક્રમણિકા સ્પષ્ટીકરણ"

#: src/utils/pactl.c:3162
#, fuzzy
msgid "You have to specify at least an object path and a message name"
msgstr "તમારે કાર્ડ નામ/અનુક્રમણિકા અને પોર્ટ નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""

#: src/utils/pactl.c:3182
#, fuzzy
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr "તમારે સિંક નામ/અનુક્રમણિકા અને મૂંગા બુલિયનને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3194
#, fuzzy
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "તમારે કાર્ડ નામ/અનુક્રમણિકા અને પોર્ટ નામને સ્પષ્ટ કરવુ જ પડશે"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr ""

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "યોગ્ય આદેશ સ્પષ્ટ થયેલ નથી."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "ફરી શરૂ કરવામાં નિષ્ફળતા: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "થોડા સમય માટે બંધ કરવા માટે નિષ્ફળતા: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "ચેતવણી: સાઉન્ડ સર્વર એ સ્થાનિક નથી, થોડા સમય માટે બંધ કરવામાં આવ્યુ નથી.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "જોડાણ નિષ્ફળ: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT મળ્યુ, બહાર નીકળી રહ્યા છે.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "ચેતવણી: બાળ પ્રક્રિયાનો સંકેત %u દ્દારા અંત આવેલ છે\n"

#: src/utils/pasuspender.c:228
#, fuzzy, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            આ મદદ ને બતાવો\n"
"      --version                         આવૃત્તિને બતાવો\n"
"  -s, --server=SERVER                   જોડાવવા માટે સર્વરનું નામ\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"libpulse %s સાથે કમ્પાઇલ થયેલ છે\n"
"libpulse %s સાથે કડી થયેલ છે\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() નિષ્ફળ.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() નિષ્ફળ.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() નિષ્ફળ.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    X11 દેખાવ (મૂળભૂત) માં જોડાયેલ હાલની PulseAudio માહિતીને બતાવો\n"
" -e    X11 દેખાવમાં સ્થાનિય PulseAudio માહિતીની નિકાસ કરો\n"
" -i    સ્થાનિક પર્યાવરણ ચલો અને કુકી ફાઇલમાં X11 દેખાવમાંથી PulseAudio માહિતીને આયાત "
"કરો.\n"
" -r    X11 દેખાવમાંથી PulseAudio માહિતીને દૂર કરો\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "આદેશ વાક્યને પદચ્છેદન કરવામાં નિષ્ફળ.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "સર્વર: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "સ્ત્રોત: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "સિંક: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "કુકી: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "કુકી માહિતીને પદચ્છેદન કરવામાં નિષ્ફળ\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "કુકી માહિતીને સંગ્રહ કરવામાં નિષ્ફળ\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "FQDN ને મેળવવામાં નિષ્ફળ.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "કુકી માહિતીને લોડ કરવામાં નિષ્ફળ\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "હજુ અમલીકરણ થયેલ નથી.\n"

#~ msgid "Got signal %s."
#~ msgstr "સંકેત %s મળ્યુ."

#~ msgid "Exiting."
#~ msgstr "બહાર નીકળી રહ્યા છે."

#~ msgid "Found user '%s' (UID %lu) and group '%s' (GID %lu)."
#~ msgstr "વપરાશકર્તા '%s' (UID %lu) અને જૂથ '%s' (GID %lu) શોધાયુ."

#~ msgid "Successfully dropped root privileges."
#~ msgstr "સફળતાપૂર્વક છોડી દીધેલ રુટ અધિકારો."

#~ msgid "setrlimit(%s, (%u, %u)) failed: %s"
#~ msgstr "setrlimit(%s, (%u, %u)) નિષ્ફળ: %s"

#~ msgid "Daemon not running"
#~ msgstr "ડિમન ચાલી રહ્યુ નથી"

#~ msgid "Daemon running as PID %u"
#~ msgstr "PID %u તરીકે ડિમન ચાલી રહ્યુ છે"

#~ msgid "Daemon startup successful."
#~ msgstr "ડિમન શરૂઆત કરવુ સફળ છે."

#~ msgid "This is PulseAudio %s"
#~ msgstr "આ PulseAudio %s છે"

#~ msgid "Compilation host: %s"
#~ msgstr "કમ્પાઇલેશન યજમાન: %s"

#~ msgid "Compilation CFLAGS: %s"
#~ msgstr "કમ્પાઇલેશન CFLAGS: %s"

#~ msgid "Running on host: %s"
#~ msgstr "યજમાન પર ચાલી રહ્યુ છે: %s"

#~ msgid "Found %u CPUs."
#~ msgstr "%u CPUs શોધાયુ."

#~ msgid "Page size is %lu bytes"
#~ msgstr "પાનાંનુ માપ %lu બાઇટો છે"

#~ msgid "Compiled with Valgrind support: yes"
#~ msgstr "Valgrind આધાર સાથે કમ્પાઇલ થયેલ છે: હા"

#~ msgid "Compiled with Valgrind support: no"
#~ msgstr "Valgrind આધાર સાથે કમ્પાઇલ થયેલ છે: ના"

#~ msgid "Running in valgrind mode: %s"
#~ msgstr "valgrind સ્થિતિમાં ચાલી રહ્યુ છે: %s"

#, fuzzy
#~ msgid "Running in VM: %s"
#~ msgstr "યજમાન પર ચાલી રહ્યુ છે: %s"

#~ msgid "Optimized build: yes"
#~ msgstr "શ્રેષ્ટ થયેલ બિલ્ડ: હા"

#~ msgid "Optimized build: no"
#~ msgstr "શ્રેષ્ટ થયેલ બિલ્ડ: ના"

#~ msgid "NDEBUG defined, all asserts disabled."
#~ msgstr "NDEBUG વ્યાખ્યાયિત થયેલ છે, બધા હકો નિષ્ક્રિય થયેલ છે."

#~ msgid "FASTPATH defined, only fast path asserts disabled."
#~ msgstr "FASTPATH વ્યાખ્યાયિત થયેલ છે, ફક્ત ઝડપી પાથનાં હકો નિષ્ક્રિય થયેલ છે."

#~ msgid "All asserts enabled."
#~ msgstr "બધા હકો સક્રિય થયેલ છે."

#~ msgid "Machine ID is %s."
#~ msgstr "મશીન ID %s છે."

#~ msgid "Session ID is %s."
#~ msgstr "સત્ર ID %s છે."

#~ msgid "Using runtime directory %s."
#~ msgstr "રનટાઇમ ડિરેક્ટરી %s ને વાપરી રહ્યા છે."

#~ msgid "Using state directory %s."
#~ msgstr "સ્થિતિ ડિરેક્ટરી %s ને વાપરી રહ્યા છે."

#~ msgid "Using modules directory %s."
#~ msgstr "ઇોડ્યુલોમ ડિરેક્ટરી %s ને વાપરી રહ્યા છે."

#~ msgid "Running in system mode: %s"
#~ msgstr "સિસ્ટમ સ્થિતિમાં ચાલી રહ્યુ છે: %s"

#~ msgid "Fresh high-resolution timers available! Bon appetit!"
#~ msgstr "તાજુ high-resolution ટાઇમરો ઉપલ્બધ છે! બોન એપેટાઇટ!"

#~ msgid ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"
#~ msgstr ""
#~ "મિત્ર, તમારુ કર્નલમાં ગડબડ છે! રસોઇયાનું આજે ભલામણ એ સક્રિય થયેલ high-resolution "
#~ "ટાઇમરો સાથે Linux નું છે!"

#~ msgid "Failed to initialize daemon."
#~ msgstr "ડિમનને શરૂ કરવામાં નિષ્ફળ."

#~ msgid "Daemon startup complete."
#~ msgstr "ડિમન પારંભ કરવાનું સમાપ્ત છે."

#~ msgid "Daemon shutdown initiated."
#~ msgstr "ડિમનને બંધ કરવાનું પ્રારંભ થયેલ છે."

#~ msgid "Daemon terminated."
#~ msgstr "ડિમનનો અંત આવેલ છે."

#~ msgid "Cleaning up privileges."
#~ msgstr "અધિકારોને છોડી રહ્યા છે."

#, fuzzy
#~ msgid "PulseAudio Sound System KDE Routing Policy"
#~ msgstr "PulseAudio સાઉન્ડ સિસ્ટમ"

#, fuzzy
#~ msgid "Start the PulseAudio Sound System with KDE Routing Policy"
#~ msgstr "PulseAudio સાઉન્ડ સિસ્ટમને શરૂ કરો"

#~ msgid "No cookie loaded. Attempting to connect without."
#~ msgstr "કુકી લોડ થયેલ નથી. તેનાં વગર જોડવાનો પ્રયત્ન કરી રહ્યા છે."

#~ msgid "Failed to load client configuration file.\n"
#~ msgstr "ક્લાઇન્ટ રૂપરેખાંકન ફાઇલને લોડ કરવામાં નિષ્ફળ.\n"

#~ msgid "Failed to read environment configuration data.\n"
#~ msgstr "પર્યાવરણ રૂપરેખાંકન માહિતીને વાંચવામાં નિષ્ફળ.\n"

#~ msgid "Telephony Duplex (HSP/HFP)"
#~ msgstr "Telephony Duplex (HSP/HFP)"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "ઍનલૉગ આઉટપુટ (LFE)"

#, fuzzy
#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "ડિજિટલ સ્ટેરિઓ (HDMI)"

#, fuzzy
#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "ડિજિટલ સ્ટેરિઓ (IEC958)"

#~ msgid "[%s:%u] rlimit not supported on this platform."
#~ msgstr "[%s:%u] rlimit એ આ પ્લેટફોર્મ પર આધારભૂત નથી."

#~ msgid "XOpenDisplay() failed"
#~ msgstr "XOpenDisplay() નિષ્ફળ"

#~ msgid ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"
#~ msgstr ""
#~ "સ્ત્રોત આઉટપુટ #%u\n"
#~ "\tડ્રાઇવર: %s\n"
#~ "\tમાલિક મોડ્યુલ: %s\n"
#~ "\tક્લાઇન્ટ: %s\n"
#~ "\tસ્ત્રોત: %u\n"
#~ "\tનમૂના સ્પષ્ટીકરણ: %s\n"
#~ "\tચેનલ મેપ %s\n"
#~ "\tબફર ગુપ્તતા: %0.0f usec\n"
#~ "\tસિંક ગુપ્તતા: %0.0f usec\n"
#~ "\tResampl પદ્દતિ: %s\n"
#~ "\tગુણધર્મો:\n"
#~ "\t\t%s\n"

#, fuzzy
#~ msgid ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "%s [options] subscribe\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ msgstr ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"

#~ msgid "%s+%s"
#~ msgstr "%s+%s"

#~ msgid "%s / %s"
#~ msgstr "%s / %s"

#~ msgid "Digital Surround 4.0 (IEC958)"
#~ msgstr "ડિજિટલ સરાઉન્ડ 4.0 (IEC958)"

#~ msgid "Low Frequency Emmiter"
#~ msgstr "Low Frequency Emmiter"
