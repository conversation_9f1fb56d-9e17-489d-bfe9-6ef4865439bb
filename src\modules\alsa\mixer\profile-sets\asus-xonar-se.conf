# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; ASUS Xonar SE card.
; This card has two devices for each rear and front panel jacks.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = yes

[Mapping analog-stereo-headset]
device-strings = hw:%f,1
channel-map = left,right
paths-output = analog-output analog-output-headphones
paths-input = analog-input-mic analog-input-headphone-mic analog-input-headset-mic
priority = 15

[Mapping analog-stereo]
device-strings = hw:%f,0
channel-map = left,right
paths-output = analog-output analog-output-speaker
paths-input = analog-input analog-input-mic analog-input-linein
priority = 14

[Mapping analog-surround-21]
device-strings = surround21:%f
channel-map = front-left,front-right,lfe
paths-output = analog-output-speaker
priority = 13
direction = output

[Mapping analog-surround-40]
device-strings = surround40:%f
channel-map = front-left,front-right,rear-left,rear-right
paths-output = analog-output-speaker
priority = 12
direction = output

[Mapping analog-surround-41]
device-strings = surround41:%f
channel-map = front-left,front-right,rear-left,rear-right,lfe
paths-output = analog-output-speaker
priority = 13
direction = output

[Mapping analog-surround-50]
device-strings = surround50:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center
paths-output = analog-output-speaker
priority = 12
direction = output

[Mapping analog-surround-51]
device-strings = surround51:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
paths-output = analog-output-speaker
priority = 13
direction = output

[Mapping iec958-stereo]
device-strings = iec958:%f
channel-map = left,right
paths-output = iec958-stereo-output
priority = 5

[Mapping iec958-ac3-surround-40]
device-strings = a52:%f
channel-map = front-left,front-right,rear-left,rear-right
paths-output = iec958-stereo-output
priority = 2
direction = output

[Mapping iec958-ac3-surround-51]
device-strings = a52:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
paths-output = iec958-stereo-output
priority = 3
direction = output
