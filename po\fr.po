# French translation of pulseaudio.
# Copyright (C) 2006-2008 <PERSON><PERSON><PERSON> Poettering
# This file is distributed under the same license as the pulseaudio package.
#
#
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2008.
# <PERSON><PERSON><PERSON> <telimektar esraonline com>, 2008.
# <PERSON> <<EMAIL>>, 2008.
# Core<PERSON> <<EMAIL>>, 2009.
# <PERSON> <<EMAIL>>, 2009, 2012.
# <PERSON> <<EMAIL>>, 2016. #zanata
# <AUTHOR> <EMAIL>, 2016. #zanata
# <AUTHOR> <EMAIL>, 2017. #zanata
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2023-03-19 17:20+0000\n"
"Last-Translator: grimst <<EMAIL>>\n"
"Language-Team: French <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/fr/>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.15.2\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [options]\n"
"\n"
"COMMANDES :\n"
"  -h, --help                            Afficher cette aide\n"
"      --version                         Afficher la version\n"
"      --dump-conf                       Vider la configuration par défaut\n"
"      --dump-modules                    Vider la liste des modules "
"disponibles\n"
"      --dump-resample-methods           Vider les méthodes resample "
"disponibles\n"
"      --cleanup-shm                     Effacer les segments usés de la "
"mémoire partagée\n"
"      --start                           Lancer le démon s’il n’est pas en "
"cours d’exécution\n"
"  -k  --kill                            Tuer un démon en cours d’exécution\n"
"      --check                           Rechercher un démon en cours d’"
"exécution (retourne uniquement un code de sortie)\n"
"\n"
"OPTIONS :\n"
"      --system[=BOOL]                   Exécuter en tant qu’instance "
"globale\n"
"  -D, --daemonize[=BOOL]                Démoniser après le lancement\n"
"      --fail[=BOOL]                     Quitter si le lancement échoue\n"
"      --high-priority[=BOOL]            Tenter de définir un niveau nice "
"élevé\n"
"                                        (uniquement disponible en tant que "
"root, lorsque SUID ou\n"
"                                        avec RLIMIT_NICE élevé)\n"
"      --realtime[=BOOL]                 Tenter d’activer la planification "
"realtime\n"
"                                        (uniquement disponible en tant que "
"root, lorsque SUID ou\n"
"                                        avec RLIMIT_RTPRIO élevé)\n"
"      --disallow-module-loading[=BOOL]   Interdire le chargement ou "
"déchargement de modules\n"
"                                        requis par l’utilisateur de module "
"après le lancement\n"
"      --disallow-exit[=BOOL]            Interdire la sortie requise par l’"
"utilisateur\n"
"      --exit-idle-time=SECS             Quitter le démon lorsqu’inactif et "
"que cette\n"
"                                        période s’est écoulée\n"
"      --scache-idle-time=SECS           Décharger les samples chargés "
"automatiquement lorsqu’inactif et que cette\n"
"                                        période s’est écoulée\n"
"      --log-level[=LEVEL]               Augmenter ou définir le niveau de "
"détail\n"
"  -v  --verbose                         Augmenter le niveau de détail\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Indiquer la cible du journal\n"
"      --log-meta[=BOOL]                 Inclure l’emplacement du code dans "
"les messages journaux\n"
"      --log-time[=BOOL]                 Inclure l’horodatage dans les "
"messages journaux\n"
"      --log-backtrace=FRAMES            Inclure un backtrace dans les "
"messages journaux\n"
"  -p, --dl-search-path=PATH             Définir le chemin de recherche pour "
"les objets dynamiques\n"
"                                        partagés (greffons)\n"
"      --resample-method=METHOD          Utiliser la méthode resample "
"spécifiée\n"
"                                        (Voir --dump-resample-methods pour\n"
"                                        les valeurs possibles)\n"
"      --use-pid-file[=BOOL]             Créer un fichier PID\n"
"      --no-cpu-limit[=BOOL]             Ne pas installer de limiteur de "
"charge CPU sur\n"
"                                        les platformes qui le prenne en "
"charge\n"
"      --disable-shm[=BOOL]              Désactiver la prise en charge de "
"mémoire partagée.\n"
"      --enable-memfd[=BOOL]             Activer le support de mémoire "
"partagée memfd.\n"
"\n"
"SCRIPT DE LANCEMENT :\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Charger le module du greffon "
"spécifié avec\n"
"                                        l’argument spécifié\n"
"  -F, --file=FILENAME                   Lancer le script spécifié\n"
"  -C                                    Ouvrir une ligne de commande sur le "
"TTY en cours d’exécution\n"
"                                        après le lancement\n"
"\n"
"  -n                                    Ne pas charger le fichier du script "
"par défaut\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize requiert un paramètre booléen"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail requiert un paramètre booléen"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level requiert un paramètre de niveau de journal (soit numérique entre "
"0 et 4, soit de débogage : info, notice, warn , error)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority requiert un paramètre booléen"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime requiert un paramètre booléen"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading requiert un paramètre booléen"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit requiert un paramètre booléen"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file requiert un paramètre booléen"

#: src/daemon/cmdline.c:328
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Cible du journal invalide : veuillez utiliser « syslog », "
"« journal »,« stderr » ou « auto », ou un nom de fichier valide « file:"
"<path> », « newfile:<path> »."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Cible du journal invalide : veuillez utiliser « syslog »,« stderr » ou "
"« auto », ou un nom de fichier valide « file:<path> », « newfile:<path> »."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time requiert un paramètre booléen"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta requiert un paramètre booléen"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Méthode de rééchantillonnage invalide « %s »."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system requiert un paramètre booléen"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit requiert un paramètre booléen"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm requiert un paramètre booléen"

#: src/daemon/cmdline.c:397
#, fuzzy
msgid "--enable-memfd expects boolean argument"
msgstr "--realtime requiert un paramètre booléen"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Cible du journal « %s » invalide."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Niveau du journal « %s » invalide."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Méthode de rééchantillonnage « %s » invalide."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] rlimit « %s » invalide."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Format d’échantillon « %s » invalide."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Taux d’échantillonnage « %s » invalide."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Canaux d’échantillonnage « %s » invalides."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Plan de canaux « %s » invalide."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Nombre de fragments « %s » invalide."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Taille du fragment « %s » invalide."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Niveau de priorité (nice) « %s » invalide."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Type de serveur « %s » invalide."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Échec lors de l’ouverture du fichier de configuration : %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"Le plan de canaux spécifié par défaut a un nombre de canaux différent du "
"nombre spécifié par défaut."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Lecture à partir du fichier de configuration : %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Nom : %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Aucune information de module disponible\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Version : %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Description : %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Auteur : %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Utilisation : %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Chargement unique : %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "AVERTISSEMENT D’OBSOLESCENCE : %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Chemin : %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "Échec d’ouverture du module %s : %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Échec lors de la recherche du chargeur lt_dlopen original."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Échec lors de l’allocation du nouveau chargeur dl."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Échec lors de l’ajout du chargeur bind-now."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Impossible de trouver l’utilisateur « %s »."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Impossible de trouver le groupe « %s »."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr ""
"Le GID de l’utilisateur « %s » et du groupe « %s » ne sont pas identiques."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "Le dossier personnel de l’utilisateur « %s » n’est pas « %s », ignoré."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Échec lors de la création de « %s » : %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Échec lors du changement de la liste du groupe : %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Échec lors du changement de GID : %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Échec lors du changement d’UID : %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "Mode système étendu non pris en charge sur cette plateforme."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Échec lors de l’analyse de la ligne de commande."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Mode système refusé pour les utilisateurs non root. Lancement du service de "
"recherche du serveur D-Bus uniquement."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Impossible de tuer le démon : %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Le programme n’est pas conçu pour être lancé en tant que root (sauf si --"
"system est renseigné)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Les privilèges root sont nécessaires."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start n’est pas pris en charge pour les instances système."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""
"Serveur configuré par l’utilisateur sur %s, refus du lancement start/"
"autospawn."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Serveur configuré par l’utilisateur sur %s, qui semble être local. Analyse "
"plus précise en cours."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "Exécution en mode système, mais --disallow-exit n’est pas défini."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"Exécution en mode système, mais --disallow-module-loading n’est pas défini."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "Exécution en mode système, désactivation forcée du mode SHM."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"Exécution en mode système, désactivation forcée de la fermeture après délai "
"d’inactivité."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Échec lors de l’acquisition de stdio."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "Échec de pipe() : %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "Échec de fork() : %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "Échec de read() : %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Échec lors du démarrage du démon."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "Échec de setsid() : %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Échec lors de l’obtention de l’ID de la machine"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"Ok, vous êtes en train d'exécuter PA en mode système. Assurez-vous que vous "
"voulez vraiment faire cela.\n"
"Veuillez lire http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ pour savoir pourquoi le mode "
"système est généralement une mauvaise idée."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "Échec de pa_pid_file_create()."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "Échec de pa_core_new()."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "paramètres de la ligne de commande"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "Démarrage du démon sans aucun module chargé : refus de fonctionner."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "Système de son PulseAudio"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Démarrer le système de son PulseAudio"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Entrée"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Entrée de la station d’accueil"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Microphone de la station d’accueil"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "Entrée ligne de la station d’accueil"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "Entrée ligne"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Microphone"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Microphone avant"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Microphone arrière"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Microphone externe"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Microphone interne"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Radio"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Vidéo"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Contrôle automatique du gain"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Pas de contrôle automatique du gain"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Boost"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Pas de boost"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Amplificateur"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Pas d’amplificateur"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Booster de basses"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Pas de booster de basses"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Haut-parleur"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Casque audio"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Entrée analogique"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Microphone de la station d’accueil"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Microphone casque"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Sortie analogique"

#: src/modules/alsa/alsa-mixer.c:2808
#, fuzzy
msgid "Headphones 2"
msgstr "Casque audio"

#: src/modules/alsa/alsa-mixer.c:2809
#, fuzzy
msgid "Headphones Mono Output"
msgstr "Sortie mono analogique"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "Sortie ligne"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Sortie mono analogique"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Haut-parleurs"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Sortie numérique (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Entrée numérique (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
#, fuzzy
msgid "Multichannel Input"
msgstr "Multicanal"

#: src/modules/alsa/alsa-mixer.c:2817
#, fuzzy
msgid "Multichannel Output"
msgstr "Multicanal"

#: src/modules/alsa/alsa-mixer.c:2818
#, fuzzy
msgid "Game Output"
msgstr "Sortie %s"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
#, fuzzy
msgid "Chat Output"
msgstr "Sortie %s"

#: src/modules/alsa/alsa-mixer.c:2821
#, fuzzy
msgid "Chat Input"
msgstr "Entrée %s"

#: src/modules/alsa/alsa-mixer.c:2822
#, fuzzy
msgid "Virtual Surround 7.1"
msgstr "Destination surround virtuelle"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Mono analogique"

#: src/modules/alsa/alsa-mixer.c:4564
#, fuzzy
msgid "Analog Mono (Left)"
msgstr "Mono analogique"

#: src/modules/alsa/alsa-mixer.c:4565
#, fuzzy
msgid "Analog Mono (Right)"
msgstr "Mono analogique"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Stéréo analogique"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Mono"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Stéréo"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Casque"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
#, fuzzy
msgid "Speakerphone"
msgstr "Haut-parleur"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Multicanal"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Surround analogique 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Surround analogique 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Surround analogique 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Surround analogique 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Surround analogique 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Surround analogique 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Surround analogique 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Surround analogique 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Surround analogique 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Surround analogique 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Surround analogique 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Stéréo numérique (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Surround numérique 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Surround numérique 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Surround numérique 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Stéréo numérique (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Surround numérique 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Duplex Mono analogique"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Duplex stéréo analogique"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Duplex stéréo numérique (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
#, fuzzy
msgid "Multichannel Duplex"
msgstr "Multicanal"

#: src/modules/alsa/alsa-mixer.c:4738
#, fuzzy
msgid "Stereo Duplex"
msgstr "Duplex stéréo analogique"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Éteint"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "Sortie %s"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "Entrée %s"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, fuzzy, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA nous a réveillés pour écrire de nouvelles données à partir du "
"périphérique, mais il n’y avait en fait rien à écrire !\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA.\n"
"Nous avons été réveillés avec POLLOUT actif, cependant un snd_pcm_avail() "
"ultérieur a retourné 0 ou une autre valeur < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, fuzzy, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA nous a réveillés pour lire de nouvelles données à partir du "
"périphérique, mais il n’y avait en fait rien à lire !\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA.\n"
"Nous avons été réveillés avec POLLIN actif, cependant un snd_pcm_avail() "
"ultérieur a retourné 0 ou une autre valeur < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, fuzzy, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() a retourné une valeur qui est exceptionnellement large : %lu "
"octets (%lu ms).\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA."
msgstr[1] ""
"snd_pcm_avail() a retourné une valeur qui est exceptionnellement large : %lu "
"octets (%lu ms).\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA."

#: src/modules/alsa/alsa-util.c:1249
#, fuzzy, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() a retourné une valeur qui est exceptionnellement large : %li "
"octets (%s%lu ms).\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA."
msgstr[1] ""
"snd_pcm_delay() a retourné une valeur qui est exceptionnellement large : %li "
"octets (%s%lu ms).\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() a retourné des valeurs inhabituelles : le délai %lu "
"est inférieur au %lu disponible.\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA."

#: src/modules/alsa/alsa-util.c:1339
#, fuzzy, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() a retourné une valeur qui est exceptionnellement "
"large : %lu octets (%lu·ms).\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA."
msgstr[1] ""
"snd_pcm_mmap_begin() a retourné une valeur qui est exceptionnellement "
"large : %lu octets (%lu·ms).\n"
"Il s’agit très probablement d’un bogue dans le pilote ALSA « %s ». Veuillez "
"rapporter ce problème aux développeurs d’ALSA."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Entrée Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Sortie Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Mains-libres"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Écouteurs"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Portable"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Voiture"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "HiFi"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Téléphone"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "Lecture haute fidélité (A2DP Sink)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "Capture haute fidélité (A2DP Source)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
#, fuzzy
msgid "Headset Head Unit (HSP)"
msgstr "Unité centrale du casque (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
#, fuzzy
msgid "Headset Audio Gateway (HSP)"
msgstr "Passerelle Audio du casque (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
#, fuzzy
msgid "Handsfree Head Unit (HFP)"
msgstr "Unité centrale du casque (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
#, fuzzy
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Passerelle Audio du casque (HSP/HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
#, fuzzy
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<nom de la source> source_properties=<propriétés de la source> "
"source_master=<nom de la source à filtrer> sink_name=<nom de la destination> "
"sink_properties=<propriétés de la destination> sink_master=<nom de la "
"destination à filtrer> adjust_time=<fréquence de réajustement des taux dans "
"s> adjust_threshold=<décalage à réajuster en ms> format=<format des "
"échantillons> rate=<taux d’échantillonnage> channels=<nombre de canaux> "
"channel_map=<plan des canaux> aec_method=<implémentation à utiliser> "
"aec_args=<paramètres du moteur AEC> save_aec=<enregistrer les données AEC "
"dans /tmp> autoloaded=<définir si ce module est chargé automatiquement> "
"use_volume_sharing=<oui ou non> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "Marche"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Sortie factice"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "Garde toujours au moins une destination même si elle est vide"

#: src/modules/module-always-source.c:35
#, fuzzy
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "Garde toujours au moins une destination même si elle est vide"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "Égaliseur à but général"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<nom de la destination> sink_properties=<propriétés de la "
"destination> sink_master=<destination à laquelle se connecter> "
"format=<format de l’échantillon> rate=<taux d’échantillonnage> "
"channels=<nombre de canaux> channel_map=<plan des canaux> "
"autoloaded=<définir si ce module est chargé automatiquement> "
"use_volume_sharing=<oui ou non> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr ""

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<décharger automatiquement les filtres non utilisés ?>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Destination virtuelle LADSPA"

#: src/modules/module-ladspa-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<nom de la destination> sink_properties=<propriétés de la "
"destination> master=<nom de la destination à filter> format=<format de "
"l’échantillon> rate=<taux d’échantillonnage> channels=<nombre de canaux> "
"channel_map=<plan des canaux> plugin=<nom de l’extension ladspa> "
"label=<étiquette de l’extension ladspa> control=<liste des valeurs de "
"contrôle de l’entrée séparées par des virgules> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Horloge de la destination vide"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Sortie vide"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Impossible de définir le format : format de la chaîne %s invalide"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Périphériques de sortie"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Périphériques d’entrée"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "Audio sur @HOSTNAME@"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "Tunnel pour %s@%s"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "Tunnel vers %s/%s"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Destination surround virtuelle"

#: src/modules/module-virtual-surround-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<nom de la destination> sink_properties=<propriétés de la "
"destination> master=<nom de la destination à filtrer> format=<format de "
"l’échantillon> rate=<taux d’échantillonnage> channels=<nombre de canaux> "
"channel_map=<plan des canaux> use_volume_sharing=<oui ou non> "
"force_flat_volume=<oui ou non> hrir=/path/to/left_hrir.wav "

#: src/modules/raop/module-raop-discover.c:295
#, fuzzy
msgid "Unknown device model"
msgstr "Code d’erreur inconnu"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr ""

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "Serveur de son PulseAudio"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Avant centre"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Avant gauche"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Avant droit"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Arrière centre"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Arrière gauche"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Arrière droit"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Caisson de basses"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Avant à gauche du centre"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Avant à droite du centre"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Côté gauche"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Côté droit"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Auxiliaire 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Auxiliaire 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Auxiliaire 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Auxiliaire 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Auxiliaire 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Auxiliaire 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Auxiliaire 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Auxiliaire 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Auxiliaire 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Auxiliaire 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Auxiliaire 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Auxiliaire 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Auxiliaire 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Auxiliaire 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Auxiliaire 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Auxiliaire 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Auxiliaire 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Auxiliaire 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Auxiliaire 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Auxiliaire 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Auxiliaire 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Auxiliaire 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Auxiliaire 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Auxiliaire 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Auxiliaire 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Auxiliaire 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Auxiliaire 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Auxiliaire 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Auxiliaire 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Auxiliaire 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Auxiliaire 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Auxiliaire 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Centre haut"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Avant centre haut"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Avant gauche haut"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Avant droit haut"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Arrière centre haut"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Arrière gauche haut"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Arrière droit haut"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(invalide)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Surround 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Surround 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Surround 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Surround 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Surround 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "Échec de xcb_connect()"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() a retourné une valeur true"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Échec lors de l’analyse des données du cookie"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork() : %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid() : %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Message reçu pour une extension inconnue « %s »"

#: src/pulse/direction.c:37
msgid "input"
msgstr "entrée"

#: src/pulse/direction.c:39
msgid "output"
msgstr "sortie"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "bidirectionnel"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "non valide"

#: src/pulsecore/core-util.c:1790
#, fuzzy, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) ne nous appartient pas (uid %d), mais appartient à uid "
"%d ! (Ceci peut se produire si par exemple vous tentez de vous connecter à "
"un PulseAudio non root en tant qu’utilisateur root sur le protocole natif. "
"Veuillez ne pas faire cela.)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "oui"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "non"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Impossible d’accéder au verrou autonome."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Échec de l’ouverture du fichier cible « %s »."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Tentative d’ouvrir le fichier cible « %s », « %s.1 », « %s.2 » … « %s.%d », "
"mais tout a échoué."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Cible du journal non valide."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Audio interne"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Accès refusé"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Commande inconnue"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Paramètre invalide"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "L’entité existe"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Aucune entité de ce type"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Connexion refusée"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Erreur du protocole"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Délai dépassé"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Pas de clé d’authentification"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Erreur interne"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Connexion terminée"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "L’entité a été tuée"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Serveur invalide"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Échec lors de l’initialisation du module"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "État incorrect"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Aucune donnée"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Version du protocole invalide"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Trop grand"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Non pris en charge"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Code d’erreur inconnu"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Aucune extension de ce type"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Fonctionnalité dépréciée"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Implantation manquante"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Le client s’est divisé (Client forked)"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Erreur d’entrée/sortie"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Périphérique ou ressource occupé"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f Gio"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f Mio"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f Kio"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Échec lors du vidage du flux : %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Flux de lecture vidé."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Vidage de la connexion au serveur."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain() : %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "Échec de pa_stream_begin_write() : %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "Échec de pa_stream_peek() : %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Création du flux réussie."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "Échec de pa_stream_get_buffer_attr() : %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Mesures du tampon : maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Mesures du tampon : maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr ""
"Utilisation de la spécification d’échantillon « %s », plan des canaux « %s »."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Connecté au périphérique %s (index : %u, suspendu : %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Erreur du flux : %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Périphérique de flux suspendu.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Périphérique de flux repris.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Flux vide.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Flux saturé.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Flux démarré.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Flux déplacé vers le périphérique %s (%u, %ssuspendu).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "non "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Des attributs du tampon de flux ont changé.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "La pile de requêtes de bouchons est vide : bouchonnage du flux"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "La pile de requêtes de bouchons est vide : débouchonnage du flux"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""
"Avertissement : il a été reçu davantage de requêtes de débouchonnage que de "
"requêtes de bouchonnage."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Connexion établie.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "Échec de pa_stream_new() : %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "Échec de pa_stream_connect_playback() : %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "impossible de définir le flux du moniteur : %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "Échec de pa_stream_connect_record() : %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Échec lors de la connexion : %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF obtenu."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "Échec de pa_stream_write() : %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "Échec de write() : %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Signal obtenu, fermeture."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Échec lors de l’obtention de la latence : %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Durée : %0.3f sec. ; Latence : %0.0f µsec."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "Échec de pa_stream_update_timing_info() : %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [options]\n"
"\n"
"  -h, --help                            Afficher cette aide\n"
"      --version                         Afficher la version\n"
"\n"
"  -r, --record                          Créer une connexion pour "
"l’enregistrement\n"
"  -p, --playback                        Créer une connexion pour la lecture\n"
"\n"
"  -v, --verbose                         Activer les opérations détaillées\n"
"\n"
"  -s, --server=SERVER                   Nom du serveur auquel se connecter\n"
"  -d, --device=DEVICE                   Nom de la destination ou de la "
"source à laquelle se connecter\n"
"  -n, --client-name=NAME                Comment appeler ce client sur le "
"serveur\n"
"      --stream-name=NAME                Comment appeler ce flux sur le "
"serveur\n"
"      --volume=VOLUME                   Indiquer le volume (linéaire) "
"initial dans la gamme 0...65536\n"
"      --rate=SAMPLERATE                 Taux d’échantillonnage en Hz (par "
"défaut 44100)\n"
"      --format=SAMPLEFORMAT             Type d’échantillon, un de s16le, "
"s16be, u8, float32le,\n"
"                                        float32be, ulaw, alaw, s32le, s32be, "
"s24le, s24be,\n"
"                                        s24-32le, s24-32be (par défaut "
"s16ne)\n"
"      --channels=CHANNELS               Nombre de canaux, 1 pour mono, 2 "
"pour stéréo\n"
"                                        (par défaut 2)\n"
"      --channel-map=CHANNELMAP          Plan des canaux à utiliser à la "
"place de la valeur par défaut\n"
"      --fix-format                      Utiliser le format du sample de la "
"destination ou de la source à laquelle le flux\n"
"                                        est connecté.\n"
"      --fix-rate                        Utiliser le taux d’échantillonnage "
"de la destination ou de la source à laquelle le flux\n"
"                                        est connecté.\n"
"      --fix-channels                    Utiliser le nombre de canaux et le "
"plan des canaux\n"
"                                        de la destination ou de la source à "
"laquelle le flux est connecté.\n"
"      --no-remix                        Ne pas mélanger (upmix ou downmix) "
"les canaux.\n"
"      --no-remap                        Cartographier les canaux par index "
"plutôt que par nom.\n"
"      --latency=BYTES                   Demander la latence spécifiée en "
"octets.\n"
"      --process-time=BYTES              Demander le temps de traitement "
"spécifié par requête en octets.\n"
"      --latency-msec=MSEC               Demander la latence spécifiée en "
"msec.\n"
"      --process-time-msec=MSEC          Demander le temps de traitement "
"spécifié par requête en msec.\n"
"      --property=PROPERTY=VALUE         Définir la propriété spécifiée sur "
"la valeur spécifiée.\n"
"      --raw                             Enregistrer ou lire les données PCM "
"brutes.\n"
"      --passthrough                     Données passthrough.\n"
"      --file-format[=FFORMAT]           Enregistrer ou lire les données PCM "
"formatées.\n"
"      --list-file-formats               Répertorier les formats de fichier "
"disponibles.\n"
"      --monitor-stream=INDEX            Enregistrer à partir de l’entrée "
"sink avec l’index INDEX.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Compilé avec libpulse %s\n"
"Lié avec libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Nom du client invalide « %s »"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Nom du flux invalide « %s »"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Plan des canaux invalide « %s »"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Spécification de latence invalide « %s »"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Spécification de temps de traitement invalide « %s »"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Propriété invalide « %s »"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Format de fichier inconnu %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "Impossible d’analyser l’argument pour --monitor-stream"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Spécification d’échantillon invalide"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open() : %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2() : %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Trop de paramètres."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr ""
"Échec lors de la génération des informations de l’échantillon du fichier."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Échec lors de l’ouverture du fichier audio."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Avertissement : les spécifications de l’échantillon spécifié seront écrasées "
"par celles du fichier."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr ""
"Échec lors de l’obtention des informations de l’échantillon du fichier."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr ""
"Avertissement : échec lors de l’obtention des informations du plan des "
"canaux du fichier."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "Le plan des canaux ne correspond pas à la spécification d’échantillon"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr ""
"Avertissement : Échec lors de l’écriture du plan des canaux dans le fichier."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Ouverture d’un flux %s avec une spécification d’échantillon « %s » et un "
"plan des canaux « %s »."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "enregistrement"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "lecture"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Impossible de définir le nom du support."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "Échec de pa_mainloop_new()."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "Échec de io_new()."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "Échec de pa_context_new()."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "Échec de pa_context_connect() : %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "Échec de pa_context_rttime_new()."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "Échec de pa_mainloop_run()."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "NAME [ARGS ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "NAME|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "NAME"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "NAME|#N VOLUME"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N VOLUME"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "NAME|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "NAME|#N KEY=VALUE"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N KEY=VALUE"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "NAME SINK|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "NAME FILENAME"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "PATHNAME"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "FILENAME SINK|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N SINK|SOURCE"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "CARD PROFILE"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "NAME|#N PORT"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "CARD-NAME|CARD-#N PORT OFFSET"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "TARGET"

#: src/utils/pacmd.c:76
#, fuzzy
msgid "NUMERIC-LEVEL"
msgstr "NUMERIC LEVEL"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "FRAMES"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr ""

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Afficher cette aide\n"
"      --version                         Afficher la version\n"
"Lorsqu’aucune commande n’est donnée, pacmd est lancé en mode interactif.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"Compilé avec libpulse %s\n"
"Lié avec libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"Aucun démon PulseAudio en cours d’exécution, ou ne s’exécutant pas dans une "
"session de type démon."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0) : %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect() : %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Impossible de tuer le démon PulseAudio."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Le démon ne répond pas."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write() : %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll() : %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read() : %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Échec lors de l’obtention des statistiques : %s"

#: src/utils/pactl.c:199
#, fuzzy, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "En cours d’utilisation : %u blocs contenant au total %s octets.\n"
msgstr[1] "En cours d’utilisation : %u blocs contenant au total %s octets.\n"

#: src/utils/pactl.c:205
#, fuzzy, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] ""
"Alloué pendant l’ensemble de la durée d’exécution : %u blocs contenant au "
"total %s octets.\n"
msgstr[1] ""
"Alloué pendant l’ensemble de la durée d’exécution : %u blocs contenant au "
"total %s octets.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Taille du cache de l’échantillon : %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Échec lors de l’obtention des informations du serveur : %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, fuzzy, c-format
msgid "%s\n"
msgstr "%s %s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"Chaîne du serveur : %s\n"
"Version du protocole de bibliothèque : %u\n"
"Version du protocole du serveur : %u\n"
"Local : %s\n"
"Index client : %u\n"
"Tile Size : %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Nom d’utilisateur : %s\n"
"Nom d’hôte : %s\n"
"Nom du serveur : %s\n"
"Version du serveur : %s\n"
"Spécification d’échantillon par défaut : %s\n"
"Plan de canaux par défaut : %s\n"
"Destination par défaut : %s\n"
"Source par défaut : %s\n"
"Cookie : %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr ""

#: src/utils/pactl.c:321
msgid "available"
msgstr ""

#: src/utils/pactl.c:322
msgid "not available"
msgstr ""

#: src/utils/pactl.c:331 src/utils/pactl.c:355
#, fuzzy
msgid "Unknown"
msgstr "inconnu(e)"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr ""

#: src/utils/pactl.c:335
#, fuzzy
msgid "Line"
msgstr "Entrée ligne"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr ""

#: src/utils/pactl.c:338
#, fuzzy
msgid "Handset"
msgstr "Casque"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr ""

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr ""

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr ""

#: src/utils/pactl.c:342
msgid "TV"
msgstr ""

#: src/utils/pactl.c:345
msgid "USB"
msgstr ""

#: src/utils/pactl.c:346
#, fuzzy
msgid "Bluetooth"
msgstr "Entrée Bluetooth"

#: src/utils/pactl.c:352
msgid "Network"
msgstr ""

#: src/utils/pactl.c:353
#, fuzzy
msgid "Analog"
msgstr "Mono analogique"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Échec lors de l’obtention des informations sur la destination : %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Destination #%u\n"
"\tÉtat : %s\n"
"\tNom : %s\n"
"\tDescription : %s\n"
"\tPilote : %s\n"
"\tSpécification de l’échantillon : %s\n"
"\tPlan des canaux : %s\n"
"\tModule du propriétaire : %u\n"
"\tSourdine : %s\n"
"\tVolume : %s\n"
"\t        balance %0.2f\n"
"\tVolume de base : %s\n"
"\tSource du moniteur : %s\n"
"\tLatence : %0.0f usec, configuré %0.0f usec\n"
"\tMarqueurs : %s%s%s%s%s%s%s\n"
"\tPropriétés :\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tPorts :\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ""

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tPort actif : %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tFormats :\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Échec lors de l’obtention des informations sur la source : %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Source #%u\n"
"\tÉtat : %s\n"
"\tNom : %s\n"
"\tDescription : %s\n"
"\tPilote : %s\n"
"\tSpécification de l’échantillon : %s\n"
"\tPlan des canaux : %s\n"
"\tModule du propriétaire : %u\n"
"\tSourdine : %s\n"
"\tVolume : %s\n"
"\t        balance %0.2f\n"
"\tVolume de base : %s\n"
"\tMoniteur de la destination : %s\n"
"\tLatence : %0.0f usec, configuré %0.0f usec\n"
"\tMarqueurs : %s%s%s%s%s%s\n"
"\tPropriétés :\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "n/d"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Échec lors de l’obtention des informations du module : %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Module #%u\n"
"\tNom : %s\n"
"\tParamètre : %s\n"
"\tNombre d’utilisations : %s\n"
"\tPropriétés : \n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Échec lors de l’obtention des informations du client : %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Client #%u\n"
"\tPilote : %s\n"
"\tModule propriétaire : %s\n"
"\tPropriétés :\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Impossible d’obtenir des informations sur la carte : %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Carte #%u\n"
"\tNom : %s\n"
"\tPilote : %s\n"
"\tModule propriétaire : %s\n"
"\tPropriétés :\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tProfils :\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tProfil actif : %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tPropriétés :\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tPartie du(des) profil(s) : %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr ""
"Échec lors de l’obtention des informations de l’entrée de la destination : %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Entrée de la destination #%u\n"
"\tPilote : %s\n"
"\tModule du propriétaire : %s\n"
"\tClient : %s\n"
"\tDestination : %u\n"
"\tSpécification de l’échantillon : %s\n"
"\tPlan des canaux : %s\n"
"\tFormat : %s\n"
"\tBouchonné : %s\n"
"\tSourdine : %s\n"
"\tVolume : %s\n"
"\t        balance %0.2f\n"
"\tLatence du tampon : %0.0f usec\n"
"\tLatence de la destination : %0.0f usec\n"
"\tMéthode de rééchantillonnage : %s\n"
"\tPropriétés :\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr ""
"Échec lors de l’obtention des informations de la sortie de la source : %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sortie de la source #%u\n"
"\tPilote : %s\n"
"\tModule du propriétaire : %s\n"
"\tClient : %s\n"
"\tSource : %u\n"
"\tSpécification de l’échantillon : %s\n"
"\tPlan des canaux : %s\n"
"\tFormat : %s\n"
"\tBouchonné : %s\n"
"\tSourdine : %s\n"
"\tVolume : %s\n"
"\t        balance %0.2f\n"
"\tLatence du tampon : %0.0f usec\n"
"\tLatence de la source : %0.0f usec\n"
"\tMéthode de rééchantillonnage : %s\n"
"\tPropriétés :\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Échec lors de l’obtention des informations de l’échantillon : %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Échantillon #%u\n"
"\tNom : %s\n"
"\tSpécification de l’échantillon : %s\n"
"\tPlan des canaux : %s\n"
"\tVolume : %s\n"
"\t        balance %0.2f\n"
"\tDurée : %0.1fs\n"
"\tTaille : %s\n"
"\tLazy : %s\n"
"\tNom du fichier : %s\n"
"\tPropriétés :\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Échec : %s"

#: src/utils/pactl.c:1667
#, fuzzy, c-format
msgid "Send message failed: %s"
msgstr "Échec de read() : %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr ""

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr ""

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Impossible de décharger le module : module %s non chargé"

#: src/utils/pactl.c:1818
#, fuzzy, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"impossible de définir le volume : vous avex tenté de définir les volumes de "
"%d canaux, tandis que channel/s prenait en charge = %d\n"
msgstr[1] ""
"impossible de définir le volume : vous avex tenté de définir les volumes de "
"%d canaux, tandis que channel/s prenait en charge = %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Échec lors de l’envoi de l’échantillon : %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Fin prématurée du fichier"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "nouveau"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "changement"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "supprimer"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "inconnu(e)"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "destination"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "source"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "sink-input"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "source-output"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "module"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "client"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "sample-cache"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "serveur"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "carte"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "Événement « %s » sur %s #%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT reçu, fermeture."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Spécification de volume invalide"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "Le volume est au-delà de la plage admissible.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Nombre de spécifications du volume invalide.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Spécification du volume incohérente.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[options]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[TYPE]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "FILENAME [NAME]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "NAME [SINK]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "NAME|#N VOLUME [VOLUME ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N VOLUME [VOLUME ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "NAME|#N 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N FORMATS"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"Les noms spéciaux @DEFAULT_SINK@, @DEFAULT_SOURCE@ et @DEFAULT_MONITOR@\n"
"peuvent être utilisés pour indiquer la destination, la source, et le "
"moniteur par défaut.\n"

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Afficher cette aide\n"
"      --version                         Afficher la version\n"
"\n"
"  -s, --server=SERVER                   Le nom du serveur auquel se "
"connecter\n"
"  -n, --client-name=NAME                Comment appeler ce client sur le "
"serveur\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Compilé avec libpulse %s\n"
"Lié avec libpulse %s\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "Nom du flux invalide « %s »"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Ne rien indiquer, ou l’un de : %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Veuillez indiquer un fichier d’échantillon à charger"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Échec lors de l’ouverture du fichier audio."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr ""
"Avertissement : Échec lors de l’obtention des spécifications de "
"l’échantillon du fichier."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Vous devez indiquer un nom d’échantillon à lire"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Vous devez indiquer un nom d’échantillon à supprimer"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr ""
"Vous devez indiquer un index d’entrée de destination et une destination"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "Vous devez indiquer un index de sortie de source et une source"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Vous devez indiquer un nom de module et des paramètres."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "Vous devez indiquer un index ou nom de module"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Vous ne pouvez pas indiquer plus d’une destination. Vous devez indiquer une "
"valeur booléenne."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "Spécification de suspension invalide."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Vous ne pouvez pas indiquer plus d’une source. Vous devez indiquer une "
"valeur booléenne."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Vous devez indiquer un nom/un index de carte et un nom de profil"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Vous devez indiquer un nom/un index de destination et un nom de port"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "Vous devez indiquer un nom de destination"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Vous devez indiquer un nom/un index de source et un nom de port"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "Vous devez indiquer un nom de source"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
#, fuzzy
msgid "You have to specify a sink name/index"
msgstr "Vous devez indiquer un nom de destination"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Vous devez indiquer un nom/un index de destination et un volume"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
#, fuzzy
msgid "You have to specify a source name/index"
msgstr "Vous devez indiquer un nom de source"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Vous devez indiquer un nom/un index de source et un volume"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Vous devez indiquer un index d’entrée de destination et un volume"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Index invalide d’entrée de la destination"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "Vous devez indiquer un index de sortie de source et un volume"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Index de sortie de source invalide"

#: src/utils/pactl.c:3086
#, fuzzy
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "Vous devez indiquer un nom/un index de destination et un booléen muet"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Spécification de sourdine invalide"

#: src/utils/pactl.c:3111
#, fuzzy
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "Vous devez indiquer un nom/un index de source et un booléen muet"

#: src/utils/pactl.c:3126
#, fuzzy
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Vous devez indiquer un index d’entrée de destination et un booléen muet"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Spécification d’index d’entrée de la destination invalide"

#: src/utils/pactl.c:3144
#, fuzzy
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "Vous devez indiquer un index de sortie de source et un booléen muet"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Spécification d’index de sortie de source invalide"

#: src/utils/pactl.c:3162
#, fuzzy
msgid "You have to specify at least an object path and a message name"
msgstr "Vous devez indiquer un nom/un index de destination et un nom de port"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"Vous devez indiquer un index de destination et une liste des formats pris en "
"charge séparée par des points-virgules"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr ""
"Vous devez indiquer un nom ou index carte, un nom de port et un décalage de "
"latence"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Impossible d’analyser le décalage de la latence"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Aucune commande valide indiquée."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork() : %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp() : %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Échec lors de la reprise : %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Échec lors de la suspension : %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr ""
"AVERTISSEMENT : le serveur de son n’est pas local, suspension annulée.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Échec lors de la connexion : %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT reçu, fermeture.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "AVERTISSEMENT : le processus fils a été terminé par le signal %u\n"

#: src/utils/pasuspender.c:228
#, fuzzy, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Affiche cette aide\n"
"      --version                         Affiche la version\n"
"  -s, --server=SERVEUR                  Le nom du serveur auquel se "
"connecter\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Compilé avec libpulse %s\n"
"Lié avec libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "Échec de pa_mainloop_new().\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "Échec de pa_context_new().\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "Échec de pa_mainloop_run().\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D visuel] [-S serveur] [-O destination] [-I source] [-c fichier]  [-d|-"
"e|-i|-r]\n"
"\n"
" -d    Affiche les données PulseAudio actuelles attachées au visuel X11 (par "
"défaut)\n"
" -e    Exporte les données PulseAudio locales vers le visuel X11\n"
" -i    Importe les données PulseAudio depuis le visuel X11 vers les "
"variables de l’environnement local et le fichier de cookie.\n"
" -r    Enlève les données PulseAudio du visuel X11\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Échec lors de l’analyse de la ligne de commande.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Serveur : %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Source : %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Destination : %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Cookie : %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Échec lors de l’analyse des données du cookie\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Échec lors de l’enregistrement des données du cookie\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Échec lors de l’obtention du FQDN (« nom de domaine complet »).\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Échec lors du chargement des données du cookie\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Pas encore implémenté.\n"

#~ msgid "Failed to initialize daemon."
#~ msgstr "Échec lors de l’initialisation du démon."

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "Sortie analogique (LFE)"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Relais numérique (S/PDIF)"

#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "Relais numérique (IEC958)"

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Afficher cette aide\n"
#~ "-v, --verbose                         Imprimer les messages de débogage\n"
#~ "      --from-rate=SAMPLERATE          À partir du taux d’échantillonnage "
#~ "en Hz (par défaut 44100)\n"
#~ "      --from-format=SAMPLEFORMAT      À partir du type d’échantillon (par "
#~ "défaut s16le)\n"
#~ "      --from-channels=CHANNELS        À partir du nombre de canaux (par "
#~ "défaut 1)\n"
#~ "      --to-rate=SAMPLERATE            Vers le taux d’échantillonnage en "
#~ "Hz (par défaut 44100)\n"
#~ "      --to-format=SAMPLEFORMAT        Vers le type d’échantillon (par "
#~ "défaut s16le)\n"
#~ "      --to-channels=CHANNELS          Vers le nombre de canaux (par "
#~ "défaut 1)\n"
#~ "      --resample-method=METHOD        Méthode de rééchantillonnage (par "
#~ "défaut auto)\n"
#~ "      --seconds=SECONDS               à partir de la durée du flux (par "
#~ "défaut 60)\n"
#~ "\n"
#~ "Si les formats ne sont pas indiqués, le test effectue toutes les "
#~ "combinaisons de formats,\n"
#~ "les unes après les autres.\n"
#~ "\n"
#~ "Le type d’échantillon doit être l’un de s16le, s16be, u8, float32le, "
#~ "float32be, ulaw, alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (par défaut s16ne)\n"
#~ "\n"
#~ "Veuillez consulter --dump-resample-methods pour voir les valeurs "
#~ "possibles des méthodes de rééchantillonnage.\n"
