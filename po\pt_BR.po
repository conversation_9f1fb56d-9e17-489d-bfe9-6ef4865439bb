# Brazilian Portuguese translation for pulseaudio
# Copyright (C) 2020 <PERSON> <<EMAIL>>
# This file is distributed under the same license as the pulseaudio package.
# <PERSON> <<EMAIL>>, 2008.
# <PERSON> <<EMAIL>>, 2009, 2012.
# <PERSON> <<EMAIL>>, 2013-2020.
#
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2021-08-04 08:04+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) <https://translate.fedoraproject.org/"
"projects/pulseaudio/pulseaudio/pt_BR/>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.7.2\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [opções]\n"
"\n"
"COMANDOS:\n"
"  -h, --help                            Mostra esta ajuda\n"
"      --version                         Mostra a versão\n"
"      --dump-conf                       Descarrega a configuração padrão\n"
"      --dump-modules                    Descarrega a lista de módulos\n"
"                                        disponíveis\n"
"      --dump-resample-methods           Descarrega os métodos de "
"reamostragem\n"
"      --cleanup-shm                     Limpa os segmentos de memória\n"
"                                        compartilhados\n"
"      --start                           Inicia o daemon se ele não estiver "
"em\n"
"                                        execução\n"
"  -k  --kill                            Encerra um daemon em execução\n"
"      --check                           Verifica se há um daemon em "
"execução\n"
"                                        (retorna apenas o código de saída)\n"
"\n"
"OPÇÕES:\n"
"      --system[=BOOL]                   Executa como uma instância do "
"sistema\n"
"                                        em escala ampla\n"
"  -D, --daemonize[=BOOL]                Torna-se um daemon após o início\n"
"      --fail[=BOOL]                     Sai quando a inicialização falhar\n"
"      --high-priority[=BOOL]            Tenta definir um nível alto de nice\n"
"                                        (disponível apenas, quando SUID ou\n"
"                                        com RLIMIT_NICE elevado)\n"
"      --realtime[=BOOL]                 Tenta habilitar o escalonamento em\n"
"                                        tempo real (disponível apenas como\n"
"                                        root quando SUID ou com "
"RLIMIT_RTPRIO\n"
"                                        elevado)\n"
"      --disallow-module-loading[=BOOL]  Não permite carga/descarga de "
"módulo\n"
"                                        exigido pelo usuário depois da "
"partida\n"
"      --disallow-exit[=BOOL]            Não permite saída exigida pelo "
"usuário\n"
"      --exit-idle-time=SEGUNDOS         Termina um daemon quando ocioso e "
"este\n"
"                                        tempo foi decorrido\n"
"      --module-idle-time=SEGUNDOS       Descarrega os módulos "
"autocarregáveis\n"
"                                        quando ociosos e este tempo foi\n"
"                                        decorrido\n"
"      --scache-idle-time=SEGUNDOS       Descarrega amostras quando ociosas "
"e\n"
"                                        este tempo foi decorrido\n"
"      --log-level[=NÍVEL]               Aumenta ou define grau de "
"detalhamento\n"
"  -v  --verbose                         Aumenta o nível de detalhamento\n"
"      --log-target={auto,syslog,stderr,file:CAMINHO,newfile:CAMINHO}\n"
"                                        Especifica o destino do log\n"
"      --log-meta[=BOOL]                 Inclui a localização do código na\n"
"                                        mensagem de log\n"
"      --log-time[=BOOL]                 Inclui carimbos de hora nas "
"mensagens\n"
"                                        de log\n"
"      --log-backtrace=QUADROS           Inclui um backtrace na mensagem de "
"log\n"
"  -p, --dl-search-path=CAMINHO          Define o caminho de pesquisa para\n"
"                                        objetos (plug-ins) dinamicamente\n"
"                                        compartilhados\n"
"      --resample-method=MÉTODO          Usa o método de reamostragem\n"
"                                        especificado (Veja\n"
"                                        --dump-resample-methods para "
"valores\n"
"                                        possíveis)\n"
"      --use-pid-file[=BOOL]             Cria um arquivo PID\n"
"      --no-cpu-limit[=BOOL]             Não instala um limitador de carga "
"de\n"
"                                        CPU em plataformas nas quais haja\n"
"                                        suporte.\n"
"      --disable-shm[=BOOL]              Desabilita o suporte à memória\n"
"                                        compartilhada.\n"
"      --enable-memfd[=BOOL]             Habilita o suporte à memória\n"
"                                        compartilhada memfd\n"
"\n"
"SCRIPT DE INICIALIZAÇÃO:\n"
"  -L, --load=\"ARGUMENTOS DO MÓDULO\"     Carrega um plug-in especificado "
"com\n"
"                                        o argumento especificado\n"
"  -F, --file=NOME_DO_ARQUIVO            Executa o script especificado\n"
"  -C                                    Abre uma linha de comando no TTY em\n"
"                                        execução depois da inicialização\n"
"\n"
"  -n                                    Não carrega o arquivo de script "
"padrão\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize espera argumento booleano"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail espera argumento booleano"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level espera um argumento em nível de log (seja numérico na faixa de "
"0..4 seja algum entre debug, info, notice, warn, error)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority espera um argumento booleano"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime espera um argumento booleano"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading espera um argumento booleano"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit espera um argumento booleano"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file espera argumento booleano"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Alvo de log inválido: use “syslog”, “journal”, “stderr” ou “auto” ou um nome "
"de um arquivo válido “file:<caminho>”, “newfile:<caminho>”."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Alvo de log inválido: use “syslog”, “stderr” ou “auto” ou um nome de arquivo "
"válido “file:<caminho>”, “newfile:<caminho>”."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time espera um argumento booleano"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta espera um argumento booleano"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Método de reamostragem inválido “%s”."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system espera argumento booleano"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit espera argumento booleano"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm espera argumento booleano"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd espera um argumento booleano"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Alvo do log inválido “%s”."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Nível de log inválido “%s”."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Método de reamostragem inválido “%s”."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] rlimit inválido “%s”."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Formato de amostragem inválido “%s”."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Taxa de amostragem inválida “%s”."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Canais de amostragem inválidos “%s”."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Mapa de canais inválido “%s”."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Números de fragmentos inválidos “%s”."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Tamanho de fragmentos inválido “%s”."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Número de nice inválido “%s”."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Tipo de servidor inválido “%s”."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Falha em abrir o arquivo de configuração: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"O mapa padrão dos canais especificado tem um número diferente de canais do "
"que o número de canais padrão especificado."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Lido do arquivo de configuração: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Nome: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Não há informação do módulo disponível\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Versão: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Descrição: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Autor: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Uso: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Carrega uma vez: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "AVISO DE OBSOLESCÊNCIA: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Caminho: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "Falha ao abrir o módulo %s: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Falha ao localizar o carregador original lt_dlopen."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Falha ao alocar o novo carregador dl."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Falha ao adicionar o bind-now-loader."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Falha ao localizar o usuário “%s”."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Falha ao localizar o grupo “%s”."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "O GID do usuário “%s” e do grupo “%s” não combinam."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "O diretório pessoal do usuário “%s” não é “%s”, ignorando."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Falha ao criar “%s”: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Falha ao alterar a lista de grupos: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Falha ao alterar o GID: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Falha ao alterar o UID: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "O modo ampliado do sistema não tem suporte nessa plataforma."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Falha ao analisar a linha de comando."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Modo de sistema recusado para usuário não root. Apenas iniciando o serviço D-"
"Bus de procura de servidores."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Falha ao encerrar o daemon: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Este programa não é para ser executado como root (a não ser que --system "
"seja especificado)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Privilégios de root requeridos."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start não tem suporte para instâncias de sistemas."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr "Servidor configurado por usuário em %s, recusando início/autogeração."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Servidor configurado por usuário em %s, que aparece ser local. Sondando mais "
"fundo."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "Executando no modo sistema, mas --disallow-exit não foi configurado."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"Executando no modo sistema, mas --disallow-module-loading não foi "
"configurado."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "Executando no modo sistema, desabilitando forçadamente o modo SHM."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"Executando no modo sistema, desabilitando forçadamente o exit idle time."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Falha em adquirir o stdio."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() falhou: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() falhou: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() falhou: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Falha na partida do daemon."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() falhou: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Falha ao obter o ID da máquina"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"OK, então você está executando o PA no modo de sistema. Por favor, "
"certifique-se de que você realmente deseja fazer isso.\n"
"Por favor, leia http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ para obter um explicação sobre "
"porque o modo de sistema é uma má ideia."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() falhou."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() falhou."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "argumentos de linha de comando"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"Falha ao inicializar o daemon devido a erros ao executar comandos de "
"inicialização. Fonte dos comandos: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr ""
"O Daemon iniciou sem qualquer módulo carregado, recusando-se a trabalhar."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "Sistema de som PulseAudio"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Inicie o sistema de som PulseAudio"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Entrada"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Entrada da base de encaixe"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Microfone de estação de base de encaixe"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "Entrada de linha de estação de base de encaixe"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "Entrada de linha"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Microfone"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Microfone frontal"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Microfone traseiro"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Microfone externo"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Microfone interno"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Rádio"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Vídeo"

# https://pt.wikipedia.org/wiki/Controle_autom%C3%A1tico_de_ganho
#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Controle automático de ganho"

# https://pt.wikipedia.org/wiki/Controle_autom%C3%A1tico_de_ganho
#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Sem controle automático de ganho"

# Este contexto de Boost é "reforço" no áudio, e não "impulso".
#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Reforço"

# Este contexto de Boost é "reforço" no áudio, e não "impulso".
#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Sem reforço"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Amplificador"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Sem amplificador"

# Este contexto de Boost é "reforço" no áudio, e não "impulso".
#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Reforço de graves"

# Este contexto de Boost é "reforço" no áudio, e não "impulso".
#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Sem reforço de graves"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Auto-falante"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Fones de ouvido"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Entrada analógica"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Microfone de base de encaixe"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Microfone de headset"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Saída analógica"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "Fones de ouvido 2"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "Saída analógica fones de ouvido"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "Saída de linha"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Saída analógica monofônica"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Alto-falantes"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Saída digital (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Entrada digital (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "Entrada multicanal"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "Saída multicanal"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "Saída de jogo"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "Saída de bate-papo"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "Entrada de bate-papo"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "Surround virtual 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Monofônico analógico"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "Monofônico analógico (Esquerda)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "Monofônico analógico (Direita)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Estéreo analógico"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Mono"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Estéreo"

# Fone de ouvido não se encaixa como tradução aqui, pois há ou pode haver microfone junto.
#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Headset"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "Viva voz"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Multicanal"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Surround analógico 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Surround analógico 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Surround analógico 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Surround analógico 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Surround analógico 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Surround analógico 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Surround analógico 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Surround analógico 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Surround analógico 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Surround analógico 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Surround analógico 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Estéreo digital (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Surround digital 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Surround digital 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Surround digital 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Estéreo digital (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Surround digital 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "Bate-papo"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "Jogo"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Duplex monofônico analógico"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Duplex estéreo analógico"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Duplex estéreo digital (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "Duplex multicanal"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "Duplex estéreo"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "Bate-papo monofônico + surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Desligado"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "Saída %s"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "Entrada %s"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"O ALSA nos acordou para gravar novos dados no dispositivo, mas não há nada a "
"ser gravado.\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema para os desenvolvedores do ALSA.\n"
"Nós fomos acordados com o conjunto POLLOUT -- entretanto, a snd_pcm_avail() "
"subsequente retornou 0 ou outro valor < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"O ALSA nos acordou para ler novos dados no dispositivo, mas não há nada a "
"ser lido.\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema para os desenvolvedores do ALSA.\n"
"Nós fomos acordados com o conjunto POLLIN -- entretanto, a snd_pcm_avail() "
"subsequente retornou 0 ou outro valor < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() retornou um valor que é excepcionalmente grande: %lu byte "
"(%lu ms).\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema aos desenvolvedores do ALSA."
msgstr[1] ""
"snd_pcm_avail() retornou um valor que é excepcionalmente grande: %lu bytes "
"(%lu ms).\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema aos desenvolvedores do ALSA."

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() retornou um valor que é excepcionalmente grande: %li byte (%s"
"%lu ms).\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema aos desenvolvedores do ALSA."
msgstr[1] ""
"snd_pcm_delay() retornou um valor que é excepcionalmente grande: %li bytes "
"(%s%lu ms).\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema aos desenvolvedores do ALSA."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail() retornou um valor estranho: o atraso de %lu é menor do que "
"(%lu ms).\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema aos desenvolvedores do ALSA."

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() retornou um valor que é excepcionalmente grande: %lu "
"byte (%lu ms).\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema aos desenvolvedores do ALSA."
msgstr[1] ""
"snd_pcm_mmap_begin() retornou um valor que é excepcionalmente grande: %lu "
"bytes (%lu ms).\n"
"É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
"relate esse problema aos desenvolvedores do ALSA."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Entrada Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Saída Bluetooth"

# Desconheço tradução comum para esta palavra.
#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Handsfree"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Fones de ouvido"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Portátil"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Carro"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "HiFi"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Telefone"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "Reprodução de alta fidelidade (Destino A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "Captura de alta fidelidade (Fonte A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "Unidade de headset (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "Gateway de áudio de headset (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "Unidade de handsfree (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Gateway de áudio do handsfree (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<nome da fonte> source_properties=<propriedades da fonte> "
"source_master=<nome de fonte para filtrar> sink_name=<nome do destino> "
"sink_properties=<propriedades do destino> sink_master=<nome do destino para "
"filtrara> adjust_time=<com qual frequência deve-se reajustar taxas em "
"segundos> adjust_threshold=<quanta diferença até reajustar, em "
"milissegundos> format=<formato da amostragem> rate=<taxa da amostragem> "
"channels=<número de canais> channel_map=<mapa do canal> "
"aec_method=<implementar para usar> aec_args=<parâmetros do mecanismo AEC> "
"save_aec=<salvar dados AEC em /tmp> autoloaded=<define se este módulo está "
"sendo carregado automaticamente> use_volume_sharing=<yes ou no> "
"use_master_format=<yes ou no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "Ligado"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Saída fictícia"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "Sempre manter pelo menos um destino carregado mesmo se for nulo"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "Sempre manter pelo menos uma fonte carregado mesmo se for nula"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "Equalizador de propósito geral"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<nome do destino> sink_properties=<propriedades do destino> "
"master=<nome do destino a ser filtrado> format=<formato de amostragem> "
"rate=<taxa da amostragem> channels=<número de canais> channel_map=<mapa dos "
"canais> autoloaded=<define se este módulo está sendo carregado "
"automaticamente> use_volume_sharing=<yes ou no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "Equalizador baseado em FFT em %s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<descarregar automaticamente filtros não usados?>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Destino Virtual LADSPA"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<nome do destino> sink_properties=<propriedades do destino> "
"sink_input_properties=<propriedades para entrada de destino> master=<name of "
"sink to filter> sink_master=<nome do destino a ser filtrado> format=<formato "
"de amostragem> rate=<taxa da amostragem> channels=<número de canais> "
"channel_map=<mapa dos canais de entrada> plugin=<nome do plugin ladspa> "
"label=<rótulo do plug-in ladspa> control=<lista separada por vírgulas dos "
"valores de controle da entrada> input_ladspaport_map=<lista separada por "
"vírgulas de nomes de porta de entrada LADSPA> output_ladspaport_map=<lista "
"separada por vírgulas de nomes de porta de saída LADSPA> autoloaded=<define "
"se esse módulo está sendo carregado automaticamente> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Destino nulo temporizado"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Saída nula"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Falha ao definir formato: string %s de formato inválida"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Dispositivos de saída"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Dispositivos de entrada"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "Áudio em @HOSTNAME@"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "Túnel para %s@%s"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "Túnel para %s/%s"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Destino surround virtual"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<nome do destino> sink_properties=<propriedades do destino> "
"master=<nome do destino a ser filtrado> sink_master=<nome do destino para "
"filtrar> format=<formato de amostragem> rate=<taxa da amostragem> "
"channels=<número de canais> channel_map=<mapa dos canais> "
"use_volume_sharing=<yes ou no> force_flat_volume=<yes ou no> hrir=/caminho/"
"para/hrir_esquerdo.wav hrir_left=/caminho/para/hrir_esquerdo.wav hrir_right=/"
"caminho/para/opcional/hrir_direito.wav autoloaded=<define se esse módulo "
"está sendo carregado automaticamente> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "Modelo desconhecido de dispositivo"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "Perfil padrão RAOP"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "Servidor de som PulseAudio"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Frontal central"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Frontal esquerda"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Frontal direita"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Traseira central"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Traseira esquerda"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Traseira direita"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Subwoofer"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Frontal esquerdo do centro"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Frontal direito do centro"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Lateral esquerdo"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Lateral direito"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Auxiliar 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Auxiliar 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Auxiliar 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Auxiliar 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Auxiliar 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Auxiliar 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Auxiliar 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Auxiliar 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Auxiliar 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Auxiliar 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Auxiliar 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Auxiliar 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Auxiliar 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Auxiliar13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Auxiliar 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Auxiliar 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Auxiliar 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Auxiliar 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Auxiliar 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Auxiliar 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Auxiliar 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Auxiliar 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Auxiliar 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Auxiliar 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Auxiliar 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Auxiliar 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Auxiliar 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Auxiliar 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Auxiliar 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Auxiliar 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Auxiliar 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Auxiliar 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Superior central"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Frontal superior central"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Frontal superior esquerda"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Frontal superior direita"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Traseira superior central"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Traseira superior esquerda"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Traseira superior direita"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(inválido)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Surround 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Surround 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Surround 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Surround 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Surround 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() falhou"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() retornou verdadeiro"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Falha ao analisar os dados do cookie"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Foi recebida uma mensagem para uma extensão desconhecida “%s”"

#: src/pulse/direction.c:37
msgid "input"
msgstr "entrada"

#: src/pulse/direction.c:39
msgid "output"
msgstr "saída"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "bidirecional"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "inválido"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) não é propriedade nossa (uid %d), e sim do uid %d! "
"(Isso poderia acontecer, por exemplo, se você tentar conectar a um "
"PulseAudio não-root como um usuário root, por meio do protocolo nativo. Não "
"faça isso.)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "sim"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "não"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Não foi possível acessar a trava de autogeração."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Falha ao abrir o arquivo alvo “%s”."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Tentado abrir arquivo alvo “%s”, “%s.1”, “%s.2” ... “%s.%d”, mas tudo falhou."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Alvo do log inválido."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Áudio interno"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Acesso negado"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Comando desconhecido"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Argumento inválido"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "Entidade existente"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Não existe tal entidade"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Conexão recusada"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Erro de protocolo"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Tempo esgotado"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Nenhuma chave de autenticação"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Erro interno"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Conexão terminada"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Entidade terminada"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Servidor inválido"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "A inicialização do módulo falhou"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Mau estado"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Não há dados"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Versão de protocolo incompatível"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Muito grande"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Não há suporte"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Código de erro desconhecido"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Não existe tal extensão"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Funcionalidade obsoleta"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Implementação faltando"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Cliente bifurcado"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Erro de entrada/saída"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Dispositivo ou recurso ocupado"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Falha ao drenar o fluxo: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Fluxo de reprodução drenado."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Drenando conexão para o servidor."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() falhou: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() falhou: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Fluxo criado com sucesso."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() falhou: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Métricas do buffer: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Métricas do buffer: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "Usando especificação de amostragem “%s”, mapa de canais “%s”."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Conectado ao dispositivo %s (índice: %u, suspenso: %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Erro de fluxo: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Dispositivo de fluxo suspenso.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "O dispositivo de fluxo continuou.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Subestimação do fluxo.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Superestimação do fluxo.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Fluxo iniciado.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Fluxo movido para o dispositivo %s (%u, %ssuspended).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "não "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Atributos do buffer de fluxo alterados.%s"

# https://en.wikipedia.org/wiki/Cork_encoding
#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Pilha de requisição cork está vazia: aplicando cork no fluxo"

# https://en.wikipedia.org/wiki/Cork_encoding
#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Pilha de requisição cork está vazia: desfazando cork no fluxo"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""
"Aviso: Recebidas mais requisições para desfazer cork do que requisições "
"aplicá-la."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Conexão estabelecida.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() falhou: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() falhou: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Falha ao definir o fluxo de monitoração: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() falhou: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Falha na conexão: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "Atingiu EOF."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() falhou: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() falhou: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Sinal recebido, saindo."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Falha ao obter a latência: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Tempo: %0.3f seg; Latência: %0.0f useg."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() falhou: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [opções]\n"
"%s\n"
"\n"
"  -h, --help                            Mostra essa ajuda\n"
"      --version                         Mostra a versão\n"
"\n"
"  -r, --record                          Cria uma conexão para gravação\n"
"  -p, --playback                        Cria uma conexão para reprodução\n"
"\n"
"  -v, --verbose                         Habilita operações no modo "
"detalhado\n"
"\n"
"  -s, --server=SERVIDOR                 O nome do servidor a conectar-se\n"
"  -d, --device=DISPOSITIVO              O nome do destino/fonte a conectar-"
"se\n"
"  -n, --client-name=NOME                Como chamar este cliente no "
"servidor\n"
"      --stream-name=NOME                Como chamar este fluxo no servidor\n"
"      --volume=VOLUME                   Especifica a faixa (linear) inicial\n"
"                                        de volume no intervalo 0...65536\n"
"      --rate=TAXA_DE_AMOSTRAGEM         Taxa de amostragem, Hz (padrão "
"44100)\n"
"      --format=FORMATO_DE_AMOSTRAGEM    Tipo de amostragem, veja\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        para valores possíveis (padrão: "
"s16ne)\n"
"      --channels=CANAIS                 O número de canais, 1 para mono,\n"
"                                        2 para estéreo (padrão: 2)\n"
"      --channel-map=MAPA_DE_CANAIS      Mapeamento de canais a ser usado no\n"
"                                        lugar do padrão\n"
"      --fix-format                      Obtém o formato da amostragem do\n"
"                                        destino/fonte onde o fluxo está\n"
"                                        sendo conectado.\n"
"      --fix-rate                        Obtém a taxa de amostragem do\n"
"                                        destino/fonte onde o fluxo está\n"
"                                        sendo conectado.\n"
"      --fix-channels                    Obtém o número de canais e o mapa "
"de\n"
"                                        canais do destino onde o fluxo está\n"
"                                        sendo conectado.\n"
"      --no-remix                        Não faz upmix nem downmix dos "
"canais.\n"
"      --no-remap                        Mapeia os canais por índice em vez\n"
"                                        de nome\n"
"      --latency=BYTES                   Requisita a latência especificada "
"em\n"
"                                        bytes.\n"
"      --process-time=BYTES              Requisita o tempo de processo\n"
"                                        especificado por requisições em "
"bytes.\n"
"      --latency-msec=MSEGUNDOS          Requisita a latência especificada "
"em\n"
"                                        milissegundos.\n"
"      --process-time-msec=MSEGUNDOS     Requisita a o tempo do processo por\n"
"                                        requisição em milissegundos.\n"
"      --property=PROPRIEDADE=VALOR      Define a propriedade especificada "
"para\n"
"                                        o valor especificado.\n"
"      --raw                             Grava/reproduz dados PCM não "
"tratados.\n"
"      --passthrough                     Dados para conversão.\n"
"      --file-format[=FORMATO_ARQUIVO]   Grava/reproduz dados PCM "
"formatados.\n"
"      --list-file-formats               Lista formatos de arquivo "
"disponíveis.\n"
"      --monitor-stream=ÍNDICE           Grava da entrada do destino com "
"índice.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""
"Reproduz arquivos de áudio codificados em um servidor de som PulseAudio."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""
"Captura dados de áudio de um servidor de som PulseAudio e escreve-os para um "
"arquivo."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"Captura dados de áudio de um servidor de som PulseAudio e escreve-os para "
"STDOUT (saída padrão) ou o arquivo especificado."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"Reproduz dados de áudio de STDIN (entrada padrão) ou o arquivo especificado "
"em um servidor de áudio PulseAudio."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Compilado com libpulse %s\n"
"Vinculado com libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Nome do cliente “%s” inválido"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Nome do fluxo “%s” inválido"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Mapa de canais “%s” inválido"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Especificação de latência inválida “%s”"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Especificação do tempo de processo “%s” inválida"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Propriedade “%s” inválida"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Formato de arquivo %s desconhecido."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "Falha ao analisar o argumento de --monitor-stream"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Especificação de amostragem inválida"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Argumentos em excesso."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Falha ao gerar a especificação de amostragem para o arquivo."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Falha ao abrir o arquivo de áudio."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Aviso: a especificação de amostragem especificada será sobrescrita pela "
"especificação do arquivo."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Falha ao determinar a especificação de amostragem a partir do arquivo."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "Aviso: Falha ao determinar o mapa de canais a partir do arquivo."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "O mapa de canais não combina com a especificação da amostragem"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Aviso: falha ao gravar o mapa de canais no arquivo."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Abrindo um fluxo %s com a especificação de amostragem “%s” e mapa de canais "
"“%s”."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "gravando"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "reprodução"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Falha ao definir o nome da mídia."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() falhou."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() falhou."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() falhou."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_new() falhou: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() falhou."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() falhou."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "NOME [ARGS ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "NOME|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "NOME"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "NOME|#N VOLUME"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N VOLUME"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "NOME|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "NOME|#N CHAVE=VALOR"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N CHAVE=VALOR"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "NOME DESTINO|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "NOME NOME_DE_ARQUIVO"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "NOME_DE_CAMINHO"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "NOME_DE_ARQUIVO DESTINO|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N DESTINO|FONTE"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "PLACA PERFIL"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "NOME|#N PORTA"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "NOME-PLACA|PLACA-#N PORTA POSIÇÃO"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "ALVO"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "NÍVEL-NUMÉRICO"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "QUADROS"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "DESTINATÁRIO MENSAGEM [PARÂMETROS_DA_MENSAGEM]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Mostra esta ajuda\n"
"      --version                         Mostra a versão\n"
"Quando nenhum comando é informado, pacmd inicia em modo interativo.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Compilado com libpulse %s\n"
"Vinculado com libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"Nenhum daemon do PulseAudio em execução ou não está em execução como daemon "
"de sessão."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Falha ao matar o daemon do PulseAudio."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "O daemon não responde."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Falha ao obter estatísticas: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Em uso no momento: %u bloco contendo %s bytes no total.\n"
msgstr[1] "Em uso no momento: %u blocos contendo %s bytes no total.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "Alocado por todo o tempo: %u bloco contendo %s bytes no total.\n"
msgstr[1] "Alocado por todo o tempo: %u blocos contendo %s bytes no total.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Tamanho do cache para amostragem: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Falha ao obter informações do servidor: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr "%s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"String do servidor: %s\n"
"Versão do protocolo da biblioteca: %u\n"
"Versão do protocolo do servidor: %u\n"
"É local: %s\n"
"Índice do cliente: %u\n"
"Tamanho de fragmento: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Nome do usuário: %s\n"
"Nome da máquina: %s\n"
"Nome do servidor: %s\n"
"Versão do servidor: %s\n"
"Especificação padrão de amostragem: %s\n"
"Mapa de canais padrão: %s\n"
"Destino padrão: %s\n"
"Fonte padrão: %s\n"
"Cookie: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "disponibilidade desconhecida"

#: src/utils/pactl.c:321
msgid "available"
msgstr "disponível"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "não disponível"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "Desconhecido"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Aux"

#: src/utils/pactl.c:335
msgid "Line"
msgstr "Linha"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Mic"

#: src/utils/pactl.c:338
msgid "Handset"
msgstr "Monofone"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "Fone de ouvido"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "SPDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "TV"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "Bluetooth"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "Rede"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "Analógico"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Falha ao obter informações do destino: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Destino #%u\n"
"\tEstado: %s\n"
"\tNome: %s\n"
"\tDescrição: %s\n"
"\tDriver: %s\n"
"\tEspecificação da amostragem: %s\n"
"\tMapa dos canais: %s\n"
"\tMódulo proprietário: %u\n"
"\tMudo: %s\n"
"\tVolume: %s\n"
"\t        balanço %0.2f\n"
"\tVolume base: %s\n"
"\tFonte de monitoração: %s\n"
"\tLatência: %0.0f useg, %0.0f useg configurado\n"
"\tSinalizadores: %s%s%s%s%s%s%s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tPortas:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (tipo: %s, prioridade: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ", grupo de disponibilidade: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tPorta ativa: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tFormatos:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Falha ao obter informações da fonte: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Fonte #%u\n"
"\tEstado: %s\n"
"\tNome: %s\n"
"\tDescrição: %s\n"
"\tDriver: %s\n"
"\tEspecificação da amostragem: %s\n"
"\tMapa dos canais: %s\n"
"\tMódulo proprietário: %u\n"
"\tMudo: %s\n"
"\tVolume: %s\n"
"\t        balanço %0.2f\n"
"\tVolume base: %s\n"
"\tMonitor do destino: %s\n"
"\tLatência: %0.0f useg, %0.0f useg configurado\n"
"\tSinalizadores: %s%s%s%s%s%s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "n/d"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Falha ao obter informações do módulo: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Módulo #%u\n"
"\tNome: %s\n"
"\tArgumento: %s\n"
"\tContador de uso: %s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Falha ao obter informações do cliente: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Cliente #%u\n"
"\tDriver: %s\n"
"\tMódulo proprietário: %s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Falha ao obter informações da placa: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Placa #%u\n"
"\tNome: %s\n"
"\tDriver: %s\n"
"\tMódulo proprietário: %s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tPerfis:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr "\t\t%s: %s (destino: %u, fontes: %u, prioridade: %u, disponível: %s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tPerfil ativo: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (tipo: %s, prioridade: %u, mudança da latência: %<PRId64> usec%s"
"%s, %s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tPropriedades:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tParte de perfil/perfis: %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Falha ao obter informações da entrada do destino: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Entrada do destino #%u\n"
"\tDriver: %s\n"
"\tMódulo proprietário: %s\n"
"\tCliente: %s\n"
"\tDestino: %u\n"
"\tEspecificação da amostragem: %s\n"
"\tMapa dos canais: %s\n"
"\tFormato: %s\n"
"\tCork: %s\n"
"\tMudo: %s\n"
"\tVolume: %s\n"
"\t        balanço %0.2f\n"
"\tLatência do buffer: %0.0f useg\n"
"\tLatência do destino: %0.0f useg\n"
"\tMétodo de reamostragem: %s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "Falha ao obter informações da saída da fonte: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Saída da fonte #%u\n"
"\tDriver: %s\n"
"\tMódulo proprietário: %s\n"
"\tCliente: %s\n"
"\tFonte: %u\n"
"\tEspecificação da amostragem: %s\n"
"\tMapa dos canais: %s\n"
"\tFormato: %s\n"
"\tCork: %s\n"
"\tMudo: %s\n"
"\tVolume: %s\n"
"\t        balanço %0.2f\n"
"\tLatência do buffer: %0.0f useg\n"
"\tLatência da fonte: %0.0f useg\n"
"\tMétodo de reamostragem: %s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Falha ao obter informações sobre a amostragem: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Amostra #%u\n"
"\tNome: %s\n"
"\tEspecificação da amostragem: %s\n"
"\tMapa dos canais: %s\n"
"\tVolume: %s\n"
"\t        balanço %0.2f\n"
"\tDuração: %0.1fs\n"
"\tTamanho: %s\n"
"\tLazy: %s\n"
"\tNome do arquivo: %s\n"
"\tPropriedades:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Falha: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "Envio de mensagem falhou: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr "mensagem list-handlers falhou: %s"

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "a resposta da mensagem list-handlers não pôde ser tratada corretamente"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "a resposta da mensagem list-handlers não é um array JSON"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""
"a resposta da mensagem list-handlers contém elemento de array %d que não é "
"um objeto JSON"

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Falha ao descarregar o módulo: módulo %s não carregado"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"Falha ao definir volume: Você tentou definir volumes para %d canal, havendo "
"suporte ao(s) canal(is) = %d\n"
msgstr[1] ""
"Falha ao definir volume: Você tentou definir volumes para %d canais, havendo "
"suporte ao(s) canal(is) = %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Falha ao enviar a amostragem: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Fim prematuro do arquivo"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "novo"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "alterar"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "remover"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "desconhecido"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "destino"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "fonte"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "entrada-destino"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "saída-fonte"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "módulo"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "cliente"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "cache-amostragem"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "servidor"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "placa"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "Evento “%s” em %s #%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT recebido, saindo."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Especificação de volume inválida"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "Volume fora da faixa admissível.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Número de especificações de volume inválido.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Especificação de volume inconsistente.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[opções]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[TIPO]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "NOME_DE_ARQUIVO [NOME]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "NOME [DESTINO]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "NOME|#N VOLUME [VOLUME ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N VOLUME [VOLUME ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "NOME|#N 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N FORMATOS"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"Os nomes especiais @DEFAULT_SINK@, @DEFAULT_SOURCE@ e @DEFAULT_MONITOR@\n"
"podem ser usados para especificar o destino, a fonte e a monitoração "
"padrão.\n"

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Mostra esta ajuda\n"
"      --version                         Mostra a versão\n"
"\n"
"  -s, --server=SERVIDOR                 Nome do servidor a ser conectado\n"
"  -n, --client-name=NOME                Como chamar este cliente no "
"servidor\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Compilado com libpulse %s\n"
"Vinculado com libpulse %s\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "Nome do fluxo “%s” inválido"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Especifique nada ou uma de: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Por favor, especifique um arquivo de amostragem a ser carregado"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Falha ao abrir o arquivo de som."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr ""
"Aviso: Falha ao determinar a especificação da amostragem a partir do arquivo."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Você deve especificar um nome para amostra a ser reproduzida"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Você deve especificar um nome para a amostra a ser removida"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "Você deve especificar a entrada do destino e um destino"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "Você deve especificar um índice de saída da fonte e uma fonte"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Você deve especificar um nome para o módulo e seus argumentos."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "Você deve especificar um nome ou índice do módulo"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Você não pode especificar mais de um destino. Você deve especificar um valor "
"booleano."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "Especificação de suspensão inválida."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Você não pode especificar mais de uma fonte. Você deve especificar um valor "
"booleano."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Você deve especificar um nome/índice para a placa e um nome de perfil"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Você deve especificar um nome/índice do destino e o nome da porta"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "Você deve especificar um nome de destino"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Você deve especificar um nome/índice da fonte e o nome da porta"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "Você deve especificar um nome de fonte"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "Você deve especificar um nome/índice de destino"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Você deve especificar um nome/índice do destino e um volume"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "Você deve especificar um nome/índice de fonte"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Você deve especificar um nome/índice da fonte e um volume"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Você deve especificar um índice de entrada para o destino e um volume"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Índice de entrada de destino inválido"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "Você deve especificar um índice de saída da fonte e um volume"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Índice de saída de fonte inválido"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Você deve especificar um nome/índice do destino e uma ação de mudo (0, 1 ou "
"“toogle”)"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Especificação de mudo inválida"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Você deve especificar um nome/índice da fonte e uma ação de mudo (0, 1 ou "
"“toogle”)"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"Você deve especificar um índice de entrada do destino e uma ação de mudo (0, "
"1 ou “toogle”)"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Especificação do índice de entrada de destino inválida"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr ""
"Você deve especificar um índice de saída de fonte e uma ação de mudo (0, 1 "
"ou “toogle”)"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Especificação do índice de saída de fonte inválida"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr ""
"Você deve especificar pelo menos um caminho de objeto e um nome de mensagem"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"Excesso de argumentos fornecidos, eles serão ignorados. Observe que todos os "
"parâmetros da mensagem devem ser fornecidos como uma única string."

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"Você deve especificar um índice do destino e uma lista separada por ponto-e-"
"vírgulas de formatos aceitos"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr ""
"Você deve especificar nome/índice de uma placa, um nome de porta e uma "
"mudança de latência"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Não foi possível analisar a mudança da latência"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Nenhum comando válido especificado."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Falha ao prosseguir: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Falha em suspender: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "AVISO: O servidor de som não é local, e não será suspenso.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Falha na conexão: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "Recebido o SIGINT, saindo.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "AVISO: O processo filho terminou pelo sinal %u\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [opções] -- PROGRAMA [ARGUMENTOS ... ]\n"
"\n"
"Suspende temporariamente o PulseAudio enquanto o PROGRAMA é executado\n"
"\n"
"  -h, --help                            Mostra esta ajuda\n"
"      --version                         Mostra a versão\n"
"  -s, --server=SERVIDOR                 Nome do servidor a ser conectado\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Compilado com libpulse %s\n"
"Vinculado com libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() falhou.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() falhou.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() falhou.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S servidor] [-O destino] [-I fonte] [-c arq.] [-d|-e|-i|-"
"r]\n"
"\n"
" -d    Mostra dados atuais do PulseAudio associados ao display X11 (padrão)\n"
" -e    Exporta dados locais do PulseAudio para um display X11\n"
" -i    Importa dados do PulseAudio de um display X11 para as variáveis de\n"
"       ambiente locais e para o arquivo de cookie.\n"
" -r    Remove os dados do PulseAudio do display X11\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Falha em interpretar a linha de comando.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Servidor: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Fonte: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Destino: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Cookie: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Falha ao analisar os dados do cookie\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Falha ao salvar os dados do cookie\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Falha ao obter FQDN.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Falha ao carregar os dados do cookie\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Não implementado ainda.\n"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Conversor digital (S/PDIF)"

#~ msgid "Digital Passthrough (IEC958)"
#~ msgstr "Conversor digital (IEC958)"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "Saída monofônica separada em LFE"

#~ msgid "Failed to initialize daemon."
#~ msgstr "Falha em iniciar o daemon."

#~ msgid ""
#~ "ALSA woke us up to write new data to the device, but there was actually "
#~ "nothing to write!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "O ALSA nos acordou para gravar novos dados no dispositivo, mas não há "
#~ "nada a ser gravado!\n"
#~ "É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
#~ "relate esse problema para os desenvolvedores do ALSA.\n"
#~ "Nós fomos acordados com o conjunto POLLOUT -- entretanto, a "
#~ "snd_pcm_avail() subsequente retornou 0 ou outro valor < min_avail."

#~ msgid ""
#~ "ALSA woke us up to read new data from the device, but there was actually "
#~ "nothing to read!\n"
#~ "Most likely this is a bug in the ALSA driver '%s'. Please report this "
#~ "issue to the ALSA developers.\n"
#~ "We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
#~ "returned 0 or another value < min_avail."
#~ msgstr ""
#~ "O ALSA nos acordou para ler novos dados no dispositivo, mas não há nada a "
#~ "ser lido!\n"
#~ "É mais provável que isso seja um erro no driver “%s” do ALSA. Por favor, "
#~ "relate esse problema para os desenvolvedores do ALSA.\n"
#~ "Nós fomos acordados com o conjunto POLLIN -- entretanto, a "
#~ "snd_pcm_avail() subsequente retornou 0 ou outro valor < min_avail."

#~ msgid "Cleaning up privileges."
#~ msgstr "Limpando privilégios."

#~ msgid "Got signal %s."
#~ msgstr "Sinal %s recebido."

#~ msgid "Exiting."
#~ msgstr "Saindo."

#~ msgid "Found user '%s' (UID %lu) and group '%s' (GID %lu)."
#~ msgstr "Usuário \"%s\" (UID %lu) e grupo \"%s\" (GID %lu) localizados."

#~ msgid "Successfully changed user to \""
#~ msgstr "Usuário alterado com sucesso para \""

#~ msgid "setrlimit(%s, (%u, %u)) failed: %s"
#~ msgstr "setrlimit(%s, (%u, %u)) falhou: %s"

#~ msgid "Daemon not running"
#~ msgstr "O daemon não está em execução"

#~ msgid "Daemon running as PID %u"
#~ msgstr "Daemon executando como PID %u"

#~ msgid "Daemon startup successful."
#~ msgstr "Os daemons foram iniciados com sucesso."

#~ msgid "This is PulseAudio %s"
#~ msgstr "Este é o PulseAudio %s"

#~ msgid "Compilation host: %s"
#~ msgstr "Máquina da compilação: %s"

#~ msgid "Compilation CFLAGS: %s"
#~ msgstr "CFLAGS da compilação: %s"

#~ msgid "Running on host: %s"
#~ msgstr "Executando no host: %s"

#~ msgid "Found %u CPUs."
#~ msgstr "%u CPUs localizadas."

#~ msgid "Page size is %lu bytes"
#~ msgstr "O tamanho da página é %lu bytes"

#~ msgid "Compiled with Valgrind support: yes"
#~ msgstr "Compilado com suporte do Valgrind: sim"

#~ msgid "Compiled with Valgrind support: no"
#~ msgstr "Compilado com suporte do Valgrind: não"

#~ msgid "Running in valgrind mode: %s"
#~ msgstr "Executando em modo valgrind: %s"

#~ msgid "Running in VM: %s"
#~ msgstr "Executando na VM: %s"

#~ msgid "Optimized build: yes"
#~ msgstr "Build otimizado: sim"

#~ msgid "Optimized build: no"
#~ msgstr "Build otimizado: não"

#~ msgid "NDEBUG defined, all asserts disabled."
#~ msgstr "NDEBUG definido, todas as declarações desabilitadas."

#~ msgid "FASTPATH defined, only fast path asserts disabled."
#~ msgstr ""
#~ "FASTPATH definido, somente as declarações do \"fast path\" foram "
#~ "desabilitadas."

#~ msgid "All asserts enabled."
#~ msgstr "Todas as declarações habilitadas."

#~ msgid "Machine ID is %s."
#~ msgstr "O ID da máquina é %s."

#~ msgid "Session ID is %s."
#~ msgstr "O ID da sessão é %s."

#~ msgid "Using runtime directory %s."
#~ msgstr "Usando o diretório de runtime %s."

#~ msgid "Using state directory %s."
#~ msgstr "Usando o diretório de estado %s."

#~ msgid "Using modules directory %s."
#~ msgstr "Usando o diretório de módulos %s."

#~ msgid "Running in system mode: %s"
#~ msgstr "Executando em modo do sistema: %s"

#~ msgid "Fresh high-resolution timers available! Bon appetit!"
#~ msgstr "Timers de alta resolução fresquinhos disponíveis! Bon appetit!"

#~ msgid ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"
#~ msgstr ""
#~ "Cara, seu kernel fede! A recomendação do chef hoje é Linux com timers de "
#~ "alta resolução habilitados!"

#~ msgid "Daemon startup complete."
#~ msgstr "A inicialização do daemon foi concluída."

#~ msgid "Daemon shutdown initiated."
#~ msgstr "O encerramento do daemon foi iniciado."

#~ msgid "Daemon terminated."
#~ msgstr "Daemon terminado."

#~ msgid "No cookie loaded. Attempting to connect without."
#~ msgstr "Nenhum cookie foi carregado. Tentativa de conexão sem eles."

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [opções]\n"
#~ "\n"
#~ "-h, --help                            Mostra essa ajuda\n"
#~ "-v, --verbose                         Mostra mensagens de depuração\n"
#~ "      --from-rate=TAXA_DE_AMOSTRAGEM  De taxa de amostragem em Hz\n"
#~ "                                      (padrão 44100)\n"
#~ "      --from-format=FORMATO_DE_AMOSTRAGEM\n"
#~ "                                      De tipo de amostragem (padrão "
#~ "s16le)\n"
#~ "      --from-channels=CANAIS          De Número de canais (padrão 1)\n"
#~ "      --to-rate=TAXA_DE_AMOSTRAGEM      Para taxa de amostragem em Hz\n"
#~ "                                      (padrão 44100)\n"
#~ "      --to-format=FORMATO_DE_AMOSTRAGEM\n"
#~ "                                      Para tipo de amostragem (padrão "
#~ "s16le)\n"
#~ "      --to-channels=CANAIS            Para número de canais (padrão 1)\n"
#~ "      --resample-method=MÉTODO        Método de reamostragem (padrão "
#~ "auto)\n"
#~ "      --seconds=SEGUNDOS              De duração de fluxo (padrão 60)\n"
#~ "\n"
#~ "Se os formatos não forem especificados, o teste realiza todas as "
#~ "combinações\n"
#~ "de formatos, para trás e para frente.\n"
#~ "\n"
#~ "O tipo de amostragem deve ser um entre s16le, s16be, u8, float32le,\n"
#~ "float32be, ulaw, alaw, s24le, s24be, s24-32le, s24-32be, s32le e s32be\n"
#~ "(padrão s16ne)\n"
#~ "\n"
#~ "Veja --dump-resample-methods para valores possíveis de métodos de "
#~ "amostragem.\n"

#~ msgid "=== %d seconds: %d Hz %d ch (%s) -> %d Hz %d ch (%s)"
#~ msgstr "=== %d segundos: %d Hz %d ch (%s) -> %d Hz %d ch (%s)"

#~ msgid "PulseAudio Sound System KDE Routing Policy"
#~ msgstr "Política de roteamento do KDE para Sistema de som PulseAudio"

#~ msgid "Start the PulseAudio Sound System with KDE Routing Policy"
#~ msgstr ""
#~ "Iniciar o sistema de som PulseAudio com política de roteamento do KDE"

#~ msgid "Failed to open configuration file '%s': %s"
#~ msgstr "Falha em abrir o arquivo de configuração \"%s\": %s"

#~ msgid "Failed to load client configuration file.\n"
#~ msgstr "Falha ao carregar o arquivo de configuração do cliente.\n"

#~ msgid "Failed to read environment configuration data.\n"
#~ msgstr "Falha ao ler os dados de configuração do ambiente.\n"

#~ msgid "Successfully dropped root privileges."
#~ msgstr "Os privilégios do root foram retirados com sucesso."

#~ msgid "[%s:%u] rlimit not supported on this platform."
#~ msgstr "[%s:%u] rlimit não tem suporte nessa plataforma."

#~ msgid "XOpenDisplay() failed"
#~ msgstr "XOpenDisplay() falhou"

#~ msgid ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"
#~ msgstr ""
#~ "Saída da fonte #%u\n"
#~ "\tDriver: %s\n"
#~ "\tMódulo proprietário: %s\n"
#~ "\tCliente: %s\n"
#~ "\tFonte: %u\n"
#~ "\tEspecificação da amostragem: %s\n"
#~ "\tMapa dos canais: %s\n"
#~ "\tLatência do buffer: %0.0f usec\n"
#~ "\tLatência da fonte: %0.0f usec\n"
#~ "\tMétodo de reamostragem: %s\n"
#~ "\tPropriedades:\n"
#~ "\t\t%s\n"

#, fuzzy
#~ msgid ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "%s [options] subscribe\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ msgstr ""
#~ "%s [opções] stat\n"
#~ "%s [opções] list\n"
#~ "%s [opções] exit\n"
#~ "%s [opções] upload-sample NOME_DO_ARQUIVO [NOME]\n"
#~ "%s [opções] play-sample NOME [DESTINO]\n"
#~ "%s [opções] remove-sample NOME\n"
#~ "%s [opções] move-sink-input ENTRADA DESTINO\n"
#~ "%s [opções] move-source-output SAÍDA FONTE\n"
#~ "%s [opções] load-module NOME [ARGS ...]\n"
#~ "%s [opções] unload-module MÓDULO\n"
#~ "%s [opções] suspend-sink [DESTINO] 1|0\n"
#~ "%s [opções] suspend-source [FONTE] 1|0\n"
#~ "%s [opções] set-card-profile [PLACA] [PERFIL]\n"
#~ "%s [opções] set-sink-port [DESTINO] [PORTA]\n"
#~ "%s [opções] set-source-port [FONTE] [PORTA]\n"
#~ "%s [opções] set-sink-volume DESTINO VOLUME\n"
#~ "%s [opções] set-source-volume FONTE VOLUME\n"
#~ "%s [opções] set-sink-input-volume ENTRADA VOLUME\n"
#~ "%s [opções] set-sink-mute DESTINO 1|0\n"
#~ "%s [opções] set-source-mute FONTE 1|0\n"
#~ "%s [opções] set-sink-input-mute ENTRADA 1|0\n"
#~ "\n"
#~ "  -h, --help                            Mostra essa ajuda\n"
#~ "      --version                        Mostra a versão\n"
#~ "\n"
#~ "  -s, --server=SERVIDOR                   O nome do servidor a ser "
#~ "conectado\n"
#~ "  -n, --client-name=NOME                Como chamar este cliente no "
#~ "servidor \n"

#~ msgid "%s+%s"
#~ msgstr "%s+%s"

#~ msgid "%s / %s"
#~ msgstr "%s / %s"

#~ msgid "Digital Surround 4.0 (IEC958)"
#~ msgstr "Surround 4.0 digital (IEC958)"

#~ msgid "Low Frequency Emmiter"
#~ msgstr "Emissor de baixa freqüência"

#~ msgid "Invalid client name '%s'\n"
#~ msgstr "Nome do cliente \"%s\" inválido\n"

#~ msgid "Failed to determine sample specification from file.\n"
#~ msgstr ""
#~ "Falha ao determinar a especificação de amostragem a partir do arquivo.\n"

#~ msgid "select(): %s"
#~ msgstr "select(): %s"

#~ msgid "Cannot connect to system bus: %s"
#~ msgstr "Não foi possível conectar com o barramento do sistema: %s"

#~ msgid "Cannot get caller from PID: %s"
#~ msgstr "Não foi possível obter quem chamou pelo PID: %s"

#~ msgid "Cannot set UID on caller object."
#~ msgstr "Não foi possível definir o UID sobre o objeto que chamou."

#~ msgid "Failed to get CK session."
#~ msgstr "Falha em obter a sessão CK."

#~ msgid "Cannot set UID on session object."
#~ msgstr "Não foi possível definir o UID do objeto da sessão."

#~ msgid "Cannot allocate PolKitAction."
#~ msgstr "Não foi possível alocar o PolKitAction."

#~ msgid "Cannot set action_id"
#~ msgstr "Não foi possível definir a action_id"

#~ msgid "Cannot allocate PolKitContext."
#~ msgstr "Não foi possível alocar o PolKitContext."

#~ msgid "Cannot initialize PolKitContext: %s"
#~ msgstr "Não foi possível iniciar o PolKitContext: %s"

#~ msgid "Could not determine whether caller is authorized: %s"
#~ msgstr "Não foi possível determinar se o solicitante está autorizado: %s"

#~ msgid "Cannot obtain auth: %s"
#~ msgstr "Não foi possível obter auth: %s"

#~ msgid "PolicyKit responded with '%s'"
#~ msgstr "PolicyKit respondeu com '%s'"

#~ msgid ""
#~ "High-priority scheduling (negative Unix nice level) for the PulseAudio "
#~ "daemon"
#~ msgstr ""
#~ "Escalonamento de alta prioridade (nível de nice Unix negativo) para o "
#~ "daemon do PulseAudio"

#~ msgid "Real-time scheduling for the PulseAudio daemon"
#~ msgstr "Escalonamento em tempo real para o daemon do PulseAudio"

#~ msgid ""
#~ "System policy prevents PulseAudio from acquiring high-priority scheduling."
#~ msgstr ""
#~ "Uma política do sistema impede que o PulseAudio adquira escalonamento de "
#~ "alta prioridade."

#~ msgid ""
#~ "System policy prevents PulseAudio from acquiring real-time scheduling."
#~ msgstr ""
#~ "Uma política do sistema impede que o PulseAudio adquira o escalonamento "
#~ "em tempo real."

#~ msgid "read() failed: %s\n"
#~ msgstr "read() falhou: %s\n"

#~ msgid "pa_context_connect() failed: %s\n"
#~ msgstr "pa_context_connect() falhou: %s\n"

#~ msgid "We're in the group '%s', allowing high-priority scheduling."
#~ msgstr "Estamos no grupo '%s', permitindo escalonamento de alta prioridade."

#~ msgid "We're in the group '%s', allowing real-time scheduling."
#~ msgstr "Estamos no grupo '%s', permitindo escalonamento em tempo real."

#~ msgid "PolicyKit grants us acquire-high-priority privilege."
#~ msgstr ""
#~ "O PolicyKit assegura-nos a aquisição de privilégio de alta prioridade."

#~ msgid "PolicyKit refuses acquire-high-priority privilege."
#~ msgstr "O PolicyKit recusa a aquisição de privilégios de alta prioridade."

#~ msgid "PolicyKit grants us acquire-real-time privilege."
#~ msgstr "O PolicyKit assegura-nos a aquisição de privilégios de tempo-real."

#~ msgid "PolicyKit refuses acquire-real-time privilege."
#~ msgstr "O PolicyKit recusa a aquisição de privilégios de tempo real."

#~ msgid ""
#~ "High-priority scheduling enabled in configuration but not allowed by "
#~ "policy."
#~ msgstr ""
#~ "O escalonamento de alta prioridade foi habilitado para esta configuração, "
#~ "mas não é permitida pela política."

#~ msgid "Successfully increased RLIMIT_RTPRIO"
#~ msgstr "RLIMIT_RTPRIO aumentado com sucesso"

#~ msgid "RLIMIT_RTPRIO failed: %s"
#~ msgstr "RLIMIT_RTPRIO falhou: %s"

#~ msgid "Giving up CAP_NICE"
#~ msgstr "Abandonando CAP_NICE"

#~ msgid ""
#~ "Real-time scheduling enabled in configuration but not allowed by policy."
#~ msgstr ""
#~ "O escalonamento de tempo real foi habilitado pela configuração, mas não é "
#~ "permitido pela política."

#~ msgid "Limited capabilities successfully to CAP_SYS_NICE."
#~ msgstr "As capacidades foram limitadas com sucesso para CAP_SYS_NICE."

#~ msgid "time_new() failed.\n"
#~ msgstr "time_new() falhou.\n"

#~ msgid "Stream successfully created\n"
#~ msgstr "Fluxo criado com sucesso\n"

#~ msgid "Stream errror: %s\n"
#~ msgstr "Erro de fluxo: %s\n"

#~ msgid "Connection established.\n"
#~ msgstr "Conexão estabelecida.\n"

#~ msgid ""
#~ "%s [options] [FILE]\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -v, --verbose                         Enable verbose operation\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -d, --device=DEVICE                   The name of the sink to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ "      --stream-name=NAME                How to call this stream on the "
#~ "server\n"
#~ "      --volume=VOLUME                   Specify the initial (linear) "
#~ "volume in range 0...65536\n"
#~ "      --channel-map=CHANNELMAP          Set the channel map to the use\n"
#~ msgstr ""
#~ "%s [options] [FILE]\n"
#~ "\n"
#~ "  -h, --help                            Mostra essa ajuda\n"
#~ "      --version                         Mostra a versão\n"
#~ "\n"
#~ "  -v, --verbose                         Habilida a operação detalhada\n"
#~ "\n"
#~ "  -s, --server=SERVER                   O nome do servidor a ser "
#~ "conectado\n"
#~ "  -d, --device=DEVICE                   O nome do destino a ser "
#~ "conectado\n"
#~ "  -n, --client-name=NAME                Como chamar este cliente no "
#~ "servidor\n"
#~ "      --stream-name=NAME                Como chamar este fluxo no "
#~ "servidor\n"
#~ "      --volume=VOLUME                   Especifica o volume inicial "
#~ "(linear) no intervalo 0...65536\n"
#~ "      --channel-map=CHANNELMAP          Define o mapa do canal para uso\n"

#~ msgid ""
#~ "paplay %s\n"
#~ "Compiled with libpulse %s\n"
#~ "Linked with libpulse %s\n"
#~ msgstr ""
#~ "paplay %s\n"
#~ "Compilado com libpulse %s\n"
#~ "Linkado com  libpulse %s\n"

#~ msgid "Invalid channel map\n"
#~ msgstr "Mapa de canais inválido\n"

#~ msgid "Channel map doesn't match file.\n"
#~ msgstr "O mapa dos canais não coincide com o arquivo.\n"

#~ msgid "Using sample spec '%s'\n"
#~ msgstr "Usando a especificação da amostragem '%s'\n"

#, fuzzy
#~ msgid ""
#~ "Called SUID root and real-time and/or high-priority scheduling was "
#~ "requested in the configuration. However, we lack the necessary "
#~ "privileges:\n"
#~ "We are not in group '"
#~ msgstr ""
#~ "A chamada de SUID root e tempo real/alta prioridade no escalonamento foi "
#~ "requisitada pela configuração. Todavia, falta-nos os privilégios "
#~ "necessários:\n"
#~ "Não estamos no grupo'"

#, fuzzy
#~ msgid "--log-time boolean argument"
#~ msgstr "--disallow-exit argumento booleano"

#~ msgid "Default sink name (%s) does not exist in name register."
#~ msgstr "O nome padrão do destino (%s) não existe no registro de nomes."

#~ msgid "Buffer overrun, dropping incoming data\n"
#~ msgstr "Houve estouro de buffer, os dados que chegaram foram descartados\n"

#~ msgid "pa_stream_drop() failed: %s\n"
#~ msgstr "pa_stream_drop() falhou: %s\n"

#~ msgid "muted"
#~ msgstr "mudo"

#~ msgid ""
#~ "*** Autoload Entry #%u ***\n"
#~ "Name: %s\n"
#~ "Type: %s\n"
#~ "Module: %s\n"
#~ "Argument: %s\n"
#~ msgstr ""
#~ "*** Entrada do Autoload #%u ***\n"
#~ "Nome: %s\n"
#~ "Tipo: %s\n"
#~ "Módulo: %s\n"
#~ "Argumento: %s\n"

#~ msgid ""
#~ "' and PolicyKit refuse to grant us priviliges. Dropping SUID again.\n"
#~ "For enabling real-time scheduling please acquire the appropriate "
#~ "PolicyKit priviliges, or become a member of '"
#~ msgstr ""
#~ "' e o PolicyKit recusa-nos a garantia de privilégios. Retirando o SUID "
#~ "outra vez.\n"
#~ " Para habilitar o escalonamento em tempo real, por favo, adquira os "
#~ "privilégios adequados pelo PolicyKit, ou torne-se membro do'"

#~ msgid ""
#~ "', or increase the RLIMIT_NICE/RLIMIT_RTPRIO resource limits for this "
#~ "user."
#~ msgstr ""
#~ "', ou eleve o RLIMIT_NICE/RLIMIT_RTPRIO dos limites do recurso para este "
#~ "usuário."

#~ msgid "socketpair(): %s"
#~ msgstr "socketpair(): %s"
