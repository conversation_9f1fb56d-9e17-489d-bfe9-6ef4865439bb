# translation of ja.po to Japanese
# PulseAudio
# Copyright (C) 2009.
# This file is distributed under the same license as the PACKAGE package.
#
# Hyu_gabaru <PERSON>yu_ichi <<EMAIL>>, 2009.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2009, 2012.
# <PERSON><PERSON> <<EMAIL>>, 2016. #zanata
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016. #zanata
# <AUTHOR> <EMAIL>, 2016. #zanata
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2022-02-20 03:16+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Japanese <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/ja/>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.10.1\n"

#: src/daemon/cmdline.c:113
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [options]\n"
"\n"
"コマンド:\n"
"  -h, --help                            このヘルプの表示\n"
"      --version                         バージョンの表示\n"
"      --dump-conf                       デフォルト設定をダンプ\n"
"      --dump-modules                    利用可能なモジュール一覧をダンプ\n"
"      --dump-resample-methods           利用可能なリサンプル方法をダンプ\n"
"      --cleanup-shm                     古い共有メモリーセグメントをクリーン"
"アップ\n"
"      --start                           デーモンが実行中でない場合、開始\n"
"  -k  --kill                            実行中のデーモンを強制終了\n"
"      --check                           実行中のデーモンを確認 (終了コードの"
"みを返す)\n"
"\n"
"オプション:\n"
"      --system[=BOOL]                   システムワイドのインスタンスとして実"
"行\n"
"  -D, --daemonize[=BOOL]                起動後にデーモン化\n"
"      --fail[=BOOL]                     起動に失敗したら終了\n"
"      --high-priority[=BOOL]            nice レベルを高く設定\n"
"                                        (SUID 時に root で、または\n"
"                                        RLIMIT_NICE を昇格させた場合のみ使用"
"可能)\n"
"      --realtime[=BOOL]                 リアルタイムのスケジューリングを有効"
"化\n"
"                                        (SUID 時に root で、または\n"
"                                        RLIMIT_RTPRIO を昇格させた場合のみ使"
"用可能)\n"
"      --disallow-module-loading[=BOOL]  起動後にユーザーがロードまたはアン"
"ロードを要求したモジュールを許可しない \n"
"      --disallow-exit[=BOOL]            ユーザーが要求した終了を許可しない\n"
"      --exit-idle-time=SECS             デーモンがアイドル状態でこの時間を経"
"過した場合に終了\n"
"      --scache-idle-time=SECS           アイドル状態でこの時間が経過した場合"
"に自動ロードされたサンプルをアンロード\n"
"      --log-level[=LEVEL]               詳細レベルを上昇または設定\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        ログターゲットを指定\n"
"      --log-meta[=BOOL]                ログメッセージにコードの場所を含める\n"
"      --log-time[=BOOL]                 ログメッセージにタイムスタンプを含め"
"る\n"
"      --log-backtrace=FRAMES            ログメッセージにバックトレースを含め"
"る\n"
"  -p, --dl-search-path=PATH             動的共有オブジェクト (プラグイン) の"
"検索パスを設定\n"
"      --resample-method=METHOD          指定されたリサンプル方法を使用\n"
"                                        (使用可能な値は、--dump-resample-"
"methods を参照)\n"
"      --use-pid-file[=BOOL]             PID ファイルを作成\n"
"      --no-cpu-limit[=BOOL]             CPU ロードリミッターをサポートしてい"
"るプラットホームにインストールしない\n"
"      --disable-shm[=BOOL]              共有メモリーサポートを無効化\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         指定された引数で指定されたプラグイ"
"ンモジュールをロード\n"
"  -F, --file=FILENAME                   指定されたスクリプトを実行\n"
"  -C                                    起動後に実行中の TTY でコマンドライン"
"を開く\n"
"                                        \n"
"  -n                                   デフォルトのスクリプトファイルをロード"
"しない \n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize はブーリアン引数を予期します"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail はブーリアン引数を予期します。"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level はログレベル引数を予期します（数値幅0～4、又はデバグ、情報、注"
"記、警告、エラーの中の1つ）"

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority は ブーリアン引数を予期します"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime はブーリアン引数を予期します "

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading はブーリアン引数を予期します "

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit はブーリアン引数を予期します "

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file はブーリアン引数を予期します "

#: src/daemon/cmdline.c:328
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"無効なログターゲット: 'syslog'、'journal'、'stderr' または 'auto' を使用する"
"か、有効なファイル名 'file:<path>', 'newfile:<path>' を使用してください。"

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"無効なログターゲット: 'syslog'、'stderr' または 'auto' を使用するか、有効な"
"ファイル名 'file:<path>', 'newfile:<path>' を使用してください。"

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time ブーリアン引数を予期します "

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta ブーリアン引数を予期します "

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "無効な再サンプル方法 '%s'"

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system はブーリアン引数を予期します"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit はブーリアン引数を予期します"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm はブーリアン引数を予期します"

#: src/daemon/cmdline.c:397
#, fuzzy
msgid "--enable-memfd expects boolean argument"
msgstr "--realtime はブーリアン引数を予期します "

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] 無効なログターゲット '%s'"

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] 無効なログレベル '%s'"

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] 無効な再サンプル方法 '%s'"

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] 無効な rlimit '%s'"

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] 無効なサンプル形式 '%s'"

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] 無効なサンプルレート '%s'"

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] 無効なサンプルチャンネル '%s'"

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] 無効なチャンネルマップ '%s'"

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] 無効なフラグメントの数 '%s'"

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] 無効なフラグメントサイズ '%s'"

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] 無効なナイスレベル '%s'"

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] 無効なサーバータイプ '%s'"

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "設定ファイルを開くのに失敗: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"指定されたデフォルトのチャンネルマップは、指定されたデフォルトのチャンネル数"
"とは異なるチャンネル数を持っています。"

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### 設定ファイルから読み込み: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "名前: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "モジュール情報が使用できません\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "バージョン: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "説明: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "著者: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "使用法: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "１度だけロード: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "破棄の警告: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "パス: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "モジュール %s を開くのに失敗しました: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "オリジナルの lt_dlopen ローダーを見つけるのに失敗しました。"

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "新規の dl ローダーの割り当てに失敗しました。"

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "bind-now-loader の追加に失敗しました。"

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "ユーザー '%s' が見つかりませんでした。"

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "グループ '%s' が見つかりませんでした。"

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "ユーザー '%s' と グループ '%s' の GID が一致しません。"

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "ユーザー '%s' のホームディレクトリは '%s' ではありません。無視します。"

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "'%s' の作成に失敗しました: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "グループ一覧の変更に失敗しました: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "GID の変更に失敗しました: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "UID の変更に失敗しました: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "このプラットフォームではシステム全域のモードはサポートがありません。"

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "コマンドラインの構文解析に失敗しました。"

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"システムモードは非 root ユーザーを拒否しました。D-Bus サーバー照合サービスだ"
"けを開始します。"

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "デーモンのキルに失敗しました: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"このプログラムは root として実行されるように意図されていません（--system を "
"指定していない限り）。"

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Root の権限が必要です。"

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start はシステムインスタンスではサポートがありません。"

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr "ユーザーが設定したサーバー %s は start/autospawn を拒否しています。"

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"ユーザーが設定したサーバー %s はローカルにあるようです。さらに調査します。"

#: src/daemon/main.c:878
#, fuzzy
msgid "Running in system mode, but --disallow-exit not set."
msgstr ""
"システムモードで実行中です、しかし --disallow-exit がセットされていません!"

#: src/daemon/main.c:881
#, fuzzy
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"システムモードで実行中です、しかし --disallow-module-loading がセットされてい"
"ません!"

#: src/daemon/main.c:884
#, fuzzy
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "システムモードで実行中です、強制的に SHM モードを無効にしています!"

#: src/daemon/main.c:889
#, fuzzy
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"システムモードで実行中です、強制的に exit の遊び時間を無効にしています!"

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "stdio の取得に失敗しました。"

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() は失敗: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() は失敗: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() は失敗: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "デーモン開始に失敗しました。"

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() は失敗: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "マシン ID の取得に失敗"

#: src/daemon/main.c:1145
#, fuzzy
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"そうすると、ユーザーはシステムモードで PA を実行しているわけです。その場合、"
"実際にはそうすべきでないことに注意して下さい。\n"
"それでも実行するのでしたら、期待どおりに機能しなくても責任はユーザー自身にあ"
"ります。\n"
"システムモードの使用が通常は良くない方針であることの説明については、http://"
"www.freedesktop.org/wiki/Software/PulseAudio/Documentation/User/"
"WhatIsWrongWithSystemWide/ をお読み下さい。"

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() は失敗"

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() は失敗"

#: src/daemon/main.c:1268
#, fuzzy
msgid "command line arguments"
msgstr "引数が多過ぎます。"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr ""
"デーモンはモジュールの読み込みなしで開始しており、動作を拒否しています。"

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "PulseAudio サウンドシステム"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "PulseAudio サウンドシステムを開始"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "入力"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "ドッキングステーション入力"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "ドッキングステーションマイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "ドッキングステーションライン入力"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "ラインイン"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "マイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "フロントマイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "リアマイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "外部マイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "内部マイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "ラジオ"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "ビデオ"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "自動ゲイン制御"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "自動ゲイン制御なし"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "ブースト"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "ブーストなし"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "アンプ"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "アンプなし"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "低音ブースト"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "低音ブーストなし"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "スピーカー"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "アナログヘッドフォン"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "アナログ入力"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "ドッキングステーションマイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "ヘッドセットマイクロフォン"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "アナログ出力"

#: src/modules/alsa/alsa-mixer.c:2808
#, fuzzy
msgid "Headphones 2"
msgstr "アナログヘッドフォン"

#: src/modules/alsa/alsa-mixer.c:2809
#, fuzzy
msgid "Headphones Mono Output"
msgstr "アナログモノ出力"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "ライン出力"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "アナログモノ出力"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "スピーカー"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "デジタル出力 (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "デジタル入力 (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
#, fuzzy
msgid "Multichannel Input"
msgstr "マルチチャネル"

#: src/modules/alsa/alsa-mixer.c:2817
#, fuzzy
msgid "Multichannel Output"
msgstr "マルチチャネル"

#: src/modules/alsa/alsa-mixer.c:2818
#, fuzzy
msgid "Game Output"
msgstr "%s 出力"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
#, fuzzy
msgid "Chat Output"
msgstr "%s 出力"

#: src/modules/alsa/alsa-mixer.c:2821
#, fuzzy
msgid "Chat Input"
msgstr "%s 入力"

#: src/modules/alsa/alsa-mixer.c:2822
#, fuzzy
msgid "Virtual Surround 7.1"
msgstr "仮想サラウンドシンク"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "アナログモノ"

#: src/modules/alsa/alsa-mixer.c:4564
#, fuzzy
msgid "Analog Mono (Left)"
msgstr "アナログモノ"

#: src/modules/alsa/alsa-mixer.c:4565
#, fuzzy
msgid "Analog Mono (Right)"
msgstr "アナログモノ"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "アナログステレオ"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "モノ"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "ステレオ"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "ヘッドセット"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
#, fuzzy
msgid "Speakerphone"
msgstr "スピーカー"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "マルチチャネル"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "アナログサラウンド 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "アナログサラウンド 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "アナログサラウンド 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "アナログサラウンド 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "アナログサラウンド 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "アナログサラウンド 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "アナログサラウンド 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "アナログサラウンド 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "アナログサラウンド 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "アナログサラウンド 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "アナログサラウンド 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "デジタルステレオ (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "デジタルサラウンド 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "デジタルサラウンド 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "デジタルサラウンド 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "デジタルステレオ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "デジタルサラウンド 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "アナログモノデュプレックス"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "アナログステレオデュプレックス"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "デジタルステレオデュプレックス (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
#, fuzzy
msgid "Multichannel Duplex"
msgstr "マルチチャネル"

#: src/modules/alsa/alsa-mixer.c:4738
#, fuzzy
msgid "Stereo Duplex"
msgstr "アナログステレオデュプレックス"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "オフ"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "%s 出力"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "%s 入力"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, fuzzy, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA が新規のデータをデバイスに書き込むように催促しましたが、書き込むことがあ"
"りません!\n"
"これは多分、ALSA ドライバー '%s' 内のバグです。この問題を ALSA 開発者に 報告"
"して下さい。\n"
"POLLOUT セットで呼び起こされましたが、その結果としての snd_pcm_avail() は 0 "
"又は他の値 < min_avail を返しました。"

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, fuzzy, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA はデバイスから新規データを読み込むように催促しましたが、読み込むものがあ"
"りません!\n"
"これは多分、ALSA ドライバー'%s' 内のバグです。この問題を ALSA 開発者に報告し"
"て下さい。\n"
"POLLIN セットで呼び起こされましたが、その結果としての snd_pcm_avail() は 0 又"
"は他の値 < min_avail を返しました。"

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, fuzzy, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() は 例外的に大きな値を返しました: %lu バイト（%lu ms）。\n"
"これは多分、ALSA ドライバー '%s' 内のバグです。この問題は ALSA 開発者宛に報告"
"を提出して下さい。"

#: src/modules/alsa/alsa-util.c:1249
#, fuzzy, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() は 例外的に大きな値を返しました: %li バイト（%s%lu ms）。\n"
"これは多分、ALSA ドライバー '%s' 内のバグです。この問題は ALSA 開発者宛に報告"
"を提出して下さい。"

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay() がおかしな値を返しました: 遅延 %lu は有効な値 %lu 未満"
"です。\n"
"これは多分、ALSA ドライバー '%s' 内のバグです。この問題は ALSA 開発者宛に報告"
"を提出して下さい。"

#: src/modules/alsa/alsa-util.c:1339
#, fuzzy, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() は 例外的に大きな値を返しました: %lu バイト（%lu "
"ms）。\n"
"これは多分、ALSA ドライバー '%s' 内のバグです。この問題は ALSA 開発者宛に報告"
"を提出して下さい。"

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Bluetooth 入力"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Bluetooth 出力"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "ハンズフリー"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "ヘッドフォン"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "ポータブル"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "車"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "HiFi"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "電話"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "ハイファイ再生 (A2DP シンク)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "ハイファイキャプチャー (A2DP ソース)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
#, fuzzy
msgid "Headset Head Unit (HSP)"
msgstr "ヘッドセットヘッドユニット (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
#, fuzzy
msgid "Headset Audio Gateway (HSP)"
msgstr "ヘッドセットオーディオゲートウェイ (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
#, fuzzy
msgid "Handsfree Head Unit (HFP)"
msgstr "ヘッドセットヘッドユニット (HSP/HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
#, fuzzy
msgid "Handsfree Audio Gateway (HFP)"
msgstr "ヘッドセットオーディオゲートウェイ (HSP/HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
#, fuzzy
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<ソースの名前> source_properties=<ソースのプロパティ> "
"source_master=<フィルタするソースの名前> sink_name=<シンクの名前> "
"sink_properties=<シンクのプロパティ> sink_master=<フィルタするシンクの名前> "
"adjust_time=<レートを再調整する頻度(秒)> adjust_threshold=<再調整するズレ幅の"
"閾値(ミリ秒)> format=<サンプル形式> rate=<サンプルレート> channels=<チャンネ"
"ル数> channel_map=<チャンネルマップ> aec_method=<使用する実装> aec_args=<AEC "
"エンジンのパラメータ> save_aec=</tmp に AEC データを保存> autoloaded=<このモ"
"ジュールが自動でロードされている場合にセット> use_volume_sharing=<yes 又は "
"no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "オン"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "ダミー出力"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr ""
"null である場合でも、常に最低でもシンクが１つロードされるように維持します"

#: src/modules/module-always-source.c:35
#, fuzzy
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr ""
"null である場合でも、常に最低でもシンクが１つロードされるように維持します"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "多目的イコライザー"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<シンクの名前> sink_properties=<シンクのプロパティ> sink_master=<接"
"続先のシンク> format=<サンプル形式> rate=<サンプルレート> channels=<チャンネ"
"ル数> channel_map=<チャンネルマップ> autoloaded=<このモジュールが自動でロード"
"されている場合にセット> use_volume_sharing=<yes 又は no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr ""

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<使っていないフィルターを自動でアンロードするかどうか>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "仮想 LADSPA シンク"

#: src/modules/module-ladspa-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<シンクの名前> sink_properties=<シンクのプロパティ> master=<フィル"
"タするシンク名> format=<サンプル形式> rate=<サンプルレート> channels=<チャン"
"ネル数> channel_map=<入力チャンネルマップ> plugin=<ladspa plugin の名前> "
"label=<ladspa plugin のラベル> control=<コンマで隔離した入力制御値の一覧> "
"input_ladspaport_map=<コンマで隔離した入力 LADSPA ポート番号の一覧> "
"output_ladspaport_map=<コンマで隔離した出力 LADSPA ポート番号の一覧> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "クロック付き NULL シンク"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Null 出力"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "形式のセットに失敗しました: 無効な形式文字列 %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "出力デバイス"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "入力デバイス"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "@HOSTNAME@ 上のオーディオ"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "%s@%s のトンネル"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "%s/%s へのトンネル"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "仮想サラウンドシンク"

#: src/modules/module-virtual-surround-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<シンクの名前> sink_properties=<シンクのプロパティ> master=<フィル"
"ターするシンク名> format=<サンプル形式> rate=<サンプルレート> channels=<チャ"
"ンネル数> channel_map=<チャンネルマップ> use_volume_sharing=<はい または いい"
"え> force_flat_volume=<はい または いいえ> hrir=/path/to/left_hrir.wav "

#: src/modules/raop/module-raop-discover.c:295
#, fuzzy
msgid "Unknown device model"
msgstr "不明なエラーコード"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr ""

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "PulseAudio サウンドサーバー"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "中央前"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "左前"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "右前"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "中央後ろ"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "左後ろ"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "右後ろ"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "サブウーファー"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "中央の左前"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "中央の右前"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "左側"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "右側"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "補助 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "補助 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "補助 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "補助 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "補助 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "補助 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "補助 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "補助 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "補助 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "補助 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "補助 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "補助 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "補助 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "補助 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "補助 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "補助 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "補助 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "補助 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "補助 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "補助 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "補助 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "補助 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "補助 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "補助 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "補助 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "補助 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "補助 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "補助 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "補助 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "補助 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "補助 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "補助 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "上部中央"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "上部中央前"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "上部左前"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "上部右前"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "上部中央後ろ"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "上部左後ろ"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "上部右後ろ"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "無効)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "サラウンド 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "サラウンド 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "サラウンド 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "サラウンド 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "サラウンド 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() は失敗"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() は真を返しました"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "クッキーデータの構文解析に失敗"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "不明な拡張子 '%s' のメッセージを受信"

#: src/pulse/direction.c:37
msgid "input"
msgstr "入力"

#: src/pulse/direction.c:39
msgid "output"
msgstr "出力"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "双方向"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "無効"

#: src/pulsecore/core-util.c:1790
#, fuzzy, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) を所有しているのは私たち (uid %d) ではなく uid %d です! "
"(たとえばこれは、root 以外の PulseAudio に root ユーザーとしてネイティブプロ"
"トコルで接続を試みた場合に起こります。これは行わないでください。)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "はい"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "いいえ"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "autospawn ロックにアクセスできません"

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "入手したファイル '%s' を開くのに失敗しました。"

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"ターゲットファイル '%s'、'%s.1'、'%s.2' ... '%s.%d' のオープンを試みましたが"
"失敗しました。"

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "無効なログターゲット。"

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "内部オーディオ"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "モデム"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "アクセス拒否"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "不明なコマンド"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "無効な引数"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "エンティティは存在します"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "そのようなエンティティはありません"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "接続拒否"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "プロトコルエラー"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "タイムアウト"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "認証されたキーなし"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "内部エラー"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "接続切断"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "エンティティはキルされました"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "無効なサーバー"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "モジュール初期化失敗"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "悪い状態"

#: src/pulse/error.c:54
msgid "No data"
msgstr "データ無し"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "互換性のないプロトコルバージョン"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "大き過ぎます"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "サポートがありません"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "不明なエラーコード"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "そのような拡張子はありません"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "旧来の機能"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "実装の欠如"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "クライアントはフォークされています"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "入力/出力エラー"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "デバイスか、リソースがビジー"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "ストリームの排出に失敗: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "排出したストリームを再生"

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "サーバーへの排出接続"

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() は失敗: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() は失敗: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "ストリームは正常に作成完了"

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() は失敗: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "バッファメトリックス: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "バッファメトリックス: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "サンプル仕様 '%s' 、チャンネルマップ '%s' を使用。"

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "デバイス %s に接続 (インデックス: %u、休止: %s)。"

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "ストリームエラー: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "ストリームデバイス休止 %s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "ストリームデバイス復帰 %s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "ストリームアンダーラン %s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "ストリームオーバーラン %s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "ストリーム開始 %s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "ストリームはデバイス %s へ移動 (%u, %ssuspended)%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "not "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "ストリームバッファの属性変更 %s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Cork リクエストスタックは空です: corking stream"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Cork リクエストスタックは空です: uncorking stream"

#: src/utils/pacat.c:425
#, fuzzy
msgid "Warning: Received more uncork requests than cork requests."
msgstr "警告: uncork リクエストを cork リクエストよりも多く受け取りました"

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "接続が確立 %s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() は失敗: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() は失敗: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "モニターストリームの設定に失敗しました: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() は失敗: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "接続失敗: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF 取得"

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() は失敗: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() は失敗: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "信号取得、退出中"

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "レイテンシー取得に失敗: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "時間: %0.3f sec ; レイテンシー: %0.0f usec"

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() は失敗: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [options]\n"
"\n"
"  -h, --help                            このヘルプの表示\n"
"      --version                         バージョンの表示\n"
"\n"
"  -r, --record                          録音の接続を作成\n"
"  -p, --playback                        再生の接続を作成\n"
"\n"
"  -v, --verbose                         詳細表示を有効化\n"
"\n"
"  -s, --server=SERVER                   接続先のサーバー名\n"
"  -d, --device=DEVICE                  接続先のシンク/ソース名\n"
"  -n, --client-name=NAME                サーバー上でのこのクライアントの名"
"前\n"
"      --stream-name=NAME                サーバー上でのこのストリームの名前\n"
"      --volume=VOLUME                   初期 (一次) ボリュームの範囲 "
"0...65536\n"
"      --rate=SAMPLERATE                Hz 単位でのサンプルレート (デフォルト"
"は 44100)\n"
"      --format=SAMPLEFORMAT             以下のいずれかのサンプルタイプ。"
"s16le、s16be、u8、float32le、float32be、ulaw、alaw、s32le、s32be、s24le、"
"s24be、s24-32le、s24-32be (デフォルトは s16ne)\n"
"      --channels=CHANNELS               チャンネル数。モノは 1 、ステレオは "
"2\n"
"                                        (デフォルトは 2)\n"
"      --channel-map=CHANNELMAP          デフォルトの代わりに使用するマップ\n"
"      --fix-format                      ストリームの接続先となるシンク/ソース"
"からサンプル形式を取る。\n"
"      --fix-rate                        ストリームの接続先となるシンク/ソース"
"からサンプルレートを取る。\n"
"      --fix-channels                    ストリームの接続先となるシンク/ソース"
"からチャンネル数とチャンネルマップを取る。\n"
"      --no-remix                        チャンネルのアップミックスまたはダウ"
"ンミックスを行わない。\n"
"      --no-remap                        名前の代わりにインデックスでチャンネ"
"ルをマップ。\n"
"      --latency=BYTES                   指定された待機時間を要求 (バイト単"
"位)。\n"
"      --process-time=BYTES              リクエストごとの指定された処理時間を"
"要求 (バイト単位)。 \n"
"      --latency-msec=MSEC               指定された待機時間を要求 (ミリ秒単"
"位)。\n"
"      --process-time-msec=MSEC          リクエストごとの指定された処理時間を"
"要求 (ミリ秒単位)。 \n"
"      --property=PROPERTY=VALUE         指定されたプロパティーを値に設定。\n"
"      --raw                             生 PCM データを録音/再生。\n"
"      --passthrough                     パススルーデータ。\n"
"      --file-format[=FFORMAT]           フォーマットされた PCM データを録音/"
"再生。\n"
"      --list-file-formats               利用可能な形式を一覧表示。\n"
"      --monitor-stream=INDEX            インデックス INDEX のシンク入力から録"
"音。\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s でコンパイル\n"
"libpulse %s で接続\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "無効なクライアント名 '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "無効なストリーム名 '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "無効なチャンネルマップ '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "無効なレイテンシー仕様 '%s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "無効なプロセスタイム仕様 '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "無効なプロパティ '%s'"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "不明なファイル形式 '%s'"

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "--monitor-stream の引数解析に失敗しました"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "無効なサンプル仕様"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "引数が多過ぎます。"

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "ファイル用のサンプル仕様の生成に失敗しました。"

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "オーディオファイルを開くのに失敗しました。"

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr "警告: 指定されたサンプルの仕様はファイルからの仕様で上書きされます。"

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "ファイルからのサンプル仕様の決定に失敗しました。"

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "警告: ファイルからのチャンネルマップの決定に失敗しました。"

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "チャンネルマップはサンプル仕様に一致しません。"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "警告: ファイルへのチャンネルマップ書き込みに失敗しました。"

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"サンプル仕様 '%s' とチャンネルマップ '%s' で  %s ストリームを開いています。"

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "録音"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "再生"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "メディア名のセットに失敗しました。"

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() は失敗"

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() は失敗"

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() は失敗"

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() は失敗: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() は失敗"

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() は失敗"

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "名前 [引数 ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "名前|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "名前"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "名前|#N ボリューム"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N ボリューム"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "名前|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "名前|#N KEY=VALUE"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N KEY=VALUE"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "名前 シンク|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "名前 ファイル名"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "パス名"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "ファイル名 シンク|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N シンク|ソース"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "カードプロフィール"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "名前|#N ポート"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "カード-名前|カード-#N ポート オフセット"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "ターゲット"

#: src/utils/pacmd.c:76
#, fuzzy
msgid "NUMERIC-LEVEL"
msgstr "数的レベル"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "フレーム"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr ""

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            このヘルプを表示\n"
"      --version                         バージョンを表示\n"
"コマンドがない場合、pacmd がインタラクティブモードで開始。\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"libpulse %s でコンパイル\n"
"libpulse %s でリンク\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"PulseAudio デーモン自身が稼働していないか、又はセッションデーモンとして稼働し"
"ていません。"

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "ソケット (PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "PulseAudio のキルに失敗"

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "デーモンが応答しません"

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "統計の取得に失敗しました: %s"

#: src/utils/pactl.c:199
#, fuzzy, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "現在使用中: %u ブロックは合計 %s バイトを含む\n"

#: src/utils/pactl.c:205
#, fuzzy, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "総寿命の期間中に割り当て: %u ブロックは合計 %s バイトを含む\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "サンプルのキャッシュサイズ: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "サーバー情報の取得に失敗 : %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, fuzzy, c-format
msgid "%s\n"
msgstr "%s %s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"サーバー文字列: %s\n"
"ライブラリプロトコルバージョン: %u\n"
"サーバープロトコルバージョン: %u\n"
"Is ローカル: %s\n"
"クライアントインデックス: %u\n"
"タイルサイズ: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"ユーザー名: %s\n"
"ホスト名: %s\n"
"サーバー名: %s\n"
"サーバーバージョン: %s\n"
"デフォルトサンプル仕様: %s\n"
"デフォルトチャンネルマップ: %s\n"
"デフォルトシンク: %s\n"
"デフォルトソース: %s\n"
"クッキー: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr ""

#: src/utils/pactl.c:321
msgid "available"
msgstr ""

#: src/utils/pactl.c:322
msgid "not available"
msgstr ""

#: src/utils/pactl.c:331 src/utils/pactl.c:355
#, fuzzy
msgid "Unknown"
msgstr "不明"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr ""

#: src/utils/pactl.c:335
#, fuzzy
msgid "Line"
msgstr "ラインイン"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr ""

#: src/utils/pactl.c:338
#, fuzzy
msgid "Handset"
msgstr "ヘッドセット"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr ""

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr ""

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr ""

#: src/utils/pactl.c:342
msgid "TV"
msgstr ""

#: src/utils/pactl.c:345
msgid "USB"
msgstr ""

#: src/utils/pactl.c:346
#, fuzzy
msgid "Bluetooth"
msgstr "Bluetooth 入力"

#: src/utils/pactl.c:352
msgid "Network"
msgstr ""

#: src/utils/pactl.c:353
#, fuzzy
msgid "Analog"
msgstr "アナログモノ"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "シンク情報の取得に失敗しました: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"シンク #%u\n"
"\t状態: %s\n"
"\t名前: %s\n"
"\t説明: %s\n"
"\tドライバー: %s\n"
"\tサンプル仕様: %s\n"
"\tチャンネルマップ: %s\n"
"\t所有者モジュール: %u\n"
"\tミュート: %s\n"
"\tボリューム: %s\n"
"\t        バランス %0.2f\n"
"\tベースボリューム: %s\n"
"\tモニターソース: %s\n"
"\t待機時間: %0.0f usec, configured %0.0f usec\n"
"\tフラグ: %s%s%s%s%s%s%s\n"
"\tプロパティー:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tポート:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ""

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\t活動中ポート: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\t形式:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "ソース情報の取得に失敗しました: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"ソース #%u\n"
"\t状態: %s\n"
"\t名前: %s\n"
"\t説明: %s\n"
"\tドライバー: %s\n"
"\tサンプル仕様: %s\n"
"\tチャンネルマップ: %s\n"
"\t所有者モジュール: %u\n"
"\tミュート: %s\n"
"\tボリューム: %s\n"
"\t        バランス %0.2f\n"
"\tベースボリューム: %s\n"
"\tシンクのモニター: %s\n"
"\t待機時間: %0.0f usec, configured %0.0f usec\n"
"\tフラグ: %s%s%s%s%s%s\n"
"\tプロパティー:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "n/a"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "モジュール情報の取得に失敗しました: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"モジュール #%u\n"
"\t名前: %s\n"
"\t引数: %s\n"
"\t使用度カウンター: %s\n"
"\tプロパティ:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "クライアント情報の取得に失敗しました: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"クライアント #%u\n"
"\tドライバー: %s\n"
"\tオーナーモジュール: %s\n"
"\tプロパティ:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "カード情報の取得に失敗しました: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"カード #%u\n"
"\t名前: %s\n"
"\tドライバー: %s\n"
"\tモジュール: %s\n"
"\tプロパティ:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tプロフィール:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\t有効なプロフィール: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tプロパティ:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tプロファイルの一部: %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "シンク入力情報の取得に失敗しました: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"シンク入力 #%u\n"
"\tドライバー: %s\n"
"\t所有者モジュール: %s\n"
"\tクライアント: %s\n"
"\tシンク: %u\n"
"\tサンプル仕様: %s\n"
"\tチャンネルマップ: %s\n"
"\t形式: %s\n"
"\tコルク: %s\n"
"\tミュート: %s\n"
"\tボリューム: %s\n"
"\t        バランス %0.2f\n"
"\tバッファー待機時間: %0.0f usec\n"
"\tシンク待機時間: %0.0f usec\n"
"\tリサンプル方法: %s\n"
"\tプロパティ:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "ソース出力情報の取得に失敗しました: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"ソース出力 #%u\n"
"\tドライバー: %s\n"
"\t所有者モジュール: %s\n"
"\tクライアント: %s\n"
"\tソース: %u\n"
"\tサンプル仕様: %s\n"
"\tチャンネルマップ: %s\n"
"\t形式: %s\n"
"\tコルク: %s\n"
"\tミュート: %s\n"
"\tボリューム: %s\n"
"\t        バランス %0.2f\n"
"\tバッファー待機時間: %0.0f usec\n"
"\tソース待機時間: %0.0f usec\n"
"\tリサンプル方法: %s\n"
"\tプロパティ:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "サンプル情報の取得に失敗しました: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"サンプル #%u\n"
"\t名前: %s\n"
"\tサンプル仕様: %s\n"
"\tチャンネルマップ: %s\n"
"\tボリューム: %s\n"
"\t        バランス %0.2f\n"
"\t期間: %0.1fs\n"
"\tサイズ: %s\n"
"\t遅延: %s\n"
"\tファイル名: %s\n"
"\tプロパティ:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "失敗: %s"

#: src/utils/pactl.c:1667
#, fuzzy, c-format
msgid "Send message failed: %s"
msgstr "read() は失敗: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "list-handlers メッセージ応答を正しく解釈できません"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "list-handlers メッセージ応答が JSON 配列ではありません"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "モジュールのアンロードに失敗: モジュール %s はロードされていません"

#: src/utils/pactl.c:1818
#, fuzzy, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"ボリュームの設定に失敗: %d チャンネルのボリューム設定を試みましたが、 チャン"
"ネルはサポートされています = %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "サンプルのアップロードに失敗しました: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "ファイルの早期終了"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "新規"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "変更"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "削除"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "不明"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "シンク"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "ソース"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "シンク入力"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "ソース出力"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "モジュール"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "クライアント"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "サンプルキャッシュ"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "サーバー"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "カード"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "イベント '%s' が %s #%u 上にあります\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT を取得、退出中"

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "無効なボリューム仕様"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "許容範囲外のボリューム\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "ボリューム仕様の数が無効。\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "ボリューム仕様が非整合。\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[オプション]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[タイプ]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "ファイル名 [名前]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "名前 [シンク]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "名前|#N ボリューム [ボリューム ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N ボリューム [ボリューム ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "名前|#N 1|0|切り替え"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|切り替え"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N 形式"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"特別な名前 @DEFAULT_SINK@, @DEFAULT_SOURCE@ と @DEFAULT_MONITOR@\n"
"を使用して、デフォルトのシンク、ソースおよびモニターを指定することができま"
"す。\n"

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                    このヘルプを表示\n"
"      --version                 バージョンを表示\n"
"\n"
"  -s, --server=SERVER           接続先サーバーの名前\n"
"  -n, --client-name=NAME        サーバーでこのクライアントへのコール方法\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"libpulse %s でコンパイル\n"
"libpulse %s でリンク\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "無効なストリーム名 '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "何も指定しないか以下から1つ指定してください: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "ロードするサンプルファイルを指定して下さい"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "サウンドファイルを開くのに失敗しました。"

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "警告: ファイルからサンプル仕様を決定するのに失敗しました。"

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "再生するサンプル名を指定する必要があります"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "削除するサンプル名を指定する必要があります。"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "シンク入力インデックスとシンクを指定する必要があります"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "ソース出力インデックスとソースを指定する必要があります"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "モジュール名と引数を指定する必要があります"

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "モジュールインデックスもしくは名前を指定する必要があります"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"シンクは1つ以上は指定できません。ブーリアン値を1つ指定する必要があります。"

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "無効なサスペンド仕様。"

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"ソースは1つ以上は指定できません。ブーリアン値を1つ指定する必要があります。"

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "カードの名前/インデックスとプロフィール名を指定する必要があります"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "シンクの名前/インデックスとポート名を指定する必要があります"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "シンクの名前を指定する必要があります"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "ソースの名前/インデックスとポート名を指定する必要があります"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "ソースの名前を指定する必要があります"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
#, fuzzy
msgid "You have to specify a sink name/index"
msgstr "シンクの名前を指定する必要があります"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "シンクの名前/インデックスとボリュームを指定する必要があります"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
#, fuzzy
msgid "You have to specify a source name/index"
msgstr "ソースの名前を指定する必要があります"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "ソースの名前/インデックスとボリュームを指定する必要があります"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "シンク入力インデックスとボリュームを指定する必要があります"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "無効なシンク入力インデックス"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "ソース出力インデックスとボリュームを指定する必要があります"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "無効なソース出力インデックス"

#: src/utils/pactl.c:3086
#, fuzzy
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "シンクの名前/インデックスとミュートブーリアンを指定する必要があります"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "無効なミュート仕様"

#: src/utils/pactl.c:3111
#, fuzzy
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "ソースの名前/インデックスとミュートブーリアンを指定する必要があります"

#: src/utils/pactl.c:3126
#, fuzzy
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr "シンク入力インデックスとミュートブーリアンを指定する必要があります"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "無効なシンク入力インデックス仕様"

#: src/utils/pactl.c:3144
#, fuzzy
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "ソース出力インデックスとミュートブーリアンを指定する必要があります"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "無効なソース出力インデックス仕様"

#: src/utils/pactl.c:3162
#, fuzzy
msgid "You have to specify at least an object path and a message name"
msgstr "シンクの名前/インデックスとポート名を指定する必要があります"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"シンクのインデックスとサポートしている形式のセミコロンで隔離した一覧を指定す"
"る必要があります"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr ""
"カードの名前/インデックス、ポート名および待機時間オフセットを指定する必要があ"
"ります"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "待機時間オフセットを解析できませんでした"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "有効なコマンドが指定されていません"

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "復帰の失敗: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "休止の失敗: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "警告: サウンドサーバーはローカルではありません。休止しません。\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "接続失敗 : %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT 取得、退出中\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "警告: 子プロセスは信号 %u で終了しました\n"

#: src/utils/pasuspender.c:228
#, fuzzy, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [オプション] ... \n"
"\n"
"  -h, --help                            このヘルプを表示\n"
"      --version                         バージョンを表示\n"
"  -s, --server=SERVER                   接続先サーバーの名前\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"libpulse %s でコンパイル\n"
"libpulse %s でリンク\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() は失敗\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() は失敗\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() は失敗\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    X11 ディスプレイに接続した現在の PulseAudio のデータを表示 (デフォル"
"ト)\n"
" -e    X11 ディスプレイにローカル PulseAudio データをエキスポート\n"
" -i    X11 ディスプレイからローカル環境変数とクッキーに PulseAudio データをイ"
"ンポート\n"
" -r    X11 ディスプレイから PulseAudio データを削除\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "コマンドラインの構文解析に失敗\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "サーバー: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "ソース: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "シンク: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "クッキー: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "クッキーデータの構文解析に失敗\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "クッキーデータの保存に失敗\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "FQDN の取得に失敗\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "クッキーデータのロードに失敗\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "まだ実装されていません\n"

#~ msgid "Failed to initialize daemon."
#~ msgstr "デーモンの初期化に失敗しました。"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "アナログ出力 (LFE)"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "デジタルパススルー (S/PDIF)"

#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "デジタルパススルー (IEC958)"

#~ msgid "High Fidelity Playback (A2DP)"
#~ msgstr "ハイファイ再生 (A2DP)"

#~ msgid "High Fidelity Capture (A2DP)"
#~ msgstr "ハイファイキャプチャ (A2DP)"

#~ msgid "Telephony Duplex (HSP/HFP)"
#~ msgstr "テレフォニーデュプレックス (HSP/HFP)"

#~ msgid "Handsfree Gateway"
#~ msgstr "ハンズフリーゲートウェイ"

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "s24le, s24be, s24-32le, s24-32be, s32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            このヘルプの表示\n"
#~ "-v, --verbose                         デバッグメッセージをプリント\n"
#~ "      --from-rate=SAMPLERATE          Hz 単位でのサンプルレート下限 (デ"
#~ "フォルトは 44100)\n"
#~ "      --from-format=SAMPLEFORMAT      サンプルタイプの下限 (デフォルトは "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        チャンネル数の下限 (デフォルトは "
#~ "1)\n"
#~ "      --to-rate=SAMPLERATE            Hz 単位でのサンプルレート上限 (デ"
#~ "フォルトは 44100)\n"
#~ "      --to-format=SAMPLEFORMAT        サンプルタイプの上限 (デフォルトは "
#~ "s16le)\n"
#~ "      --to-channels=CHANNELS          チャンネル数の上限 (デフォルトは "
#~ "1)\n"
#~ "      --resample-method=METHOD        リサンプル方法 (デフォルトは auto)\n"
#~ "      --seconds=SECONDS               ストリーム期間の下限 (デフォルトは "
#~ "60)\n"
#~ "\n"
#~ "フォーマットが指定されていない場合、テストはすべてのフォーマットの組み合わ"
#~ "せを実行します。\n"
#~ "\n"
#~ "サンプルタイプは、以下のいずれかである必要があります。s16le、s16be、u8、"
#~ "float32le、float32be、ulaw、alaw、s24le、s24be、s24-32le、s24-32be、"
#~ "s32le、s32be (defaults to s16ne)\n"
#~ "\n"
#~ "リサンプル方法で利用可能な値については、 --dump-resample-methods を参照し"
#~ "てください。\n"
