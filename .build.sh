#!/bin/sh -e
[ -z "$DEBUG" ] || set -x
echo "########## pulseaudio-17.0: build ##########"
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
PATH="/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin:/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin"  PYTHONNOUSERSITE=y /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/ninja   -C /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0//build
