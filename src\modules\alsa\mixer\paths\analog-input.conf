# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; A fallback for devices that lack separate Mic/Line/Aux/Video/TV
; Tuner/FM elements
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 100

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Mic]
required-absent = any

[Element Mic Boost]
required-absent = any

[Element Dock Mic]
required-absent = any

[Element Dock Mic Boost]
required-absent = any

[Element Front Mic]
required-absent = any

[Element Front Mic Boost]
required-absent = any

[Element Int Mic]
required-absent = any

[Element Int Mic Boost]
required-absent = any

[Element Internal Mic]
required-absent = any

[Element Internal Mic Boost]
required-absent = any

[Element Rear Mic]
required-absent = any

[Element Rear Mic Boost]
required-absent = any

[Element Headset]
required-absent = any

[Element Headset Mic]
required-absent = any

[Element Headset Mic Boost]
required-absent = any

[Element Headphone Mic]
required-absent = any

[Element Headphone Mic Boost]
required-absent = any

[Element Line]
required-absent = any

[Element Line Boost]
required-absent = any

[Element Aux]
required-absent = any

[Element Video]
required-absent = any

[Element Mic/Line]
required-absent = any

[Element TV Tuner]
required-absent = any

[Element FM]
required-absent = any

.include analog-input.conf.common
