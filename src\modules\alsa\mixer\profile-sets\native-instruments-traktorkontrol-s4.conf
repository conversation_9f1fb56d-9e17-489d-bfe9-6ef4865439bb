# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Native Instruments Traktor Kontrol S4
;
; This controller has two stereo pairs of input (named "Channel C" and
; "Channel D") and two stereo pairs of output, one "Main Out" and
; "Headphone Out".
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-output-main]
description = Analog Stereo Main Out
device-strings = hw:%f,0,0
channel-map = left,right

[Mapping analog-stereo-output-headphone]
description = Analog Stereo Headphones Out
device-strings = hw:%f,0,1
channel-map = left,right
direction = output

[Mapping analog-stereo-c-input]
description = Analog Stereo Channel C
device-strings = hw:%f,0,1
channel-map = left,right
direction = input

[Mapping analog-stereo-d-input]
description = Analog Stereo Channel D
device-strings = hw:%f,0,1
channel-map = left,right
direction = input

[Profile output:analog-stereo-all+input:analog-stereo-all]
description = Analog Stereo Duplex
output-mappings = analog-stereo-output-main analog-stereo-output-headphone
input-mappings = analog-stereo-c-input analog-stereo-d-input
priority = 100
skip-probe = yes

[Profile output:analog-stereo-main]
description = Analog Stereo Main Output
output-mappings = analog-stereo-output-main
priority = 4
skip-probe = yes

[Profile output:analog-stereo-headphone]
description = Analog Stereo Output Headphones Out
output-mappings = analog-stereo-output-headphone
priority = 3
skip-probe = yes

[Profile input:analog-stereo-c]
description = Analog Stereo Input Channel C
input-mappings = analog-stereo-c-input
priority = 2
skip-probe = yes

[Profile input:analog-stereo-d]
description = Analog Stereo Input Channel D
input-mappings = analog-stereo-d-input
priority = 1
skip-probe = yes

