# Italian translation for PulseAudio.
# Copyright (C) 2008, 2009, 2012, 2015, 2019 The Free Software Foundation, Inc
# This file is distributed under the same license as the pulseaudio package.
#
# <PERSON> <<EMAIL>>, 2008, 2009.
# mario_santagiuliana <mario at marionline.it>, 2009.
# Milo <PERSON>grande <<EMAIL>>, 2009, 2012, 2015, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2021-04-19 20:02+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/it/>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.6\n"

# mamma mia che impressione
#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [OPZIONI]\n"
"\n"
"COMANDI:\n"
"  -h, --help                            Mostra questo aiuto\n"
"      --version                         Mostra la versione\n"
"      --dump-conf                       Riversa la configurazione "
"predefinita\n"
"      --dump-modules                    Riversa l'elenco dei moduli "
"disponibili\n"
"      --dump-resample-methods           Riversa i metodi di ricampionamento\n"
"                                        disponibili\n"
"      --cleanup-shm                     Pulisce i segmenti di memoria "
"condivisa\n"
"                                        esauriti\n"
"      --start                           Avvia il demone se non è in "
"esecuzione\n"
"  -k  --kill                            Uccide un demone in esecuzione\n"
"      --check                           Controlla la presenza di un demone "
"in\n"
"                                        esecuzione (restituisce solo il "
"codice\n"
"                                        di uscita)\n"
"\n"
"OPZIONI:\n"
"      --system[=BOOL]                   Esegue un'istanza di sistema\n"
"  -D, --daemonize[=BOOL]                Rende demone dopo l'avvio\n"
"      --fail[=BOOL]                     Esce quando l'avvio non riesce\n"
"      --high-priority[=BOOL]            Tenta di impostare un livello di "
"nice\n"
"                                        elevato (disponibile solo come "
"root,\n"
"                                        quando SUID o con RLIMIT_NICE "
"elevato)\n"
"      --realtime[=BOOL]                 Tenta di abilitare lo scheduling\n"
"                                        realtime (disponibile solo come "
"root,\n"
"                                        quando SUID o con RLIMIT_RTPRIO "
"elevato)\n"
"      --disallow-module-loading[=BOOL]  Rifiuta il caricamento/rimozione "
"dei\n"
"                                        moduli richiesti dall'utente dopo \n"
"                                        l'avvio\n"
"      --disallow-exit[=BOOL]            Rifiuta le richieste utente di "
"uscita\n"
"      --exit-idle-time=SECONDI          Termina il demone quando inattivo e "
"una\n"
"                                        volta trascorso questo tempo\n"
"      --scache-idle-time=SECONDI        Rimuove i campioni caricati in modo\n"
"                                        automatico quando inattivo e una "
"volta\n"
"                                        trascorso questo tempo\n"
"      --log-level[=LIVELLO]             Incrementa o imposta il livello di\n"
"                                        verbosità\n"
"  -v  --verbose                         Incrementa il livello di verbosità\n"
"      --log-target={auto,syslog,stderr,file:PERC,newfile:PERC}\n"
"                                        Specifica la destinazione del "
"registro\n"
"      --log-meta[=BOOL]                 Include la posizione del codice nei\n"
"                                        messaggi di registro\n"
"      --log-time[=BOOL]                 Include i marcatempo nei messaggi "
"di\n"
"                                        registro\n"
"      --log-backtrace=FRAME             Include un backtrace nei messaggi "
"di\n"
"                                        registro\n"
"  -p, --dl-search-path=PERCORSO         Imposta il percorso di ricerca per "
"gli\n"
"                                        oggetti condivisi dinamici (plugin)\n"
"      --resample-method=METODO          Usa il metodo di ricampionamento "
"indicato\n"
"                                        (vedere --dump-resample-methods per "
"i\n"
"                                        valori ammessi)\n"
"      --use-pid-file[=BOOL]             Crea un file PID\n"
"      --no-cpu-limit[=BOOL]             Non installa un limitatore di "
"carico\n"
"                                        della CPU sulle piattaforme che lo\n"
"                                        supportano.\n"
"      --disable-shm[=BOOL]              Disabilita il supporto alla memoria\n"
"                                        condivisa.\n"
"      --enable-memfd[=BOOL]             Abilita il supporto alla memoria\n"
"                                        condivisa memfd\n"
"\n"
"SCRIPT DI AVVIO:\n"
"  -L, --load=\"MODULO ARGOMENTI\"         Carica il modulo di plugin "
"specificato\n"
"                                        con gli argomenti specificati\n"
"  -F, --file=NOME_FILE                  Esegue lo script specificato\n"
"  -C                                    Apre una riga di comando sul TTY in\n"
"                                        esecuzione dopo l'avvio\n"
"\n"
"  -n                                    Non carica il file script "
"predefinito\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize richiede un argomento booleano"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail richiede un argomento booleano"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level richiede il livello di registro come argomento (sia "
"nell'intervallo numerico 0..4 oppure uno tra debug, info, notice, warn, "
"error)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority richiede un argomento booleano"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime richiede un argomento booleano"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading richiede un argomento booleano"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit richiede un argomento booleano"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file richiede un argomento booleano"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Destinazione del registro non valida: usare \"syslog\", \"journal\", \"stderr"
"\" o \"auto\" oppure un nome di file valido \"file:<percorso>\", \"newfile:"
"<percorso>\"."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Destinazione del registro non valida: usare \"syslog\", \"stderr\" o \"auto"
"\" oppure un nome di file valido \"file:<percorso>\", \"newfile:<percorso>\"."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time richiede un argomento booleano"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta richiede un argomento booleano"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Metodo di ricampionamento «%s» non valido."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system richiede un argomento booleano"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit richiede un argomento booleano"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm richiede un argomento booleano"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd richiede un argomento booleano"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Destinazione di registro «%s» non valida."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Livello di registro «%s» non valido."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Metodo di ricampionamento «%s» non valido."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] rlimit «%s» non valido."

# o campionamento?? ma campionamento non è sampling?
#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Formato di campionamento «%s» non valido."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Frequenza di campionamento '%s' non valida."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Canali di campionamento «%s» non validi."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Mappa del canale «%s» non valida."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Numero di frammenti «%s» non valido."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Dimensione dei frammenti «%s» non valida."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Livello di nice «%s» non valido."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Tipo di server «%s» non valido."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Apertura del file di configurazione non riuscita: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"La mappa del canale predefinita specificata presenta un numero diverso di "
"canali rispetto a quello predefinito specificato."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Lettura dal file di configurazione: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Nome: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Nessuna informazione disponibile sul modulo\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Versione: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Descrizione: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Autore: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Uso: %s\n"

# %s è sì/no
#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Caricato una sola volta: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "ATTENZIONE, DEPRECATI: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Percorso: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "Apertura del modulo %s non riuscita: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Ricerca del loader lt_dlopen originale non riuscita."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Allocazione del nuovo loader dl non riuscita."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Aggiunta di bind-now-loader non riuscita."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Ricerca dell'utente «%s» non riuscita."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Ricerca del gruppo «%s» non riuscita."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "Il GID dell'utente «%s» e del gruppo «%s» non corrispondono."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "La directory home dell'utente «%s» non è «%s», ignorato."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Creazione di «%s» non riuscita: %s"

# group list ????
#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Cambio dell'elenco di gruppo non riuscito: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Cambio di GID non riuscito: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Cambio di UID non riuscito: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "Modalità di sistema non supportata su questa piattaforma."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Analisi della riga di comando non riuscita."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"Modalità sistema non concessa a utenti non root. Viene avviato solamente il "
"servizio di lookup del server D-Bus."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Terminazione del demone non riuscita: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Questo programma non è pensato per essere eseguito come root (a meno di "
"specificare --system)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Richiesti privilegi di root."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start non supportato per le istanze di sistema."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""
"Server configurato dall'utente in %s, si rifiuta di avviarsi o di eseguire "
"autospawn."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""
"Server configurato dall'utente in %s, sembra essere locale. Esame più "
"approfondito."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr "In esecuzione in modalità sistema, ma --disallow-exit non impostato."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"In esecuzione in modalità sistema, ma --disallow-module-loading non "
"impostato."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr ""
"In esecuzione in modalità sistema, disabilitata in modo forzato la modalità "
"SHM."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr ""
"In esecuzione in modalità sistema, disabilitato in modo forzato il tempo di "
"uscita per inattività."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Acquisizione di STDIO non riuscita."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() non riuscita: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() non riuscita: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() non riuscita: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Avvio del demone non riuscito."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() non riuscita: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Recupero dell'ID della macchina non riuscito"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"PulseAudio è in esecuzione in modalità sistema. Assicurarsi che sia "
"esattamente ciò che si desidera fare.\n"
"Consultare http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/"
"User/WhatIsWrongWithSystemWide/ per maggiori informazioni sul perché la "
"modalità sistema è una pessima idea."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() non riuscita."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() non riuscita."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "argomenti della riga di comando"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"Inizializzazione del demone non riuscita a causa di errori nell'eseguire i "
"comandi di avvio. Origine dei comandi: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "Avvio del demone senza alcun modulo caricato, rifiuta di lavorare."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "Sistema sonoro PulseAudio"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Avvia il sistema sonoro PulseAudio"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "Ingresso"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "Ingresso docking station"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "Microfono docking station"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "Linea in docking station"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "Line-In"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "Microfono"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "Microfono anteriore"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "Microfono posteriore"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "Microfono esterno"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "Microfono interno"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "Radio"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "Video"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "Controllo automatico del guadagno"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "Nessun controllo automatico del guadagno"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "Boost"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "Nessun boost"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "Amplificatore"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "Nessun amplificatore"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "Incremento bassi"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "Nessun incremento bassi"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "Altoparlante"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "Cuffie analogiche"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "Ingresso analogico"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "Microfono docking station"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "Microfono auricolare"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "Uscita analogica"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "Cuffie 2"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "Uscita mono cuffie"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "Line-Out"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "Uscita mono analogica"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "Altoparlanti"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "Uscita digitale (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "Ingresso digitale (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "Ingresso multi canale"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "Uscita multi canale"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "Uscita gioco"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "Uscita chat"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "Ingresso chat"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "Surround virtuale 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Mono analogico"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "Mono analogico (sinistra)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "Mono analogico (destra)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Stereo analogico"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Mono"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Stereo"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "Cuffie con microfono"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "Viva voce"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "Multi canale"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "Surround analogico 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "Surround analogico 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "Surround analogico 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Surround analogico 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Surround analogico 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Surround analogico 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Surround analogico 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "Surround analogico 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "Surround analogico 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "Surround analogico 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Surround analogico 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Stereo digitale (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Surround digitale 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Surround digitale 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Surround digitale 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Stereo digitale (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Surround digitale 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "Chat"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "Gioco"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "Duplex mono analogico"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "Duplex stereo analogico"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Duplex stereo digitale (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "Duplex multi canale"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "Duplex stereo"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "Chat mono + Surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Spento"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "Uscita «%s»"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "Ingresso «%s»"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"Attivazione da parte di ALSA per scrivere nuovi dati sul dispositivo, ma non "
"c'era nulla da scrivere.\n"
"Molto probabilmente si tratta di un bug nei driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori di ALSA.\n"
"Attivazione avvenuta con POLLOUT impostato; tuttavia, una successiva "
"snd_pcm_avail() ha ritornato 0 o un altro valore < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"Attivazione da parte di ALSA per leggere nuovi dati dal dispositivo, ma non "
"c'era nulla da leggere.\n"
"Molto probabilmente si tratta di un bug nei driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori di ALSA.\n"
"Attivazione avvenuta con POLLIN impostato; tuttavia, una successiva "
"snd_pcm_avail() ha ritornato 0 o un altro valore < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() ha restituito un valore molto grande: %lu byte (%lu ms).\n"
"Molto probabilmente si tratta di un bug nel driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori ALSA."
msgstr[1] ""
"snd_pcm_avail() ha restituito un valore molto grande: %lu byte (%lu ms).\n"
"Molto probabilmente si tratta di un bug nel driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori ALSA."

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() ha restituito un valore molto grande: %li byte (%s%lu ms).\n"
"Molto probabilmente si tratta di un bug nel driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori ALSA."
msgstr[1] ""
"snd_pcm_delay() ha restituito un valore molto grande: %li byte (%s%lu ms).\n"
"Molto probabilmente si tratta di un bug nel driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori ALSA."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail() ha restituito dei valori strani: delay %lu è minore di avail "
"%lu.\n"
"Molto probabilmente si tratta di un bug nel driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori ALSA."

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() ha restituito un valore molto grande: %lu byte (%lu "
"ms).\n"
"Molto probabilmente si tratta di un bug nel driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori ALSA."
msgstr[1] ""
"snd_pcm_mmap_begin() ha restituito un valore molto grande: %lu byte (%lu "
"ms).\n"
"Molto probabilmente si tratta di un bug nel driver ALSA «%s». Segnalare "
"questo problema agli sviluppatori ALSA."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "Ingresso Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "Uscita Bluetooth"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "Sistema mani-libere"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "Cuffie"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "Portabile"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "Automobile"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "HiFi"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "Telefono"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "Riproduzione ad alta fedeltà (sink A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "Registrazione ad alta fedeltà (sorgente A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "Unità headset head (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "Gateway headset audio (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "Unità handsfree head (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "Gateway handsfree audio (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<nome della sorgente> source_properties=<proprietà della "
"sorgente> source_master=<nome della sorgente da filtrare> sink_name=<nome "
"del sink> sink_properties=<proprietà del sink> sink_master=<nome del sink da "
"filtrare> adjust_time=<quando spesso regolare il campionamento in s> "
"adjust_threshold=<di quanto regolare lo scansamento in ms> format=<formato "
"compionamento> rate=<frequenza campionamento> channels=<numeo di canali> "
"channel_map=<mappa canali ingresso> aec_method=<implementazione da usare> "
"aec_args=<parametri per il motore AEC> save_aec=<salva i dati AEC in /tmp> "
"autoloaded=<imposta se il modulo viene caricato automaticamente> "
"use_volume_sharing=<yes o no> use_master_format=<yes o no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "On"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Uscita dummy"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "Mantiene sempre almeno un sink caricato anche se è nullo"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "Mantiene sempre almeno una sorgente caricata anche se è nulla"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "Equalizzatore generale"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<nome del sink> sink_properties=<proprietà del sink> "
"sink_master=<sink a cui connettersi> format=<formato campionamento> "
"rate=<frequenza campionamento> channels=<numero di canali> "
"channel_map=<mappa canali> autoloaded=<imposta se il modulo viene caricato "
"automaticamente> use_volume_sharing=<yes o no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "Equalizzatore basato su FFT su %s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<rimuovere automaticamente i filtri non usati?>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "Sink LADSPA virtuale"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<nome del sink> sink_properties=<proprietà del sink> "
"sink_input_properties=<proprietà del sink ingresso> master=<nome del sink da "
"filtrare> sink_master=<nome del sink da filtrare> format=<formato "
"campionamento> rate=<frequenza campionamento> channels=<numero di canali> "
"channel_map=<mappa canali ingresso> plugin=<nome plugin ladspa> "
"label=<etichetta plugin ladspa> control=<valori di controllo separati da "
"virgole> input_ladspaport_map=<nomi di porte d'ingresso LADSPA separati da "
"virgole> output_ladspaport_map=<nomi di porte d'uscita LADSPA separati da "
"virgole> autoloaded=<imposta se il modulo viene caricato automaticamente> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "Sink NULL temporizzato"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "Uscita nulla"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Impostazione del formato non riuscita: stringa %s non valida"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "Dispositivi di uscita"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "Dispositivi di ingresso"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "Audio su @HOSTNAME@"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "Tunnel per %s@%s"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "Tunnel verso %s/%s"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "Sink surround virtuale"

#: src/modules/module-virtual-surround-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<nome del sink> sink_properties=<proprietà del sink> master=<nome "
"del sink da filtrare> sink_master=<nome del sink da filtrare> "
"format=<formato campionamento> rate=<frequenza campionamento> "
"channels=<numero di canali> channel_map=<mappa dei canali> "
"use_volume_sharing=<yes o no> force_flat_volume=<yes o no> hrir=/percorso/al/"
"file/left_hrir.wav autoloaded=<imposta se il modulo viene caricato "
"automaticamente> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "Modello dispositivo sconosciuto"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "Profilo standard RAOP"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "Server sonoro PulseAudio"

# frontale centrale non si usa in HiFi
# solo centrale.
#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Centrale"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Frontale sinistro"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Frontale destro"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Centrale posteriore"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Posteriore sinistro"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Posteriore destro"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "Subwoofer"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Frontale sinistra-del-centro"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Frontale destra-del-centro"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Laterale sinistro"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Laterale destro"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Ausiliario 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Ausiliario 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Ausiliario 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Ausiliario 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Ausiliario 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Ausiliario 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Ausiliario 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Ausiliario 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Ausiliario 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Ausiliario 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Ausiliario 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Ausiliario 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Ausiliario 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Ausiliario 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Ausiliario 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Ausiliario 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Ausiliario 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Ausiliario 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Ausiliario 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Ausiliario 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Ausiliario 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Ausiliario 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Ausiliario 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Ausiliario 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Ausiliario 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Ausiliario 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Ausiliario 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Ausiliario 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Ausiliario 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Ausiliario 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Ausiliario 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Ausiliario 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Centrale superiore"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Superiore frontale centrale"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Superiore frontale sinistro"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Superiore frontale destro"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Superiore posteriore centrale"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Superiore posteriore sinistro"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Superiore posteriore destro"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(non valido)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Surround 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Surround 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Surround 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Surround 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Surround 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() non riuscita"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error() ha restituito VERO"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Analisi dei dati cookie non riuscita"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Ricevuto messaggio per l'estensione sconosciuta «%s»"

#: src/pulse/direction.c:37
msgid "input"
msgstr "ingresso"

#: src/pulse/direction.c:39
msgid "output"
msgstr "uscita"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "bidirezionale"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "non valido"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s) non è di nostra proprietà (uid %d), ma dello uid %d "
"(ciò potrebbe verificarsi se si tenta la connessione come utente root verso "
"PulseAudio eseguito non da root, attraverso il protocollo nativo: non fare "
"ciò)."

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "sì"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "no"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Impossibile accedere al lock di autospawn."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "Impossibile aprire il file di destinazione «%s»."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"Tentativo di aprire i file di destinazione «%s», «%s.1», «%s.2» ... «%s.%d» "
"non riuscito."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "Destinazione di registrazione non valida."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Audio interno"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Accesso negato"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Comando sconosciuto"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Argomento non valido"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "L'entità esiste"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Entità inesistente"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Connessione rifiutata"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Errore di protocollo"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Timeout"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "Nessuna chiave di autenticazione"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Errore interno"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Connessione terminata"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Entità uccisa"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Server non valido"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Inizializzazione del modulo non riuscita"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Stato errato"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Nessun dato"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Versione di protocollo incompatibile"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Troppo grande"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Non supportato"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Codice d'errore sconosciuto"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Estensione inesistente"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Funzionalità obsoleta"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Implementazione mancante"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Fork del client"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Errore di input/output"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Dispositivo o risorsa occupata"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s ch %u %u Hz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Svuotamento dello stream non riuscito: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Stream di riproduzione svuotato."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Svuotamento della connessione sul server."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() non riuscita: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() non riuscita: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Creazione dello stream riuscita."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() non riuscita: %s"

# maxlength, fragsize e gli altri non so se vanno tradotti...
#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Metriche del buffer: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

# maxlength e fragsize non so se vanno tradotti...
#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Metriche del buffer: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "In uso specifica di campionamento «%s», mappa dei canali «%s»."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Collegato al dispositivo %s (indice: %u, sospeso: %s)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Errore di stream: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Device stream sospeso.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Device stream ripristinato.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Underrun dello stream.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Overrun dello stream.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Stream avviato.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Stream spostato sul device %s (%u, %ssospeso).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "non "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Attributi del buffer di stream cambiati.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Lo stack delle richieste di blocco è vuoto: viene bloccato il flusso"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Lo stack delle richieste di blocco è vuoto: viene sbloccato il flusso"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr "Attenzione: ricevute più richieste di sblocco che di blocco."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Connessione stabilita.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() non riuscita: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() non riuscita: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Impostazione dello stream di monitor non riuscita: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() non riuscita: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Connessione non riuscita: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "Ricevuto EOF."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() non riuscita: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() non riuscita: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Ricevuto il segnale, uscita."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Recupero della latenza non riuscito: %s"

# dubbio: tempo o durata??
#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Tempo: %0.3f sec; Latenza: %0.0f microsec."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() non riuscita: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [OPZIONI]\n"
"%s\n"
"\n"
"  -h, --help                            Mostra questo aiuto\n"
"      --version                         Mostra la versione\n"
"\n"
"  -r, --record                          Crea una connessione per registrare\n"
"  -p, --playback                        Crea una connessione per riprodurre\n"
"\n"
"  -v, --verbose                         Abilita la modalità prolissa\n"
"\n"
"  -s, --server=SERVER                   Il nome del server a cui "
"connettersi\n"
"  -d, --device=DEVICE                   Il nome del sink/sorgente a cui\n"
"                                        connettersi\n"
"  -n, --client-name=NOME                Come chiamare questo client sul "
"server\n"
"      --stream-name=NOME                Come chiamare questo stream sul "
"server\n"
"      --volume=VOLUME                   Specifica il volume iniziale "
"(lineare) \n"
"                                        nell'intervallo 0...65536\n"
"      --rate=FREQ_CAMP                  La frequenza di campionamento in Hz\n"
"                                        (44100 come predefinita)\n"
"      --format=FORM_CAMP                Il tipo di campionamento. Valori "
"ammessi\n"
"                                        sono: s16le, s16be, u8, float32le,\n"
"                                        float32be, ulaw, alaw, s32le, s32be\n"
"                                        s24le, s24be, s24-32le, s24-32be\n"
"                                        (s16ne come predefinito)\n"
"      --channels=CANALI                 Il numero di canali, 1 per mono, 2 "
"per\n"
"                                        stereo (2 come predefinito)\n"
"      --channel-map=MAP_CANALI          La mappa dei canali da usare al "
"posto di\n"
"                                        quella predefinita\n"
"      --fix-format                      Recupera il formato di "
"campionamento\n"
"                                        dal sink a cui lo stream sta per "
"essere\n"
"                                        connesso\n"
"      --fix-rate                        Recupera la frequenza di "
"campionamento\n"
"                                        dal sink a cui lo stream sta per "
"essere\n"
"                                        connesso\n"
"      --fix-channels                    Recupera il numero di canali e la "
"mappa\n"
"                                        dei canali dal sink a cui lo "
"stream \n"
"                                        sta per essere connesso\n"
"      --no-remix                        Non esegue l'upmix o il downmix \n"
"                                        dei canali\n"
"      --no-remap                        Mappa i canali per indice invece "
"che \n"
"                                        per nome\n"
"      --latency=BYTE                    Richiede la latenza specificata in "
"byte\n"
"      --process-time=BYTE               Richiede il tempo di elaborazione "
"per\n"
"                                        richiesta specificato in byte\n"
"      --latency-msec=MSEC               Richiede la latenza specificata in "
"msec\n"
"      --process-time-msec=MSEC          Richiede il tempo di elaborazione "
"per\n"
"                                        richiesta specificato in msec\n"
"      --property=PROPRIETÀ=VAL          Imposta la proprietà al valore "
"specificato\n"
"      --raw                             Registra/Riproduce dati PCM grezzi\n"
"      --passthrough                     Dati pass-through\n"
"      --file-format=FFORMAT             Registra/Riproduce dati PCM "
"formattati\n"
"      --list-file-formats               Elenca i formati disponibili\n"
"      --monitor-stream=INDICE           Registra dall'input sink con INDICE\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr "Riproduce file audio codificati su un server audio PulseAudio."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr "Cattura dati audio da un server audio PulseAudio e lo scrive su file."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"Cattura dati audio da un server audio PulseAudio e lo scrive sullo STDOUT o "
"sul file specificato."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"Riproduce dati audio dallo STDIN o dal file specificato su un server audio "
"PulseAudio."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Compilato con libpulse %s\n"
"Link eseguito con libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Nome del client «%s» non valido"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Nome dello stream «%s» non valido"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Mappa dei canali «%s» non valida"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Specifica di latenza «%s» non valida"

# esecuzione???
#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Specifica di tempo di elaborazione «%s» non valida"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Proprietà «%s» non valida"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Formato file %s sconosciuto."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "Analisi dell'argomento per --monitor-stream non riuscita"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Specifica di campionamento non valida"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Troppi argomenti."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Generazione della specifica di campionamento per il file non riuscita."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Apertura del file audio non riuscita."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr ""
"Attenzione: la specifica di campionamento indicata verrà soprascritta con "
"quella dal file."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Determinazione della specifica di campionamento dal file non riuscita."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr ""
"Attenzione: determinazione della mappa dei canali dal file non riuscita."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "La mappa dei canali non corrisponde alla specifica di campionamento"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Attenzione: scrittura della mappa dei canali su file non riuscita."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Apertura di uno stream %s con specifica di campionamento «%s» e mappa dei "
"canali «%s»."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "registrazione"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "riproduzione"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "Impostazione nome del supporto non riuscita."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() non riuscita."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() non riuscita."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() non riuscita."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() non riuscita: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() non riuscita."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() non riuscita."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "NOME [ARG ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "NOME|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "NOME"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "NOME|#N VOLUME"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N VOLUME"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "NOME|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "NOME|#N CHIAVE=VALORE"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N CHIAVE=VALORE"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "NOME SINK|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "NOME NOMEFILE"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "NOMEPERCORSO"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "NOMEFILE SINK|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N SINK|SORGENTE"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "PROFILO SCHEDA"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "NOME|#N PORTA"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "NOME-SCHEDA|SCHEDA-#N PORTA OFFSET"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "OBIETTIVO"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "LIVELLO-NUMERICO"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "FRAME"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "DESTINATARIO MESSAGGIO [PARAMETRI_MESSAGGIO]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help                            Mostra questo aiuto\n"
"      --version                         Mostra la versione\n"
"Quando non viene fornito alcun comando, pacmd si avvia in modalità "
"interattiva.\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"Compilato con libpulse %s\n"
"Collegato con libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"Nessun demone PulseAudio in esecuzione o non in esecuzione come demone di "
"sessione."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Uccisione del demone PulseAudio non riuscita."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Il demone non sta rispondendo."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Recupero delle statistiche non riuscito: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Attualmente in uso: %u blocco contenente %s byte in totale.\n"
msgstr[1] "Attualmente in uso: %u blocchi contenenti %s byte in totale.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] ""
"Allocato durante l'intera esecuzione: %u blocco contenente %s byte in "
"totale.\n"
msgstr[1] ""
"Allocato durante l'intera esecuzione: %u blocchi contenenti %s byte in "
"totale.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Dimensione della cache dei campioni: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Recupero delle informazioni del server non riuscito: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr ""

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"Stringa server: %s\n"
"Versione protocollo libreria: %u\n"
"Versione protocollo server: %u\n"
"Locale: %s\n"
"Indice client: %u\n"
"Dimensione tile: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Nome utente: %s\n"
"Nome host: %s\n"
"Nome server: %s\n"
"Versione server: %s\n"
"Specifica di campionamento predefinita: %s\n"
"Mappa del canale predefinita: %s\n"
"Sink predefinito: %s\n"
"Sorgente predefinita: %s\n"
"Cookie: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "disponibilità sconosciuta"

#: src/utils/pactl.c:321
msgid "available"
msgstr "disponibile"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "non disponibile"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "Sconosciuto"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Aux"

#: src/utils/pactl.c:335
msgid "Line"
msgstr "Line"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Mic"

#: src/utils/pactl.c:338
#, fuzzy
msgid "Handset"
msgstr "Cuffie con microfono"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "Auricolare"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "SPDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "TV"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "Bluetooth"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "Rete"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "Analogico"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Recupero delle informazioni del sink non riuscito: %s"

# nel relativo messaggio per il source
# c'è "monitor of sink", quindi assumo che
# qui dovesse essere "monitor of source"
#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink #%u\n"
"\tStato: %s\n"
"\tNome: %s\n"
"\tDescrizione: %s\n"
"\tDriver: %s\n"
"\tSpecifica di campionamento: %s\n"
"\tMappa dei canali: %s\n"
"\tModulo di appartenenza: %u\n"
"\tMuto: %s\n"
"\tVolume: %s\n"
"\t        bilanciamento %0.2f\n"
"\tVolume base: %s\n"
"\tMonitor della sorgente: %s\n"
"\tLatenza: %0.0f usec, configurata %0.0f usec\n"
"\tFlag: %s%s%s%s%s%s%s\n"
"\tProprietà:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tPorte:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (tipo: %s, priorità: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
#, fuzzy
msgid ", availability group: "
msgstr ", gruppo disponibilità: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tPorta attiva: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\tFormati:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Recupero delle informazioni della sorgente non riuscito: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sorgente #%u\n"
"\tStato: %s\n"
"\tNome: %s\n"
"\tDescrizione: %s\n"
"\tDriver: %s\n"
"\tSpecifica di campionamento: %s\n"
"\tMappa dei canali: %s\n"
"\tModulo di appartenenza: %u\n"
"\tMuto: %s\n"
"\tVolume: %s\n"
"\t        bilanciamento %0.2f\n"
"\tVolume base: %s\n"
"\tMonitor del sink: %s\n"
"\tLatenza: %0.0f usec, configurata %0.0f usec\n"
"\tFlag: %s%s%s%s%s%s\n"
"\tProprietà:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "n/d"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Recupero delle informazioni del modulo non riuscito: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Modulo #%u\n"
"\tNome: %s\n"
"\tArgomento: %s\n"
"\tContatore utilizzi: %s\n"
"\tProprietà:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Recupero delle informazioni del client non riuscito: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Client #%u\n"
"\tDriver: %s\n"
"\tModulo di appartenenza: %s\n"
"\tProprietà:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Recupero delle informazioni della scheda non riuscito: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Scheda #%u\n"
"\tNome: %s\n"
"\tDriver: %s\n"
"\tModulo di appartenenza: %s\n"
"\tProprietà:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tProfili:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr "\t\t%s: %s (sink: %u, sorgenti: %u, priorità: %u, disponibile: %s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tProfilo attivo: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (tipo: %s, priorità: %u, offset latenza: %<PRId64> usec%s%s, %s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\tProprietà:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\tParte dei profili: %s"

# Sink input
# A stream that is connected to an output device, i.e. an input for a sink.
#
# from http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/Developer/Clients/WritingVolumeControlUIs/
#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Recupero delle informazioni dell'ingresso per il sink non riuscito: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink d'ingresso #%u\n"
"\tDriver: %s\n"
"\tModulo di appartenenza: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSpecifica di campionamento: %s\n"
"\tMappa dei canali: %s\n"
"\tFormato: %s\n"
"\tCorked: %s\n"
"\tMuto: %s\n"
"\tVolume: %s\n"
"\t        bilanciamento %0.2f\n"
"\tLatenza del buffer: %0.0f usec\n"
"\tLatenza del sink: %0.0f usec\n"
"\tMetodo di ricampionamento: %s\n"
"\tProprietà:\n"
"\t\t%s\n"

# Source output
# A stream that is connected to an input device, i.e. an output of a source.
#
# from http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/Developer/Clients/WritingVolumeControlUIs/
#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr ""
"Recupero delle informazioni dell'uscita per la sorgente non riuscito: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sorgente d'uscita #%u\n"
"\tDriver: %s\n"
"\tModulo di appartenenza: %s\n"
"\tClient: %s\n"
"\tSorgente: %u\n"
"\tSpecifica di campionamento: %s\n"
"\tMappa dei canali: %s\n"
"\tFormato: %s\n"
"\tCorked: %s\n"
"\tMuto: %s\n"
"\tVolume: %s\n"
"\t        bilanciamento %0.2f\n"
"\tLatenza del buffer: %0.0f usec\n"
"\tLatenza del sink: %0.0f usec\n"
"\tMetodo di ricampionamento: %s\n"
"\tProprietà:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Recupero delle informazioni del campione non riuscito: %s"

# campiona lazy??
#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Campione #%u\n"
"\tNome: %s\n"
"\tSpecifica di campionamento: %s\n"
"\tMappa dei canali: %s\n"
"\tVolume: %s\n"
"\t        bilanciamento %0.2f\n"
"\tDurata: %0.1f s\n"
"\tDimensione: %s\n"
"\tLazy: %s\n"
"\tNome file: %s\n"
"\tProprietà:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Fallimento: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "Invia messaggio non riuscito: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr ""

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr ""

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Scaricamento del modulo non riuscito: modulo %s non caricato"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"Impostazione del volume non riuscita: tentata l'impostazione dei volumi per "
"%d canale dove i canali supportati = %d\n"
msgstr[1] ""
"Impostazione del volume non riuscita: tentata l'impostazione dei volumi per "
"%d canali dove i canali supportati = %d\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Caricamento del campione non riuscito: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Fine del file prematura"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "nuovo"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "modifica"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "rimuovi"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "sconosciuto"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "sink"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "sorgente"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "sink-input"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "sorgente-output"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "modulo"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "client"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "sample-cache"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "server"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "scheda"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "Evento «%s» su %s #%u\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "Ricevuto SIGINT, uscita."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Specifica di volume non valida"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "Volume oltre l'intervallo permesso.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "Numero di specifiche volume non valido.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "Specifica di volume non consistente.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[opzioni]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[TIPO]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "NOMEFILE [NOME]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "NOME [SINK]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "NOME|#N VOLUME"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "Volume"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "NOME|#N 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N FORMATI"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"I nomi speciali @DEFAULT_SINK@, @DEFAULT_SOURCE@ e @DEFAULT_MONITOR@\n"
"possono essere usati per specificare il sink, l'origine e il monitor "
"predefiniti.\n"

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help                            Mostra questo aiuto\n"
"      --version                         Mostra la versione\n"
"\n"
"  -s, --server=SERVER                   Il nome del server a cui "
"connettersi\n"
"  -n, --client-name=NOME                Il nome da dare a questo client sul "
"server\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Compilato con libpulse %s\n"
"Link eseguito con libpulse %s\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "Nome dello stream «%s» non valido"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "Specificare nulla o uno di: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Specificare un file campione da caricare"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Apertura del file audio non riuscita."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr ""
"Attenzione: determinazione della specifica di campionamento dal file non "
"riuscita."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "È necessario specificare un nome di campione da riprodurre"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "È necessario specificare un nome di campione da rimuovere"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "È necessario specificare un indice di ingresso per sink e un sink"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr ""
"È necessario specificare una indice di uscita per sorgente e una sorgente"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "È necessario specificare un nome di modulo e gli argomenti."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "È necessario specificare l'indice di un modulo o un nome"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Non è possibile specificare più di un sink. È necessario specificare un "
"valore booleano."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "Specifica di sospensione non valida."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Non è possibile specificare più di una sorgente. È necessario specificare un "
"valore booleano."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "È necessario specificare un nome/indice di scheda e un nome di profilo"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "È necessario specificare un nome/indice di sink e un nome di porta"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "È necessario specificare un nome di sink"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "È necessario specificare un nome/indice di sorgente e un nome di porta"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "È necessario specificare il nome di una sorgente"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
#, fuzzy
msgid "You have to specify a sink name/index"
msgstr "È necessario specificare un nome di sink"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "È necessario specificare un nome/indice di sink e un nome di porta"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
#, fuzzy
msgid "You have to specify a source name/index"
msgstr "È necessario specificare il nome di una sorgente"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "È necessario specificare un nome/indice di sorgente e un nome di porta"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "È necessario specificare un indice di ingresso per sink e un sink"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Indice dell'input del sink non valido"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr ""
"È necessario specificare un indice di uscita per la sorgente e il volume"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "Indice di uscita per la sorgente non valido"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"È necessario specificare un nome/indice di sink e un'azione per il muto (0, "
"1 o \"toggle\")"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "Specifica per il muto non valida"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr ""
"È necessario specificare un nome/indice di sorgente e un'azione per il muto "
"(0, 1 o \"toggle\")"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr ""
"È necessario specificare un indice d'ingresso per il sink e un'azione per il "
"muto (0, 1 o \"toggle\")"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Specifica dell'indice di input del sink non valida"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr ""
"È necessario specificare un indice di uscita per il sink e un'azione per il "
"muto (0, 1 o \"toggle\")"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "Specifica di indice di uscita per la sorgente non valida"

#: src/utils/pactl.c:3162
#, fuzzy
msgid "You have to specify at least an object path and a message name"
msgstr "È necessario specificare un nome/indice di sink e un nome di porta"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"È necessario specificare un indice di sink e un elenco separato da punti e "
"virgola di formati supportati"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr ""
"È necessario specificare un nome o un indice per la scheda, un nome per la "
"porta e un offset di latenza"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "Impossibile analizzare l'offset della latenza"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Nessun comando valido specificato."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Ripristino non riuscito: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Sospensione non riuscita: %s\n"

# cambiato un po' la parte finale...
#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "ATTENZIONE: server audio non locale, impossibile sospendere.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Connessione non riuscita: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "Ricevuto SIGINT, in uscita.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "ATTENZIONE: processo figlio terminato dal segnale %u\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [OPZIONI] -- PROGRAMMA [ARGOMENTI ...] \n"
"\n"
"Sospende temporaneamente PulseAudio mentre PROGRAMMA è in esecuzione.\n"
"\n"
"  -h, --help                            Mostra questo aiuto\n"
"      --version                         Mostra la versione\n"
"  -s, --server=SERVER                   Il nome del server a cui "
"connettersi\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"Compilato con libpulse %s\n"
"Link eseguito con libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() non riuscita.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() non riuscita.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() non riuscita.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D DISPLAY] [-S SERVER] [-O SINK] [-I SORGENTE] [-c FILE]  [-d|-e|-i|-"
"r]\n"
"\n"
" -d    Mostra i dati PulseAudio attuali collegati al display X11 (predef)\n"
" -e    Esporta i dati PulseAudio locali sul display X11\n"
" -i    Importa i dati PulseAudio dal display X11 alle variabili d'ambiente "
"e\n"
"        al file cookie locali \n"
" -r    Rimuove i dati PulseAudio dal display X11\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Analisi della riga di comando non riuscita.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Server: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Sorgente: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Sink: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Cookie: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Analisi dei dati cookie non riuscita\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Salvataggio dei dati cookie non riuscito\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Recupero del FQDN non riuscito.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Caricamento dei dati cookie non riuscito\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Non ancora implementato.\n"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "LFE su uscita mono separata"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Pass-through digitale (S/PDIF)"

#~ msgid "Digital Passthrough (IEC958)"
#~ msgstr "Pass-through digitale (IEC958)"
