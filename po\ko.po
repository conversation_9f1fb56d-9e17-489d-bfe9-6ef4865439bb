# <AUTHOR> <EMAIL>, 2013. #zanata
# <PERSON><PERSON><PERSON>g<PERSON>eon <<EMAIL>>, 2017.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018.
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2023-07-19 15:21+0000\n"
"Last-Translator: 김인수 <<EMAIL>>\n"
"Language-Team: Korean <https://translate.fedoraproject.org/projects/"
"pulseaudio/pulseaudio/ko/>\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.18.2\n"

#: src/daemon/cmdline.c:113
#, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [options]\n"
"\n"
"명령:\n"
"  -h, --help                            도움말을 표시\n"
"      --version                         버전을 표시\n"
"      --dump-conf                       기본 구성 덤프\n"
"      --dump-modules                    사용 가능한 모듈의 목록 덤프\n"
"      --dump-resample-methods           사용 가능한 재표본 방식으로 덤프\n"
"      --cleanup-shm                      오래된 공유 메모리 세그멘트 정리\n"
"      --start                           만약 실행되지 않았으면 데몬을 시작\n"
"  -k  --kill                            실행 중인 데몬을 제거\n"
"      --check                           동작 중인 데몬 점검 (종료 코드만 반"
"환)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   시스템-전반의 인스턴스로 실행\n"
"  -D, --daemonize[=BOOL]                시작 후에 데몬화\n"
"      --fail[=BOOL]                     시작이 실패 할 때에 종료\n"
"      --high-priority[=BOOL]            높은 수준으로 설정을 시도\n"
"                                        (root로만 사용 가능, SUID 또는\n"
"                                        높은 RLIMIT_NICE일 때에)\n"
"      --realtime[=BOOL]                 실시간 스케쥴링 활성 시도\n"
"                                        (root로만 사용 가능, SUID 또는\n"
"                                        높은 RLIMIT_RTPRIO일 때에)\n"
"      --disallow-module-loading[=BOOL]  시작 후 사용자가 요청 모듈을\n"
"                                        적재/적재하지 않는 부분을 허용하지 않"
"음\n"
"      --disallow-exit[=BOOL]            사용자가 요청한 종료를 허용하지 않"
"음\n"
"      --exit-idle-time=SECS             유휴 상태이고 시간이 경과하면\n"
"                                        데몬을 종료함\n"
"      --scache-idle-time=SECS           유휴 상태이고 시간이 경과하면\n"
"                                        자동 적재된 표본을 내려 놓음\n"
"      --log-level[=LEVEL]               자세한 표시 수준을 높이거나 설정\n"
"  -v  --verbose                         자세한 표시 수준을 높임\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        기록 대상을 지정\n"
"      --log-meta[=BOOL]                 로그 메시지에서 코드 위치를 포함\n"
"      --log-time[=BOOL]                 로그 메시지에서 시간표기를 포함\n"
"      --log-backtrace=FRAMES            로그 메시지에서 역추적을 포함\n"
"  -p, --dl-search-path=PATH             동적 공유 객체(플러그인)을 위한\n"
"                                        검색 경로를 설정\n"
"      --resample-method=METHOD          지정한 재표본 방식을 사용\n"
"                                        (사용 가능한 값은 --dump-resample-"
"methods 옵션을\n"
"                                        참조)\n"
"      --use-pid-file[=BOOL]             PID 파일을 생성\n"
"      --no-cpu-limit[=BOOL]             이를 지원하는 CPU 부하 제한기를\n"
"                                        설치하지 않음.\n"
"      --disable-shm[=BOOL]              공유 메모리 지원을 비활성화.\n"
"      --enable-memfd[=BOOL]             memfd 공유 메모리 지원을 활성화.\n"
"\n"
"스크립트 시작:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         지정된 인수와 함께 지정된 플러그"
"인\n"
"                                        모듈을 적재\n"
"  -F, --file=FILENAME                   지정 스크립트 실행\n"
"  -C                                    시작 후에 동작 중인 TTY에서\n"
"                                        명령 줄을 엽니다\n"
"\n"
"  -n                                    기본 스크립트 파일을 적재하지 않음\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:265
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level 에는 로그 수준 인자 값이 필요합니다 (0~4 숫자 범위 또는 오류, 경"
"고, 알림, 정보, 디버그 중 하나)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:328
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"잘못된 로그 대상: 'syslog', 'journal', 'stderr' 또는 'auto' 또는 유효한 파일 "
"이름 'file:<path>', 'newfile:<path>'를 사용하십시오."

#: src/daemon/cmdline.c:330
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"잘못된 로그 대상: 'syslog', 'stderr' 또는 'auto' 또는 유효한 파일 이름 'file:"
"<path>', 'newfile:<path>'를 사용하십시오."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "잘못된 리샘플링 방식 '%s'."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm 에는 부울 인자 값이 필요합니다"

#: src/daemon/cmdline.c:397
msgid "--enable-memfd expects boolean argument"
msgstr "--enable-memfd는 부울 인수가 필요합니다"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] 잘못된 로그 대상 '%s'."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] 잘못된 로그 레벨 '%s'."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] 잘못된 리샘플링 방법 '%s'."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] 잘못된 rlimit '%s'."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] 잘못된 샘플 형식 '%s'."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] 잘못된 샘플링 속도 '%s'."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] 잘못된 샘플 채널 '%s'."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] 잘못된 채널 맵 '%s'."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] 잘못된 fragment 수 '%s'."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] 잘못된 fragment 크기 '%s'."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] 잘못된 nice 레벨 '%s'."

#: src/daemon/daemon-conf.c:552
#, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] 잘못된 서버 종류 '%s'."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "설정 파일 열기 실패: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"지정 기본 채널 맵은 지정 기본 채널 수와는 다른 채널 수를 가지고 있습니다."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### 설정 파일에서 읽기: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "이름: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "사용 가능한 모듈 정보 없음\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "버전: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "설명: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "개발자: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "사용법: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "한 번 적재하기: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "지원 중지 경고: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "경로: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, c-format
msgid "Failed to open module %s: %s"
msgstr "모듈 열기 실패 '%s: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "기존 lt_dlopen 로더를 찾는데 실패했습니다."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "새 dl 로더 할당에 실패했습니다."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "bind-now-loader를 추가하는데 실패했습니다."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "'%s' 사용자를 찾을 수 없습니다."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "'%s' 그룹을 찾을 수 없습니다."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "'%s' 사용자의 GID와 '%s' 그룹 정보가 일치하지 않습니다."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "사용자 '%s'의 홈 디렉토리가 '%s' 아니면, 무시합니다."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "'%s' 생성 실패: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "그룹 리스트 변경 실패: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "GID 변경 실패: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "UID 변경 실패: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "시스템 전역 모드는 이 기술환경에서 지원하지 않습니다."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "명령어 행 분석 실패."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""
"비 루트 사용자의 시스템 모드 전환을 거부했습니다. D-Bus 서버 검색 서비스만 시"
"작합니다."

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "데몬 종료 실패: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"이 프로그램은 루트 계정으로 실행하도록 만들지 않았습니다. (실행하려면 --"
"system을 명기하십시오)."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "루트 권한이 필요합니다."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start는 시스템 인스턴스에 대해 지원되지 않습니다."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr "%s에서 사용자 설정한 서버, start/autospawn을 거부하고 있습니다."

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr "%s에 사용자가 설정한 서버, 이는 로컬에 있습니다. 상세히 조사합니다."

#: src/daemon/main.c:878
msgid "Running in system mode, but --disallow-exit not set."
msgstr ""
"시스템 모드에서 실행 중입니다. 하지만 --disallow-exit을 설정하지 않았습니다."

#: src/daemon/main.c:881
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr ""
"시스템 모드에서 실행 중입니다. 하지만 --disallow-module-loading을 설정하지 않"
"았습니다."

#: src/daemon/main.c:884
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "시스템 모드에서 실행 중입니다. 강제로 SHM 모드를 비활성화합니다."

#: src/daemon/main.c:889
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr "시스템 모드에서 실행 중입니다. 강제로 exit 유휴 시간을 비활성화합니다."

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "표준 입출력을 얻을 수 없습니다."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, c-format
msgid "pipe() failed: %s"
msgstr "pipe() 실패: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() 실패: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() 실패: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "데몬 시작에 실패했습니다."

#: src/daemon/main.c:987
#, c-format
msgid "setsid() failed: %s"
msgstr "setsid() 실패: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "머신 ID 가져오기 실패"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"PA가 시스템 모드로 동작하고 있습니다. 하지만 이것은 권장되지 않습니다. 만약 "
"의도대로 정상 동작하지 않더라도 그것은 당신의 잘못입니다.\n"
"시스템 모드가 좋지 않은 이유에 대해서는 다음 문서를 확인하시기 바랍니다. "
"http://www.freedesktop.org/wiki/Software/PulseAudio/Documentation/User/"
"WhatIsWrongWithSystemWide/."

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() 실패."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() 실패."

#: src/daemon/main.c:1268
msgid "command line arguments"
msgstr "명령 줄 인수"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""
"시작 명령을 실행 할 때에 오류로 인해 데몬을 초기화 할 수 없습니다. 명령 소"
"스: %s"

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "아무런 모듈 없이 데몬이 실행되었습니다. 동작하지 않습니다."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "펄스오디오 사운드 시스템"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "펄스오디오 사운드 시스템을 시작합니다"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "입력"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "도킹 스테이션 입력"

#: src/modules/alsa/alsa-mixer.c:2710
msgid "Docking Station Microphone"
msgstr "도킹 스테이션 마이크"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr "도킹스테이션 라인 입력"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "라인 입력"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "마이크"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
msgid "Front Microphone"
msgstr "전면 마이크"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
msgid "Rear Microphone"
msgstr "후면 마이크"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "외부 마이크"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "내부 마이크"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "라디오"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "비디오"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "자동 게인 컨트롤"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "자동 게인 컨트롤 없음"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "부스트"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "부스트 없음"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "증폭"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "증폭 없음"

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr "베이스 부스트"

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr "베이스 부스트 없음"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr "스피커"

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "헤드폰"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "아날로그 입력"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "도킹스테이션 마이크"

#: src/modules/alsa/alsa-mixer.c:2802
msgid "Headset Microphone"
msgstr "헤드셋 마이크"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "아날로그 출력"

#: src/modules/alsa/alsa-mixer.c:2808
msgid "Headphones 2"
msgstr "헤드폰 2"

#: src/modules/alsa/alsa-mixer.c:2809
msgid "Headphones Mono Output"
msgstr "헤드폰 모노 출력"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr "라인 출력"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "아날로그 모노 출력"

#: src/modules/alsa/alsa-mixer.c:2812
msgid "Speakers"
msgstr "스피커"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr "HDMI / DisplayPort"

#: src/modules/alsa/alsa-mixer.c:2814
msgid "Digital Output (S/PDIF)"
msgstr "디지털 출력 (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2815
msgid "Digital Input (S/PDIF)"
msgstr "디지털 입력 (S/PDIF)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr "다채널 입력"

#: src/modules/alsa/alsa-mixer.c:2817
msgid "Multichannel Output"
msgstr "다채널 출력"

#: src/modules/alsa/alsa-mixer.c:2818
msgid "Game Output"
msgstr "게임 출력"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
msgid "Chat Output"
msgstr "대화 출력"

#: src/modules/alsa/alsa-mixer.c:2821
msgid "Chat Input"
msgstr "대화 입력"

#: src/modules/alsa/alsa-mixer.c:2822
msgid "Virtual Surround 7.1"
msgstr "가상 서라운드 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "아날로그 모노"

#: src/modules/alsa/alsa-mixer.c:4564
msgid "Analog Mono (Left)"
msgstr "아날로그 모노 (왼쪽)"

#: src/modules/alsa/alsa-mixer.c:4565
msgid "Analog Mono (Right)"
msgstr "아날로그 모노 (오른쪽)"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "아날로그 스테레오"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "모노"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "스테레오"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr "헤드셋"

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
msgid "Speakerphone"
msgstr "스피커폰"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr "멀티채널"

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "아날로그 서라운드 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "아날로그 서라운드 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "아날로그 서라운드 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "아날로그 서라운드 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "아날로그 서라운드 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "아날로그 서라운드 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "아날로그 서라운드 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "아날로그 서라운드 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "아날로그 서라운드 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "아날로그 서라운드 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "아날로그 서라운드 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "디지털 스테레오 (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "디지털 서라운드 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "디지털 서라운드 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "디지털 서라운드 5.1 (IEC958/DTS)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "디지털 스테레오 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
msgid "Digital Surround 5.1 (HDMI)"
msgstr "디지털 서라운드 5.1 (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr "대화"

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr "게임"

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "아날로그 양방향 모노"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "아날로그 양방향 스테레오"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "아날로그 양방향 스테레오 (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr "멀티채널 듀플렉스"

#: src/modules/alsa/alsa-mixer.c:4738
msgid "Stereo Duplex"
msgstr "스테레오 듀플렉스"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr "모노 대화 + 7.1 서라운드"

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "끄기"

#: src/modules/alsa/alsa-mixer.c:4840
#, c-format
msgid "%s Output"
msgstr "%s 출력"

#: src/modules/alsa/alsa-mixer.c:4848
#, c-format
msgid "%s Input"
msgstr "%s 입력"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA가 장치에 새 데이터를 쓰도록 재촉했지만 쓸 수 있는 것이 없습니다!\n"
"이는 대부분 ALSA 드라이버 '%s'의 버그입니다. 이 문제를 ALSA 개발자에게 보고하"
"십시오.\n"
"POLLOUT 세트로 불러 오려했지만 결과적으로 snd_pcm_avail()이 0 또는 다른 값 < "
"min_avail을 반환했습니다."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA가 장치에 새 데이터를 읽도록 재촉했지만 읽을 수 있는 것이 없습니다!\n"
"이는 대부분 ALSA 드라이버 '%s'의 버그입니다. 이 문제를 ALSA 개발자에게 보고하"
"십시오.\n"
"POLLIN 세트로 불러오려했지만 결과적으로 snd_pcm_avail()이 0 또는 다른 값 < "
"min_avail을 반환했습니다."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail()이 %lu 바이트 (%lu ms)의 매우 큰 값을 반환했습니다.\n"
"ALSA 드라이버 '%s'의 오류일 수 있습니다. ALSA 개발자에게 이 문제를 보고해주시"
"기 바랍니다."

#: src/modules/alsa/alsa-util.c:1249
#, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay()가 %li 바이트 (%s%lu ms)의 매우 큰 값을 반환했습니다.\n"
"ALSA 드라이버 '%s'의 오류일 수 있습니다. ALSA 개발자에게 이 문제를 보고해주시"
"기 바랍니다."

#: src/modules/alsa/alsa-util.c:1296
#, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail_delay()가 이상한 값을 반환했습니다: 지연 시간 %lu은 사용 가능"
"한 시간 %lu 보다 작습니다.\n"
"ALSA 드라이버 '%s'의 오류일 수 있습니다. ALSA 개발자에게 이 문제를 보고해 주"
"시기 바랍니다."

#: src/modules/alsa/alsa-util.c:1339
#, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin()이 %lu 바이트 (%lu ms)의 매우 큰 값을 반환했습니다.\n"
"ALSA 드라이버 '%s'의 오류일 수 있습니다. ALSA 개발자에게 이 문제를 보고해 주"
"시기 바랍니다."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr "블루투스 출력"

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
msgid "Bluetooth Output"
msgstr "블루투스 출력"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr "핸즈프리"

#: src/modules/bluetooth/module-bluez5-device.c:1971
msgid "Headphone"
msgstr "헤드폰"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr "이동식"

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr "자동차"

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr "하이파이"

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr "전화기"

#: src/modules/bluetooth/module-bluez5-device.c:2042
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "Hi-Fi 재생 (A2DP Sink)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
msgid "High Fidelity Capture (A2DP Source)"
msgstr "Hi-Fi 캡쳐 (A2DP Source)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr "헤드셋 헤드 유닛 (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr "헤드셋 오디오 게이트웨어 (HSP)"

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr "핸즈프리 헤드 유닛 (HFP)"

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr "블루투스 핸즈프리 게이트웨이 (HFP)"

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"source_name=<소스의 이름> source_properties=<소스에 속성들을 지정> "
"source_master=<필터를 적용할 소스의 이름> sink_name=<싱크의 이름> "
"sink_properties=<싱크에 속성들을 지정> sink_master=<필터를 적용할 싱크의 이름"
"> adjust_time=<샘플 레이트 재조정을 몇 초 단위로 할 것인지 지정> "
"adjust_threshold=<드리프트가 몇 ms 이후부터 재조정을 할 것인지 지정> format=<"
"샘플 형식> rate=<샘플 레이트> channels=<채널 수> channel_map=<채널 맵> "
"aec_method=<사용 할 구현체> aec_args=<AEC 엔진의 인자들> save_aec=<AEC 데이터"
"를 /tmp 안에 저장> autoloaded=<이 모듈이 자동으로 로드된다면 설정하십시오> "
"use_volume_sharing=<yes 또는 no> "

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr "활성"

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "가짜 출력"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "빈 싱크를 포함하여 최소한 하나 이상의 싱크가 존재해야 합니다"

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "빈 싱크를 포함하여 최소한 하나 이상의 싱크가 존재해야 합니다"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr "일반적 목적의 이퀼라이저"

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<싱크의 이름> sink_properties=<싱크에 속성들을 지정> sink_master=<"
"연결할 싱크> format=<샘플 형식> rate=<샘플 레이트> channels=<채널 수> "
"channel_map=<채널 맵> autoloaded=<이 모듈이 자동으로 로드된다면 설정하십시오"
"> use_volume_sharing=<yes 또는 no> "

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr "고속퓨리에변환 기반 이퀄라이저 동작 %s"

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr "autoclean=<사용되지 않는 필터들을 자동으로 언로드>"

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "가상 LADSPA 싱크"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<싱크의 이름> sink_properties=<싱크에 속성들을 지정> master=<필터"
"를 적용할 싱크의 이름> sink_master=<필터를 적용할 싱크의 이름> format=<샘플 "
"형식> rate=<샘플 레이트> channels=<채널 수> channel_map=<입력 채널 맵> "
"plugin=<ladspa 플러그인 이름> label=<ladspa 플러그인 이름표> control=<쉼표로 "
"구분된 입력 제어값> input_ladspaport_map=<쉼표로 구분된 LADSPA 입력포트 이름"
"의 목록> output_ladspaport_map=<쉼표로 구분된 LADSPA 출력포트 이름의 목록> "
"autoloaded=<이 모듈이 자동으로 적재되면 설정하세요> "

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "클럭 사용 빈 싱크"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "빈 출력"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "포맷 설정 실패: 잘못된 포맷 문자열 %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "출력 장치"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "입력 장치"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "호스트 @HOSTNAME@의 오디오"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr "%s@%s 위한 터널"

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr "%s/%s가는 터널"

#: src/modules/module-virtual-surround-sink.c:50
msgid "Virtual surround sink"
msgstr "가상 서라운드 싱크"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<싱크의 이름> sink_properties=<싱크에 속성들을 지정> master=<필터"
"를 적용할 싱크의 이름> sink_master=<필터를 적용할 싱크의 이름> format=<샘플 "
"형식> rate=<샘플 레이트> channels=<채널 수> channel_map=<채널 맵> "
"use_volume_sharing=<yes 또는 no> force_flat_volume=<yes 또는 no> hrir=/path/"
"to/left_hrir.wav autoloaded=<이 모듈이 자동으로 로드된다면 설정하십시오> "

#: src/modules/raop/module-raop-discover.c:295
msgid "Unknown device model"
msgstr "알 수 없는 장치 모델"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr "RAOP 표준 프로파일"

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "펄스오디오 사운드 서버"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "전면 중앙"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "전면 왼쪽"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "전면 오른쪽"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "후면 중앙"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "후면 왼쪽"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "후면 오른쪽"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr "서브우퍼"

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "전면 중앙의 왼쪽"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "전면 중앙의 오른쪽"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "측면 왼쪽"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "측면 오른쪽"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "보조 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "보조 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "보조 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "보조 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "보조 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "보조 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "보조 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "보조 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "보조 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "보조 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "보조 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "보조 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "보조 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "보조 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "보조 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "보조 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "보조 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "보조 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "보조 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "보조 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "보조 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "보조 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "보조 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "보조 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "보조 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "보조 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "보조 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "보조 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "보조 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "보조 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "보조 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "보조 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "상단 중앙"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "상단 전면 중앙"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "상단 전면 왼쪽"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "상단 전면 오른쪽"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "상단 후면 중앙"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "상단 후면 왼쪽"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "상단 후면 오른쪽"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(잘못됨)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "서라운드 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "서라운드 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "서라운드 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "서라운드 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "서라운드 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
msgid "xcb_connect() failed"
msgstr "xcb_connect() 실패"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr "xcb_connection_has_error()가 true를 반환했습니다"

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "쿠키 데이터 분석 실패"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "알 수 없는 확장자 '%s'에 대해 전송된 메세지"

#: src/pulse/direction.c:37
msgid "input"
msgstr "입력"

#: src/pulse/direction.c:39
msgid "output"
msgstr "출력"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr "양방향"

#: src/pulse/direction.c:43
msgid "invalid"
msgstr "유효하지 않음"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""
"XDG_RUNTIME_DIR (%s)은 우리(uid %d)가 아니라 uid %d가 소유합니다! (자체 프로"
"토콜로 비 루트 펄스오디오 사용자가 루트 사용자 권한으로 연결할 때 이 문제가 "
"일어납니다. 그렇게 하지 마십시오.)"

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr "예"

#: src/pulsecore/core-util.h:97
msgid "no"
msgstr "아니요"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "autospawn 잠금에 액세스할 수 없습니다."

#: src/pulsecore/log.c:165
#, c-format
msgid "Failed to open target file '%s'."
msgstr "대상 파일 '%s 열기에 실패하였습니다."

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""
"대상 파일 '%s', '%s.1', '%s.2' ... '%s.%d'을 열기 시도하였으나, 실패하였습니"
"다."

#: src/pulsecore/log.c:651
msgid "Invalid log target."
msgstr "잘못된 기록 대상."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "내장 오디오"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "모뎀"

#: src/pulse/error.c:38
msgid "OK"
msgstr "확인"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "접근 거부됨"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "알 수 없는 명령"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "잘못된 인수"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "실재가 있음"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "이러한 엔티티가 없음"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "연결 거부됨"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "프로토콜 오류"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "제한 시간"

#: src/pulse/error.c:47
msgid "No authentication key"
msgstr "인증 키가 없음"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "내부 오류"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "연결 종료됨"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "엔티티가 종료됨"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "잘못된 서버"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "모듈 초기화 실패"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "잘못된 상태"

#: src/pulse/error.c:54
msgid "No data"
msgstr "자료 없음"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "호환되지 않는 프로토콜 버전"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "너무 큽니다"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "지원되지 않음"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "알 수 없는 오류 코드"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "이러한 확장자가 없음"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "사용하지 않는 기능"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "누락된 실행"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "클라이언트가 포크됨"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "입/출력 오류"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "장치 또는 자원이 사용중입니다"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "스트림 배출 실패: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "재생 스트림이 배출되었습니다."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "서버에 연결을 비우기."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_begin_write() 실패: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() 실패: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "스트림이 성공적으로 생성되었습니다."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() 실패: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "버퍼 지표: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "버퍼 지표: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "샘플 사양 '%s', 채널 맵 '%s' 사용."

#: src/utils/pacat.c:342
#, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "장치 %s에 연결되었습니다 (%u, %s중단되었습니다)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "스트림 오류: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "스트림 장치가 중단되었습니다.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "스트림 장치가 다시 시작되었습니다.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "스트림 언더런.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "스트림 오버런.%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "스트림 시작됨.%s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "스트림이 장치 %s 로 이동했습니다 (%u, %s중단되었습니다).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "없음 "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "스트림 버퍼 속성이 변경되었습니다.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr "Cork 요청 스택이 비어 있습니다: corking stream"

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr "Cork 요청 스택이 비어 있습니다: uncorking stream"

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr "경고: cork 요청 보다 uncork 요청을 더 많이 받았습니다."

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "연결되었습니다.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() 실패: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() 실패: %s"

#: src/utils/pacat.c:497
#, c-format
msgid "Failed to set monitor stream: %s"
msgstr "모니터 스트림 설정에 실패하였습니다: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() 실패: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "연결 실패: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF 받음."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() 실패: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() 실패: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "시그널 수신, 종료합니다."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "지연시간 얻기 실패: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "시간: %0.3f sec; 지연: %0.0f usec."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() 실패: %s"

#: src/utils/pacat.c:676
#, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            이 도움말을 표시합니다\n"
"      --version                         버전을 표시합니다\n"
"\n"
"  -r, --record                          기록을 위해 연결을 생성합니다\n"
"  -p, --playback                        재생을 위해 연결을 생성합니다\n"
"\n"
"  -v, --verbose                         자세한 작업 활성화합니다\n"
"\n"
"  -s, --server=SERVER                   연결하고자 하는 서버의 이름\n"
"  -d, --device=DEVICE                   연결하고자 하는 싱크/원천의 이름. 특"
"별한 이름 @DEFAULT_SINK@, @DEFAULT_SOURCE@과 @DEFAULT_MONITOR@는 각기 기본 싱"
"크, 원천과 관리를 지정하는데 사용 될 수 있습니다.\n"
"  -n, --client-name=NAME                서버에서 이와 같은 클라이언트를 호출"
"하는 방법\n"
"      --stream-name=NAME                서버에서 이와 같은 스트림을 호출하는 "
"방법\n"
"      --volume=VOLUME                   범위 0...65536에서 초기(선형) 소리를 "
"지정합니다\n"
"      --rate=SAMPLERATE                 샘플 속도(Hz) (기본값 44100)\n"
"      --format=SAMPLEFORMAT             샘플 형식, 가능한 값(기본값 s16ne)\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        참고하세요\n"
"      --channels=CHANNELS               채널의 수, 모노 1, 스테레오 2\n"
"                                        (기본값 2)\n"
"      --channel-map=CHANNELMAP          기본값 대신에 사용하려는 채널맵\n"
"      --fix-format                      스트림이 연결되고 있는 싱크/원천에"
"서\n"
"                                        샘플 형식을 가져옵니다.\n"
"      --fix-rate                        스트림이 연결되고 있는 싱크/원천에"
"서\n"
"                                        샘플 비율을 가져옵니다.\n"
"      --fix-channels                    스트림이 연결되고 있는 싱크/원천에"
"서\n"
"                                        채널 수와 채널맵을 가져옵니다.\n"
"      --no-remix                        채널을 언믹스하거나 다운믹스 하지 않"
"습니다.\n"
"      --no-remap                        이름 대신에 색인으로 채널을 맵핑합니"
"다.\n"
"      --latency=BYTES                   바이트 단위로 지정된 지연시간을 요청"
"합니다.\n"
"      --process-time=BYTES              바이트 단위로 요청당 지정된 처리시간"
"을 요청합니다.\n"
"      --latency-msec=MSEC               msec 단위로 지정된 지연시간을 요청합"
"니다.\n"
"      --process-time-msec=MSEC          msec 단위로 요청당 지정된 처리시간을 "
"요청합니다.\n"
"      --property=PROPERTY=VALUE         지정된 속성을 지정된 값으로 설정합니"
"다.\n"
"      --raw                             raw PCM 자료를 기록하거나/재생.\n"
"      --passthrough                     통과 자료.\n"
"      --file-format[=FFORMAT]           형식화된 PCM 자료 기록/재생.\n"
"      --list-file-formats               사용 가능한 파일 형식을 나열합니다.\n"
"      --monitor-stream=INDEX            색인 INDEX를 사용하여 싱크 입력에서 "
"기록합니다.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr "PulseAudio 소리 서버에서 부호화된 오디오 파일을 뒤로 재생합니다."

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr "PluseAudio 소리 서버에서 오디오 자료 순간찍기하고 파일에 이를 씁니다."

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""
"PulseAudio 소리 서버에서 오디오 자료 순간찍기와 STDOUT 또는 지정된 파일에 이"
"를 작성합니다."

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""
"PulseAudio 소리 서버에 STDIN 또는 지정된 파일에서 오디오 자료를 뒤로 재생합니"
"다."

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s로 컴파일됨\n"
"libpulse %s와 링크됨\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "잘못된 클라이언트 이름 '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "잘못된 스트림 이름 '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "잘못된 채널 맵 '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "잘못된 지연 사양 '%s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "잘못된 처리 시간 사양 '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "잘못된 속성 '%s'"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "알 수 없는 파일 포맷 %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr "--monitor-stream 을 위한 인수 구분 분석에 실패하였습니다"

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "잘못된 샘플 사양"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "인수가 너무 많습니다."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "파일의 샘플 사양 생성에 실패했습니다."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "오디오 파일을 열 수 없습니다."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr "경고: 지정된 샘플 사양은 파일에서의 사양을 덮어쓰기하게 됩니다."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "파일에서 샘플 사양 지정에 실패했습니다."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "경고: 채널 맵을 파일에서 확인할 수 없습니다."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "채널 맵은 샘플 사양과 일치하지 않습니다"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "경고: 채널 맵을 파일에 기록할 수 없습니다."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr "샘플 사양 '%s', 채널 맵 '%s'으로 %s 스트림을 엽니다."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "녹음"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "재생"

#: src/utils/pacat.c:1162
msgid "Failed to set media name."
msgstr "미디어 이름 설정에 실패했습니다."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() 실패."

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() 실패."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() 실패."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() 실패: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_rttime_new() 실패."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() 실패."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr "이름 [인수 ...]"

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr "이름|#N"

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr "이름"

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr "이름|#N 볼륨"

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr "#N 볼륨"

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr "이름|#N 1|0"

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr "#N 1|0"

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr "이름|#N 키=값"

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr "#N 키=값"

#: src/utils/pacmd.c:61
msgid "#N"
msgstr "#N"

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr "이름 싱크|#N"

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr "이름 파일이름"

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr "경로이름"

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr "파일 이름 싱크|#N"

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr "#N 싱크|소스"

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr "1|0"

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr "카드 프로파일"

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr "이름|#N 포트"

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr "카드-이름|카드-#N 포트 오프셋"

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr "대상"

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr "숫자 레벨"

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr "프레임"

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr "수신인 메시지 [ 메시지_매개변수]"

#: src/utils/pacmd.c:82
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"\n"
"  -h, --help 도움말 표시\n"
" --version 버전 표시\n"
"명령이 없을 경우 pacdm은 상호 대화식 모드에서 시작합니다\n"

#: src/utils/pacmd.c:129
#, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacmd %s\n"
"libpulse %s로 컴파일\n"
"libpulse %s로 연결\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr ""
"펄스오디오 데몬이 실행되고 있지 않거나, 세션 데몬으로 실행되고 있지 않습니다."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "소켓(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "펄스오디오 데몬 종료에 실패하였습니다."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "데몬이 응답하지 않습니다."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "통계 검색 실패: %s"

#: src/utils/pactl.c:199
#, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "현재 사용 중: %u 블록 포함 되어 있는 %s bytes 총계.\n"

#: src/utils/pactl.c:205
#, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "전체 수명 기간 동안 할당: %u 블록 포함 되어 있는 %s bytes 총계.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "샘플 캐쉬 크기: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "서버 정보 획득 실패: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, c-format
msgid "%s\n"
msgstr "%s\n"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""
"서버 이름: %s\n"
"라이브러리 프로토콜 버전: %u\n"
"서버 프로토콜 버전: %u\n"
"로컬 동작: %s\n"
"클라이언트 개요: %u\n"
"타일 크기: %zu\n"

#: src/utils/pactl.c:294
#, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"사용자 이름: %s\n"
"호스트 이름: %s\n"
"서버 이름: %s\n"
"서버 버전: %s\n"
"기본 샘플 사양: %s\n"
"기본 채널 매핑: %s\n"
"기본 싱크: %s\n"
"기본 소스: %s\n"
"쿠키: %04x:%04x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr "가용성을 알 수 없음"

#: src/utils/pactl.c:321
msgid "available"
msgstr "사용 가능"

#: src/utils/pactl.c:322
msgid "not available"
msgstr "사용할 수 없음"

#: src/utils/pactl.c:331 src/utils/pactl.c:355
msgid "Unknown"
msgstr "알 수 없음"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr "Aux"

#: src/utils/pactl.c:335
msgid "Line"
msgstr "라인 입력"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr "Mic"

#: src/utils/pactl.c:338
msgid "Handset"
msgstr "핸드셋"

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr "이어폰"

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr "SPDIF"

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr "HDMI"

#: src/utils/pactl.c:342
msgid "TV"
msgstr "텔레비전"

#: src/utils/pactl.c:345
msgid "USB"
msgstr "USB"

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr "블루투스"

#: src/utils/pactl.c:352
msgid "Network"
msgstr "네트워크"

#: src/utils/pactl.c:353
msgid "Analog"
msgstr "아날로그"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "싱크 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:664
#, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"싱크 #%u\n"
"\t상태: %s\n"
"\t이름: %s\n"
"\t설명: %s\n"
"\t드라이버: %s\n"
"\t샘플 사양: %s\n"
"\t채널 맵: %s\n"
"\t소유자 모듈: %u\n"
"\t무음: %s\n"
"\t볼륨: %s%s%s\n"
"\t균형 %0.2f\n"
"\t기본 볼륨: %s%s%s\n"
"\t모니터 소스: %s\n"
"\t지연시간: %0.0f usec, 설정 %0.0f usec\n"
"\t플래그: %s%s%s%s%s%s%s\n"
"\t속성:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\t포트:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr "\t\t%s: %s (유형: %s, 순위: %u%s%s, %s)\n"

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ", 가용성 그룹: "

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\t활성 포트: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, c-format
msgid "\tFormats:\n"
msgstr "\t형식:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "소스 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:849
#, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"소스 #%u\n"
"\t상태: %s\n"
"\t이름: %s\n"
"\t설명: %s\n"
"\t드라이버: %s\n"
"\t샘플 사양: %s\n"
"\t채널 맵: %s\n"
"\t소유자 모듈: %u\n"
"\t무음: %s\n"
"\t볼륨: %s%s%s\n"
"\t        균형 %0.2f\n"
"\t기본 볼륨: %s%s%s\n"
"\t싱크 모니터: %s\n"
"\t지연시간: %0.0f usec, 설정 %0.0f usec\n"
"\t플래그: %s%s%s%s%s%s\n"
"\t속성:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "해당 없음"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "모듈 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"모듈 #%u\n"
"\t이름: %s\n"
"\t인수: %s\n"
"\t사용자 카운터: %s\n"
"\t속성:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "클라이언트 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"클라이언트 #%u\n"
"\t드라이버: %s\n"
"\t소유자 모듈: %s\n"
"\t속성:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "카드 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"카드 #%u\n"
"\t이름: %s\n"
"\t드라이버: %s\n"
"\t소유자 모듈: %s\n"
"\t속성:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\t프로파일:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\t활성 프로파일: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""
"\t\t\t속성:\n"
"\t\t\t\t%s\n"

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr "\t\t\t프로파일 부분: %s"

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "싱크 입력 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:1366
#, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"싱크 입력 #%u\n"
"\t드라이버: %s\n"
"\t소유자 모듈: %s\n"
"\t클라이언트: %s\n"
"\t싱크: %u\n"
"\t샘플 사양: %s\n"
"\t채널 맵: %s\n"
"\t포맷: %s\n"
"\t코르크: %s\n"
"\t무음: %s\n"
"\t볼륨: %s\n"
"\t %s\n"
"\t균형 %0.2f\n"
"\t버퍼 지연 시간: %0.0f usec\n"
"\t싱크 지연 시간: %0.0f usec\n"
"\t리샘플링 방법: %s\n"
"\t속성:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "소스 출력 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:1489
#, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"소스 출력 #%u\n"
"\t드라이버: %s\n"
"\t소유자 모듈: %s\n"
"\t클라이언트: %s\n"
"\t소스: %u\n"
"\t샘플 사양: %s\n"
"\t채널 맵: %s\n"
"\t포맷: %s\n"
"\t코르크: %s\n"
"\t무음: %s\n"
"\t볼륨: %s\n"
"\t 균형 %0.2f\n"
"\t버퍼 지연 시간: %0.0f usec\n"
"\t소스 지연 시간: %0.0f usec\n"
"\t리샘플링 방법: %s\n"
"\t속성:\n"
"\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "샘플 정보를 가져올 수 없습니다: %s"

#: src/utils/pactl.c:1604
#, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"샘플 #%u\n"
"\t이름: %s\n"
"\t샘플 사양: %s\n"
"\t채널맵: %s\n"
"\t볼륨: %s\n"
"\t %s\n"
"\t균형 %0.2f\n"
"\t길이: %0.1fs\n"
"\t크기: %s\n"
"\t레이지: %s\n"
"\t파일 이름: %s\n"
"\t속성:\n"
"\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "오류: %s"

#: src/utils/pactl.c:1667
#, c-format
msgid "Send message failed: %s"
msgstr "메시지 보내기 실패: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr "목록-처리자 메시지에 실패하였습니다: %s"

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr "list-handlers 메시지 응답을 올바르게 구문 분석 할 수 없습니다"

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr "list-handlers 메시지 응답이 JSON 배열이 아닙니다"

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr "항목-처리자 메시지 응답 배열 요소 %d는 JSON 객체가 아닙니다"

#: src/utils/pactl.c:1800
#, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "모듈 비적재에 실패했습니다: 모듈 %s 불러오기 실패"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
"볼률 설정에 실패하였습니다: %d 채널을 위한 볼률 설정을 시도합니다, 그렇지만 "
"지원하는 채널 = %d 합니다\n"

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "샘플 업로드 실패: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "파일의 조기 종료"

#: src/utils/pactl.c:2144
msgid "new"
msgstr "새로운"

#: src/utils/pactl.c:2147
msgid "change"
msgstr "변경"

#: src/utils/pactl.c:2150
msgid "remove"
msgstr "제거"

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr "알 수 없음"

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "싱크"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "소스"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr "싱크-입력"

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr "소스-출력"

#: src/utils/pactl.c:2173
msgid "module"
msgstr "모듈"

#: src/utils/pactl.c:2176
msgid "client"
msgstr "클라이언트"

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr "샘플-캐쉬"

#: src/utils/pactl.c:2182
msgid "server"
msgstr "서버"

#: src/utils/pactl.c:2185
msgid "card"
msgstr "카드"

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr "이벤트 '%s'는 %s #%u 상에 있습니다.\n"

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT를 받았습니다. 종료합니다."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "잘못된 볼륨 사양"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr "볼륨이 허용 범위를 벗어납니다.\n"

#: src/utils/pactl.c:2594
msgid "Invalid number of volume specifications.\n"
msgstr "볼륨 사양의 수가 잘못되었습니다.\n"

#: src/utils/pactl.c:2606
msgid "Inconsistent volume specification.\n"
msgstr "볼륨 사양이 일치하지 않습니다.\n"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr "[옵션]"

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr "[유형]"

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr "파일이름 [NAME]"

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr "이름 [싱크]"

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr "이름|#N VOLUME [VOLUME ...]"

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr "#N VOLUME [볼륨 ...]"

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr "이름|#N 1|0|toggle"

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr "#N 1|0|toggle"

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr "#N 포맷"

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""
"\n"
"특별한 이름 @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@은\n"
"기본싱크, 소스 및 모니터를 지정하는데 사용 할 수 있습니다.\n"

#: src/utils/pactl.c:2664
#, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"\n"
"  -h, --help 도움말 표시\n"
" --version 버전 표시\n"
"\n"
"  -f, --format=FORMAT                   출력 형식. \"normal\" 또는 \"json\" "
"중의 하나\n"
" -s, --server=SERVER 연결할 서버 이름\n"
" -n, --client-name=NAME 서버에서 클라이언트 호출 방법\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"libpulse %s로 컴파일 됨\n"
"libpulse %s와 링크됨\n"

#: src/utils/pactl.c:2751
#, c-format
msgid "Invalid format value '%s'"
msgstr "잘못된 형식 값 '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr "아무것도 지정하지 않거나 다움 중 하나을 지정합니다: %s"

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "적재 할 샘플 파일을 지정하십시오"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "사운드 파일을 열 수 없습니다."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "경고: 파일에서 샘플 사양을 지정할 수 없습니다."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "재생할 샘플 이름을 지정해야 합니다"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "제거할 샘플 이름을 지정해야 합니다"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "싱크 입력 인덱스와 싱크를 지정해야 합니다"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "소스 출력 인덱스와 소스를 지정해야 합니다"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "모듈 이름과 인수를 지정해야 합니다."

#: src/utils/pactl.c:2889
msgid "You have to specify a module index or name"
msgstr "모듈 인덱스 또는 이름을 지정해야 합니다"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr "하나 이상의 싱크를 지정할 수 없습니다. 부울 값을 지정해야 합니다."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
msgid "Invalid suspend specification."
msgstr "일시 중지 사양이 잘못되었습니다."

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr "하나 이상의 소스를 지정할 수 없습니다. 부울 값을 지정해야 합니다."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "카드 이름/인덱스와 프로파일 이름을 지정해야 합니다"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "싱크 이름/인덱스와 포트 이름을 지정해야 합니다"

#: src/utils/pactl.c:2961
msgid "You have to specify a sink name"
msgstr "모듈 인덱스 또는 이름을 지정해야 합니다"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "소스 이름/인덱스와 포트 이름을 지정해야 합니다"

#: src/utils/pactl.c:2985
msgid "You have to specify a source name"
msgstr "모듈 인덱스 또는 이름을 지정해야 합니다"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
msgid "You have to specify a sink name/index"
msgstr "싱크 이름/색인을 지정해야 합니다"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "싱크 이름/인덱스와 볼륨을 지정해야 합니다"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
msgid "You have to specify a source name/index"
msgstr "원천 이름/색인을 지정해야 합니다"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "소스 이름/인덱스와 볼륨을 지정해야 합니다"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "싱크 입력 인덱스와 볼륨을 지정해야 합니다"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "잘못된 싱크 입력 인덱스"

#: src/utils/pactl.c:3060
msgid "You have to specify a source output index and a volume"
msgstr "소스 출력 인덱스와 볼륨을 지정해야 합니다"

#: src/utils/pactl.c:3065
msgid "Invalid source output index"
msgstr "잘못된 소스 출력 인덱스"

#: src/utils/pactl.c:3086
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "싱크 이름/인덱스와 무음 부울을 지정해야 합니다"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
msgid "Invalid mute specification"
msgstr "잘못된 무음 사양"

#: src/utils/pactl.c:3111
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "소스 이름/인덱스와 무음 부울을 지정해야 합니다 (0, 1, 또는 '토글')"

#: src/utils/pactl.c:3126
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr "싱크 입력 인덱스와 무음 부울을 지정해야 합니다. (0, 1, or '토글')"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "잘못된 싱크 입력 인덱스 사양"

#: src/utils/pactl.c:3144
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "소스 출력 인덱스와 무음 부울을 지정해야 합니다. (0, 1, or '토글')"

#: src/utils/pactl.c:3149
msgid "Invalid source output index specification"
msgstr "잘못된 소스 출력 인덱스 사양"

#: src/utils/pactl.c:3162
msgid "You have to specify at least an object path and a message name"
msgstr "최소한 객체 경로와 메시지 이름을 지정해야만 합니다"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""
"초과 인자 값을 부여하면 무시합니다. 모든 메시지 변수는 단일 문자열로 주어져"
"야 합니다."

#: src/utils/pactl.c:3182
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr ""
"싱크 인덱스 및 지원하는 형식의 쌍반점(;)으로 분리된 목록을 지정해야 합니다"

#: src/utils/pactl.c:3194
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "카드 이름/인덱스, 포트 이름 및 지연 오프셋을 지정해야 합니다"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr "지연 오프셋을 분석할 수 없습니다"

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "유효한 명령이 지정되어 있지 않습니다."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "다시 시작하기 실패: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "중지 실패: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "경고: 사운드 서버가 로컬에 있지 않으며 정지하지 않습니다.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "연결 실패: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT 받음, 종료 중.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "경고: 자식 프로세스가 시그널 %u에 의해 종료되었습니다.\n"

#: src/utils/pasuspender.c:228
#, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [옵션] -- 프로그램 -[ 인자 ...]\n"
"\n"
"프로그램이 동작 할 때에 일시적으로 PulseAudio를 중지합니다.\n"
"\n"
" -h, --help 도움말 표시\n"
" --version 버전 표시\n"
" -s, --server=SERVER 연결할 서버 이름\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"libpulse %s로 컴파일\n"
"libpulse %s로 연결\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() 실패.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() 실패.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() 실패.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    X11 디스플레이에 연결된 최신 PulseAudio 데이터 표시 (기본값)\n"
" -e    X11 디스플레이에 로컬 PulseAudio 데이터를 내보내기\n"
" -i    X11 디스플레이에서 로컬 환경 변수 및 쿠키 파일에 PulseAudio 데이터 가"
"져오기.\n"
" -r    X11 디스플레이에서 PulseAudio 데이터 삭제\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "명령행 분석 실패\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "서버: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "소스: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "싱크: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "쿠키: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "쿠키 데이터 분석 실패\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "쿠키 데이터 저장 실패\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "FQDN을 가져올 수 없습니다.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "쿠키 데이터 읽기 실패\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "아직 구현되지 않았습니다.\n"

#~ msgid ""
#~ "--log-level expects log level argument (either numeric in range 0..4 or "
#~ "one of debug, info, notice, warn, error)."
#~ msgstr ""
#~ "--log-level은 로그 수준 논의가 예상됩니다 (0..4 숫자 범위 내의 수 또는 디"
#~ "버그중의 하나, 정보, 알림, 경고, 오류)."

#~ msgid "Got signal %s."
#~ msgstr "시그널 %s를 받았습니다."

#~ msgid "Exiting."
#~ msgstr "종료합니다."

#~ msgid "Found user '%s' (UID %lu) and group '%s' (GID %lu)."
#~ msgstr "사용자 \"%s' (UID %lu)와 그룹 '%s' (GID %lu)를 찾았습니다."

#~ msgid "Successfully dropped root privileges."
#~ msgstr "root 권한을 올바르게 삭제했습니다."

#~ msgid "setrlimit(%s, (%u, %u)) failed: %s"
#~ msgstr "setrlimit(%s, (%u, %u)) 실패: %s"

#~ msgid "Daemon not running"
#~ msgstr "데몬이 실행중이지 않습니다."

#~ msgid "Daemon running as PID %u"
#~ msgstr "데몬이 %u PID로 실행중입니다."

#~ msgid "Daemon startup successful."
#~ msgstr "데몬이 성공적으로 시작되었습니다."

#~ msgid "This is PulseAudio %s"
#~ msgstr "펄스오디오 %s 입니다"

#~ msgid "Compilation host: %s"
#~ msgstr "컴파일 호스트: %s"

#~ msgid "Compilation CFLAGS: %s"
#~ msgstr "컴파일 CFLAGS: %s"

#~ msgid "Running on host: %s"
#~ msgstr "호스트에서 실행 중: %s"

#~ msgid "Found %u CPUs."
#~ msgstr "%u개의 CPU를 찾았습니다."

#~ msgid "Page size is %lu bytes"
#~ msgstr "페이지 크기 %lu 바이트"

#~ msgid "Compiled with Valgrind support: yes"
#~ msgstr "Valgrind 지원하도록 컴파일: 예"

#~ msgid "Compiled with Valgrind support: no"
#~ msgstr "Valgrind 지원하도록 컴파일: 아니요"

#~ msgid "Running in valgrind mode: %s"
#~ msgstr "Valgrind 모드로 실행중: %s"

#~ msgid "Running in VM: %s"
#~ msgstr "VM에서 실행 중: %s"

#~ msgid "Optimized build: yes"
#~ msgstr "빌드 최적화: 예"

#~ msgid "Optimized build: no"
#~ msgstr "빌드 최적화: 아니요"

#~ msgid "NDEBUG defined, all asserts disabled."
#~ msgstr "NDEBUG 정의되어, 모든 assert 비활성화됨."

#~ msgid "FASTPATH defined, only fast path asserts disabled."
#~ msgstr ""
#~ "FASTPATH는 정의되어 있습니다. 빠른 경로 assert만 비활성화되어 있습니다."

#~ msgid "All asserts enabled."
#~ msgstr "모든 assert 활성화됨."

#~ msgid "Machine ID is %s."
#~ msgstr "머신 ID는 %s입니다."

#~ msgid "Session ID is %s."
#~ msgstr "세션 ID는 %s입니다."

#~ msgid "Using runtime directory %s."
#~ msgstr "런타임 디렉토리 %s 사용 중"

#~ msgid "Using state directory %s."
#~ msgstr "상태 디렉토리 %s 사용 중"

#~ msgid "Using modules directory %s."
#~ msgstr "모듈 디렉토리 %s 사용 중 "

#~ msgid "Running in system mode: %s"
#~ msgstr "시스템 모드로 실행중: %s"

#~ msgid "Fresh high-resolution timers available! Bon appetit!"
#~ msgstr "최신 고해상도 타이머가 사용 가능합니다! 사용해 보십시오!"

#~ msgid ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"
#~ msgstr ""
#~ "커널이 좋지 않습니다! 고해상도 타이머가 활성화되어 있는 Linux를 추천합니"
#~ "다!"

#~ msgid "Failed to initialize daemon."
#~ msgstr "데몬 초기화 실패."

#~ msgid "Daemon startup complete."
#~ msgstr "데몬 구동이 완료되었습니다."

#~ msgid "Daemon shutdown initiated."
#~ msgstr "데몬 종료가 초기화되었습니다."

#~ msgid "Daemon terminated."
#~ msgstr "데몬이 종료되었습니다."

#~ msgid "Cleaning up privileges."
#~ msgstr "권한을 삭제하고 있습니다."

#~ msgid "PulseAudio Sound System KDE Routing Policy"
#~ msgstr "펄스오디오 사운드 시스템 KDE 라우팅 정책 "

#~ msgid "Start the PulseAudio Sound System with KDE Routing Policy"
#~ msgstr "KDE 라우팅 정책을 사용하여 펄스오디오 사운드 시스템 시작 "

#~ msgid "No cookie loaded. Attempting to connect without."
#~ msgstr "쿠키가 로딩되지 않았습니다. 없는 상태에서 연결을 시도하고 있습니다."

#~ msgid "Failed to load client configuration file.\n"
#~ msgstr "클라이언트 설정 파일 읽기 실패\n"

#~ msgid "Failed to read environment configuration data.\n"
#~ msgstr "환경 구성 데이터를 가져올 수 없습니다\n"

#~ msgid "Bluetooth High Quality (A2DP)"
#~ msgstr "Bluetooth 고품질 (A2DP)"

#~ msgid "Bluetooth Telephony (HSP/HFP)"
#~ msgstr "Bluetooth 전화 (HSP/HFP)"

#~ msgid "Telephony Duplex (HSP/HFP)"
#~ msgstr "양방향 전화통화 (HSP/HFP)"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "별도의 모노 출력 (LFE)"

#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "디지털 통과 (S/PDIF)"

#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "디지털 통과 (IEC958)"

#~ msgid ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            Show this help\n"
#~ "-v, --verbose                         Print debug messages\n"
#~ "      --from-rate=SAMPLERATE          From sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      From sample type (defaults to "
#~ "s16le)\n"
#~ "      --from-channels=CHANNELS        From number of channels (defaults "
#~ "to 1)\n"
#~ "      --to-rate=SAMPLERATE            To sample rate in Hz (defaults to "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        To sample type (defaults to s16le)\n"
#~ "      --to-channels=CHANNELS          To number of channels (defaults to "
#~ "1)\n"
#~ "      --resample-method=METHOD        Resample method (defaults to auto)\n"
#~ "      --seconds=SECONDS               From stream duration (defaults to "
#~ "60)\n"
#~ "\n"
#~ "If the formats are not specified, the test performs all formats "
#~ "combinations,\n"
#~ "back and forth.\n"
#~ "\n"
#~ "Sample type must be one of s16le, s16be, u8, float32le, float32be, ulaw, "
#~ "alaw,\n"
#~ "32le, s32be (defaults to s16ne)\n"
#~ "\n"
#~ "See --dump-resample-methods for possible values of resample methods.\n"
#~ msgstr ""
#~ "%s [options]\n"
#~ "\n"
#~ "-h, --help                            도움말 표시\n"
#~ "-v, --verbose                         디버그 메세지 표시\n"
#~ "      --from-rate=SAMPLERATE          변환 전 샘플 레이트(Hz) (기본값 "
#~ "44100)\n"
#~ "      --from-format=SAMPLEFORMAT      변환 전 샘플 유형 (기본값 s16le)\n"
#~ "      --from-channels=CHANNELS        변환 전 채널 수 (기본값 1)\n"
#~ "      --to-rate=SAMPLERATE            변환 후 샘플 레이트 (Hz) (기본값 "
#~ "44100)\n"
#~ "      --to-format=SAMPLEFORMAT        변환 후 샘플 유형 (기본값 s16le)\n"
#~ "      --to-channels=CHANNELS          변환 후 채널 수 (기본값 1)\n"
#~ "      --resample-method=METHOD        리샘플링 방법 (기본값 auto)\n"
#~ "      --seconds=SECONDS               변환 전 스트림 시간 (기본값 60)\n"
#~ "\n"
#~ "형식이 지정되지 않은 경우 모든 형식의 조합을\n"
#~ "테스트합니다.\n"
#~ "\n"
#~ "샘플 유형은 s16le, s16be, u8, float32le, float32be, ulaw, alaw, 32le, "
#~ "s32be에서\n"
#~ "하나를 선택합니다 (기본값 s16ne)\n"
#~ "\n"
#~ "리샘플링 방법에 사용 가능한 값은 --dump-resample-methods에서 참조하십시"
#~ "오.\n"

#~ msgid "=== %d seconds: %d Hz %d ch (%s) -> %d Hz %d ch (%s)"
#~ msgstr "=== %d 초: %d Hz %d ch (%s) -> %d Hz %d ch (%s)"
