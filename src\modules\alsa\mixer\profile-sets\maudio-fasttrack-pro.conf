# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; M-Audio FastTrack Pro
;
; This card has one duplex stereo channel called A and an additional
; stereo output channel called B.
;
; We knowingly only define a subset of the theoretically possible
; mapping combinations as profiles here.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-a-output]
description = Analog Stereo Channel A
device-strings = hw:%f,0,0
channel-map = left,right
direction = output

; Try both device 0 and device 1 for input, see
; http://mailman.alsa-project.org/pipermail/alsa-devel/2012-March/050701.html
[Mapping analog-stereo-a-input]
description = Analog Stereo Channel A
device-strings = hw:%f,0,0 hw:%f,1,0
channel-map = left,right
direction = input

[Mapping analog-stereo-b-output]
description = Analog Stereo Channel B
device-strings = hw:%f,1,0
channel-map = left,right
direction = output

[Profile output:analog-stereo-all+input:analog-stereo-all]
description = Analog Stereo Duplex Channel A, Analog Stereo output Channel B
output-mappings = analog-stereo-a-output  analog-stereo-b-output
input-mappings = analog-stereo-a-input
priority = 100
skip-probe = yes

[Profile output:analog-stereo-a-output+input:analog-stereo-a-input]
description = Analog Stereo Duplex Channel A
output-mappings = analog-stereo-a-output
input-mappings = analog-stereo-a-input
priority = 40
skip-probe = yes

[Profile output:analog-stereo-b+input:analog-stereo-b]
description = Analog Stereo Output Channel B
output-mappings = analog-stereo-b-output
input-mappings =
priority = 50
skip-probe = yes

[Profile output:analog-stereo-a]
description = Analog Stereo Output Channel A
output-mappings = analog-stereo-a-output
priority = 5
skip-probe = yes

[Profile output:analog-stereo-b]
description = Analog Stereo Output Channel B
output-mappings = analog-stereo-b-output
priority = 6
skip-probe = yes

[Profile input:analog-stereo-a]
description = Analog Stereo Input Channel A
input-mappings = analog-stereo-a-input
priority = 2
skip-probe = yes
