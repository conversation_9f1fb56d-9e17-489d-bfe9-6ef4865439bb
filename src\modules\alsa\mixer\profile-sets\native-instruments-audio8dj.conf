# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Native Instruments Audio 8 DJ
;
; This card has four stereo pairs of input and four stereo pairs of
; output, named channels A to D. Channel C has an additional Mic/Line
; connector, channel D an additional Headphone connector.
;
; We knowingly only define a subset of the theoretically possible
; mapping combinations as profiles here.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-a]
description = Analog Stereo Channel A
device-strings = hw:%f,0,0
channel-map = left,right

[Mapping analog-stereo-b]
description = Analog Stereo Channel B
device-strings = hw:%f,0,1
channel-map = left,right

# Since we want to set a different description for channel C's/D's input
# and output we define two separate mappings for them
[Mapping analog-stereo-c-output]
description = Analog Stereo Channel C
device-strings = hw:%f,0,2
channel-map = left,right
direction = output

[Mapping analog-stereo-c-input]
description = Analog Stereo Channel C (Line/Mic)
device-strings = hw:%f,0,2
channel-map = left,right
direction = input

[Mapping analog-stereo-d-output]
description = Analog Stereo Channel D (Headphones)
device-strings = hw:%f,0,3
channel-map = left,right
direction = output

[Mapping analog-stereo-d-input]
description = Analog Stereo Channel D
device-strings = hw:%f,0,3
channel-map = left,right
direction = input

[Profile output:analog-stereo-all+input:analog-stereo-all]
description = Analog Stereo Duplex Channels A, B, C (Line/Mic), D (Headphones)
output-mappings = analog-stereo-a analog-stereo-b analog-stereo-c-output analog-stereo-d-output
input-mappings = analog-stereo-a analog-stereo-b analog-stereo-c-input analog-stereo-d-input
priority = 100
skip-probe = yes

[Profile output:analog-stereo-d+input:analog-stereo-c]
description = Analog Stereo Channel D (Headphones) Output, Channel C (Line/Mic) Input
output-mappings = analog-stereo-d-output
input-mappings = analog-stereo-c-input
priority = 90
skip-probe = yes

[Profile output:analog-stereo-c-d+input:analog-stereo-c-d]
description = Analog Stereo Duplex Channels C (Line/Mic), D (Line/Mic)
output-mappings = analog-stereo-c-output analog-stereo-d-output
input-mappings = analog-stereo-c-input analog-stereo-d-input
priority = 80
skip-probe = yes

[Profile output:analog-stereo-a+input:analog-stereo-a]
description = Analog Stereo Duplex Channel A
output-mappings = analog-stereo-a
input-mappings = analog-stereo-a
priority = 50
skip-probe = yes

[Profile output:analog-stereo-b+input:analog-stereo-b]
description = Analog Stereo Duplex Channel B
output-mappings = analog-stereo-b
input-mappings = analog-stereo-b
priority = 40
skip-probe = yes

[Profile output:analog-stereo-c+input:analog-stereo-c]
description = Analog Stereo Duplex Channel C (Line/Mic)
output-mappings = analog-stereo-c-output
input-mappings = analog-stereo-c-input
priority = 60
skip-probe = yes

[Profile output:analog-stereo-d+input:analog-stereo-d]
description = Analog Stereo Duplex Channel D (Headphones)
output-mappings = analog-stereo-d-output
input-mappings = analog-stereo-d-input
priority = 70
skip-probe = yes

[Profile output:analog-stereo-a]
description = Analog Stereo Output Channel A
output-mappings = analog-stereo-a
priority = 6
skip-probe = yes

[Profile output:analog-stereo-b]
description = Analog Stereo Output Channel B
output-mappings = analog-stereo-b
priority = 5
skip-probe = yes

[Profile output:analog-stereo-c]
description = Analog Stereo Output Channel C
output-mappings = analog-stereo-c-output
priority = 7
skip-probe = yes

[Profile output:analog-stereo-d]
description = Analog Stereo Output Channel D (Headphones)
output-mappings = analog-stereo-d-output
priority = 8
skip-probe = yes

[Profile input:analog-stereo-a]
description = Analog Stereo Input Channel A
input-mappings = analog-stereo-a
priority = 2
skip-probe = yes

[Profile input:analog-stereo-b]
description = Analog Stereo Input Channel B
input-mappings = analog-stereo-b
priority = 1
skip-probe = yes

[Profile input:analog-stereo-c]
description = Analog Stereo Input Channel C (Line/Mic)
input-mappings = analog-stereo-c-input
priority = 4
skip-probe = yes

[Profile input:analog-stereo-d]
description = Analog Stereo Input Channel D
input-mappings = analog-stereo-d-input
priority = 3
skip-probe = yes
