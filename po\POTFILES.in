src/daemon/caps.c
src/daemon/cmdline.c
src/daemon/cpulimit.c
src/daemon/daemon-conf.c
src/daemon/dumpmodules.c
src/daemon/ltdl-bind-now.c
src/daemon/main.c
src/daemon/pulseaudio.desktop.in
src/daemon/systemd/user/pulseaudio.service.in
src/daemon/systemd/user/pulseaudio-x11.service.in
src/modules/alsa/alsa-mixer.c
src/modules/alsa/alsa-sink.c
src/modules/alsa/alsa-source.c
src/modules/alsa/alsa-util.c
src/modules/alsa/module-alsa-card.c
src/modules/alsa/module-alsa-sink.c
src/modules/alsa/module-alsa-source.c
src/modules/bluetooth/module-bluez5-device.c
src/modules/echo-cancel/module-echo-cancel.c
src/modules/jack/module-jack-sink.c
src/modules/jack/module-jack-source.c
src/modules/macosx/module-coreaudio-device.c
src/modules/module-allow-passthrough.c
src/modules/module-always-sink.c
src/modules/module-always-source.c
src/modules/module-cli.c
src/modules/module-combine.c
src/modules/module-console-kit.c
src/modules/module-default-device-restore.c
src/modules/module-detect.c
src/modules/module-device-restore.c
src/modules/module-equalizer-sink.c
src/modules/module-esound-compat-spawnfd.c
src/modules/module-esound-compat-spawnpid.c
src/modules/module-esound-sink.c
src/modules/module-filter-apply.c
src/modules/module-ladspa-sink.c
src/modules/module-lirc.c
src/modules/module-match.c
src/modules/module-mmkbd-evdev.c
src/modules/module-native-protocol-fd.c
src/modules/module-null-sink.c
src/modules/module-pipe-sink.c
src/modules/module-pipe-source.c
src/modules/module-position-event-sounds.c
src/modules/module-protocol-stub.c
src/modules/module-remap-sink.c
src/modules/module-rescue-streams.c
src/modules/module-rygel-media-server.c
src/modules/module-sine.c
src/modules/module-solaris.c
src/modules/module-stream-restore.c
src/modules/module-suspend-on-idle.c
src/modules/module-tunnel.c
src/modules/module-tunnel-sink-new.c
src/modules/module-tunnel-source-new.c
src/modules/module-virtual-surround-sink.c
src/modules/module-volume-restore.c
src/modules/module-zeroconf-discover.c
src/modules/module-zeroconf-publish.c
src/modules/oss/module-oss.c
src/modules/oss/oss-util.c
src/modules/raop/module-raop-discover.c
src/modules/raop/raop-sink.c
src/modules/reserve-wrap.c
src/modules/rtp/module-rtp-recv.c
src/modules/rtp/module-rtp-send.c
src/modules/rtp/rtp-common.c
src/modules/rtp/rtp-native.c
src/modules/rtp/rtp-gstreamer.c
src/modules/rtp/sap.c
src/modules/rtp/sdp.c
src/modules/x11/module-x11-bell.c
src/modules/x11/module-x11-publish.c
src/modules/x11/module-x11-xsmp.c
src/pulse/channelmap.c
src/pulse/client-conf.c
src/pulse/client-conf-x11.c
src/pulse/context.c
src/pulse/direction.c
src/pulsecore/asyncmsgq.c
src/pulsecore/asyncq.c
src/pulsecore/auth-cookie.c
src/pulsecore/authkey.c
src/pulsecore/avahi-wrap.c
src/pulsecore/cli.c
src/pulsecore/cli-command.c
src/pulsecore/client.c
src/pulsecore/cli-text.c
src/pulsecore/conf-parser.c
src/pulsecore/core.c
src/pulsecore/core-error.c
src/pulsecore/core-scache.c
src/pulsecore/core-subscribe.c
src/pulsecore/core-util.c
src/pulsecore/core-util.h
src/pulsecore/dbus-util.c
src/pulsecore/dynarray.c
src/pulsecore/fdsem.c
src/pulsecore/ffmpeg/resample2.c
src/pulsecore/flist.c
src/pulsecore/g711.c
src/pulsecore/hashmap.c
src/pulsecore/hook-list.c
src/pulsecore/i18n.c
src/pulsecore/idxset.c
src/pulsecore/iochannel.c
src/pulsecore/ioline.c
src/pulsecore/ipacl.c
src/pulsecore/lock-autospawn.c
src/pulsecore/log.c
src/pulsecore/ltdl-helper.c
src/pulsecore/mcalign.c
src/pulsecore/memblock.c
src/pulsecore/memblockq.c
src/pulsecore/memchunk.c
src/pulsecore/modargs.c
src/pulsecore/modinfo.c
src/pulsecore/module.c
src/pulsecore/msgobject.c
src/pulsecore/mutex-posix.c
src/pulsecore/mutex-win32.c
src/pulsecore/namereg.c
src/pulsecore/object.c
src/pulsecore/once.c
src/pulsecore/packet.c
src/pulsecore/parseaddr.c
src/pulsecore/pdispatch.c
src/pulsecore/pid.c
src/pulsecore/pipe.c
src/pulsecore/play-memblockq.c
src/pulsecore/play-memchunk.c
src/pulsecore/poll-posix.c
src/pulsecore/poll-win32.c
src/pulsecore/proplist-util.c
src/pulsecore/protocol-cli.c
src/pulsecore/protocol-esound.c
src/pulsecore/protocol-http.c
src/pulsecore/protocol-native.c
src/pulsecore/protocol-simple.c
src/pulsecore/pstream.c
src/pulsecore/pstream-util.c
src/pulsecore/queue.c
src/pulsecore/random.c
src/pulsecore/resampler.c
src/pulsecore/rtpoll.c
src/pulsecore/sample-util.c
src/pulsecore/sconv.c
src/pulsecore/sconv-s16be.c
src/pulsecore/sconv-s16le.c
src/pulsecore/semaphore-posix.c
src/pulsecore/semaphore-win32.c
src/pulsecore/shared.c
src/pulsecore/shm.c
src/pulsecore/sink.c
src/pulsecore/sink-input.c
src/pulsecore/sioman.c
src/pulsecore/socket-client.c
src/pulsecore/socket-server.c
src/pulsecore/socket-util.c
src/pulsecore/sound-file.c
src/pulsecore/sound-file-stream.c
src/pulsecore/source.c
src/pulsecore/source-output.c
src/pulsecore/start-child.c
src/pulsecore/strbuf.c
src/pulsecore/strlist.c
src/pulsecore/tagstruct.c
src/pulsecore/thread-mq.c
src/pulsecore/thread-posix.c
src/pulsecore/thread-win32.c
src/pulsecore/time-smoother.c
src/pulsecore/time-smoother_2.c
src/pulsecore/tokenizer.c
src/pulsecore/x11prop.c
src/pulsecore/x11wrap.c
src/pulse/error.c
src/pulse/ext-stream-restore.c
src/pulse/format.c
src/pulse/glib-mainloop.c
src/pulse/introspect.c
src/pulse/mainloop-api.c
src/pulse/mainloop.c
src/pulse/mainloop-signal.c
src/pulse/operation.c
src/pulse/proplist.c
src/pulse/sample.c
src/pulse/scache.c
src/pulse/simple.c
src/pulse/stream.c
src/pulse/subscribe.c
src/pulse/thread-mainloop.c
src/pulse/timeval.c
src/pulse/utf8.c
src/pulse/util.c
src/pulse/volume.c
src/pulse/xmalloc.c
src/tests/resampler-test.c
src/utils/pacat.c
src/utils/pacmd.c
src/utils/pactl.c
src/utils/padsp.c
src/utils/pasuspender.c
src/utils/pax11publish.c
