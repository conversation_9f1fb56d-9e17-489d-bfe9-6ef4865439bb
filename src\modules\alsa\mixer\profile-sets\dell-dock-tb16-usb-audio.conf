# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Dell Dock TB16 USB audio
;
; This card has two stereo pairs of output, One Mono input.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-headphone]
description = Headphone
device-strings = hw:%f,0,0
channel-map = left,right
direction = output

[Mapping analog-stereo-speaker]
description = Speaker
device-strings = hw:%f,1,0
channel-map = left,right
direction = output

[Mapping analog-stereo-mic]
description = Headset-Mic
device-strings = hw:%f,0,0
channel-map = left,right
direction = input


[Profile output:analog-stereo-speaker]
description = Speaker
output-mappings = analog-stereo-speaker
priority = 60
skip-probe = yes

[Profile output:analog-stereo-headphone+input:analog-stereo-mic]
description = Headset
output-mappings = analog-stereo-headphone
input-mappings = analog-stereo-mic
priority = 80
skip-probe = yes
