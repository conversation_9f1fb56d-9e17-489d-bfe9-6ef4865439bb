# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Texas Instruments PCM2902
;
; This is a generic chip used in multiple products, including at least
; Behringer U-Phoria UMC22, Behringer Xenyx 302USB, Intopic Jazz-UB700 and
; some unbranded "usb mini microphone".
;
; <PERSON><PERSON>inger UMC22 has stereo input (representing two physical mono inputs),
; others have mono input.
;
; Some devices have a mic input path, but at least Behringer Xenyx 302USB
; doesn't have any input mixer controls.
;
; Since the UMC22 card has only stereo input PCM device but is commonly used
; with mono mics, we define special mono mappings using "mono,aux1" and
; "aux1,mono" channel maps. If we had only had the standard stereo input
; mapping, the user would have to record stereo tracks with one channel silent,
; which would be inconvenient.
;
; This config also removes default digital input/output mappings that do
; not physically exist on cards that we've seen so far.
;
; Originally added by Nazar Mokrynskyi <<EMAIL>> for Behringer
; UMC22.

[General]
auto-profiles = yes

[Mapping analog-stereo-input]
device-strings = hw:%f
channel-map = left,right
paths-input = analog-input-mic analog-input
direction = input
priority = 4

[Mapping analog-mono]
device-strings = hw:%f
channel-map = mono
paths-input = analog-input-mic analog-input
direction = input
priority = 3

[Mapping analog-mono-left]
device-strings = hw:%f
channel-map = mono,aux1
paths-input = analog-input-mic analog-input
direction = input
priority = 2

[Mapping analog-mono-right]
device-strings = hw:%f
channel-map = aux1,mono
paths-input = analog-input-mic analog-input
direction = input
priority = 1

[Mapping analog-stereo-output]
device-strings = front:%f
channel-map = left,right
paths-output = analog-output
direction = output
