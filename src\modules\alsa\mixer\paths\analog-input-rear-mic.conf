# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; For devices where a 'Rear Mic' or 'Rear Mic Boost' element exists
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 82
description-key = analog-input-microphone-rear

[Jack Rear Mic]
required-any = any

[Jack Rear Mic - Input]
required-any = any

[Jack Rear Mic Phantom]
required-any = any
state.plugged = unknown
state.unplugged = unknown

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Rear Mic Boost]
required-any = any
switch = select
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Option Rear Mic Boost:on]
name = input-boost-on

[Option Rear Mic Boost:off]
name = input-boost-off

[Element Rear Mic]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Input Source]
enumeration = select

[Option Input Source:Rear Mic]
name = analog-input-microphone-rear
required-any = any

[Element Capture Source]
enumeration = select

[Option Capture Source:Rear Mic]
name = analog-input-microphone-rear
required-any = any

[Element Mic]
switch = off
volume = off

[Element Internal Mic]
switch = off
volume = off

[Element Front Mic]
switch = off
volume = off

[Element Dock Mic]
switch = off
volume = off

[Element Mic Boost]
switch = off
volume = off

[Element Dock Mic Boost]
switch = off
volume = off

[Element Internal Mic Boost]
switch = off
volume = off

[Element Front Mic Boost]
switch = off
volume = off

.include analog-input-mic.conf.common
