#!/bin/bash

# Test script for AirPods HFP fix
# This script tests the modifications made to module-bluez5-device.c

echo "=== AirPods HFP Fix Test Script ==="
echo

# Check if PulseAudio is running
if ! pgrep -x "pulseaudio" > /dev/null; then
    echo "Starting PulseAudio with debug logging..."
    export PULSE_LOG=4
    export PULSE_LOG_PRINT_TIME=1
    export PULSE_LOG_PRINT_META=1
    pulseaudio --kill 2>/dev/null
    pulseaudio --start --log-level=debug --log-meta --log-time &
    sleep 3
fi

echo "1. Checking current Bluetooth cards..."
pactl list cards short | grep bluez

echo
echo "2. Checking for AirPods devices..."
pactl list cards | grep -A 20 -B 5 -i airpods

echo
echo "3. Checking device properties for intended_roles..."
for card in $(pactl list cards short | grep bluez | cut -f1); do
    echo "--- Card $card ---"
    pactl list cards | sed -n "/Card #$card/,/Card #/p" | grep -E "(device.intended_roles|bluetooth.battery|Ports:|Active Profile)"
done

echo
echo "4. Testing profile switching (if AirPods found)..."
AIRPODS_CARD=$(pactl list cards short | grep -i airpods | cut -f1 | head -1)

if [ -n "$AIRPODS_CARD" ]; then
    echo "Found AirPods card: $AIRPODS_CARD"
    
    echo "Current profile:"
    pactl list cards | sed -n "/Card #$AIRPODS_CARD/,/Card #/p" | grep "Active Profile"
    
    echo "Available profiles:"
    pactl list cards | sed -n "/Card #$AIRPODS_CARD/,/Card #/p" | grep -A 10 "Profiles:"
    
    echo "Attempting to switch to handsfree_head_unit profile..."
    if pactl set-card-profile $AIRPODS_CARD handsfree_head_unit; then
        echo "✓ Profile switch successful!"
        sleep 2
        echo "New profile:"
        pactl list cards | sed -n "/Card #$AIRPODS_CARD/,/Card #/p" | grep "Active Profile"
    else
        echo "✗ Profile switch failed"
    fi
    
    echo "Switching back to a2dp_sink..."
    pactl set-card-profile $AIRPODS_CARD a2dp_sink
else
    echo "No AirPods device found. Please connect AirPods and run again."
fi

echo
echo "5. Checking PulseAudio logs for AirPods-related messages..."
echo "Recent log entries:"
journalctl --user -u pulseaudio --since "1 minute ago" | grep -i -E "(airpods|headset|handsfree|hfp)" | tail -10

echo
echo "=== Test Complete ==="
echo "If you see 'intended_roles = phone' and proper port names, the fix is working!"
