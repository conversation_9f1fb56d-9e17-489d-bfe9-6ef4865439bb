TAGS
*.lo
*.o
*.la
*.gcno
*.trs
*.log
.deps
.libs
/Makefile
/Makefile.in
client.conf
daemon.conf
default.pa
echo-cancel-test
esdcompat
gsettings-helper
org.freedesktop.pulseaudio.gschema.valid
pacat
pacmd
pactl
padsp
paplay
pasuspender
pax11publish
pulseaudio
pulseaudio.service
pulseaudio-x11.service
start-pulseaudio-x11
*-orc-gen.[ch]
# tests
alsa-mixer-path-test
alsa-time-test
asyncmsgq-test
asyncq-test
atomic-test
channelmap-test
close-test
connect-stress
core-util-test
cpulimit-test
cpulimit-test2
cpu-sconv-test
cpu-remap-test
cpu-mix-test
cpu-volume-test
extended-test
flist-test
format-test
get-binary-name-test
gtk-test
hashmap-test
hook-list-test
interpol-test
ipacl-test
json-test
lfe-filter-test
lock-autospawn-test
lo-latency-test
mainloop-test
mainloop-test-glib
mcalign-test
memblockq-test
memblock-test
mix-test
once-test
pacat-simple
parec-simple
passthrough-test
proplist-test
queue-test
remix-test
resampler-test
resampler-rewind-test
rtpoll-test
rtstutter
sig2str-test
sigbus-test
smoother-test
srbchannel-test
stripnul
strlist-test
sync-playback
system.pa
thread-mainloop-test
thread-test
usergroup-test
utf8-test
volume-test
mult-s16-test
