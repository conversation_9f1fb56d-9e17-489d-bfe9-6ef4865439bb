### v8, implemented by >= 0.8

First version supported.

### v9, implemented by >= 0.9.0

Reply for PA_COMMAND_CREATE_PLAYBACK_STREAM,
PA_COMMAND_CREATE_RECORD_STREAM now returns buffer_attrs that are used:

Four new fields in reply of PA_COMMAND_CREATE_PLAYBACK_STREAM:

   maxlength
   tlength
   prebuf
   minreq

Two new fields in reply of PA_COMMAND_CREATE_RECORD_STREAM:

   maxlength
   fragsize

### v10, implemented by >= 0.9.5

New opcodes:

 PA_COMMAND_MOVE_SINK_INPUT
 PA_COMMAND_MOVE_SOURCE_OUTPUT

SHM data transfer support

### v11, implemented by >= 0.9.7

Reply to PA_COMMAND_GET_SINK_INPUT_INFO, PA_COMMAND_GET_SINK_INPUT_INFO_LIST gets new field at the end:

 mute

New opcodes:

 PA_COMMAND_SET_SINK_INPUT_MUTE
 PA_COMMAND_SUSPEND_SINK
 PA_COMMAND_SUSPEND_SOURCE

### v12, implemented by >= 0.9.8

S32LE, S32BE is now known as sample spec.

Gained six new bool fields for PA_COMMAND_CREATE_PLAYBACK_STREAM, PA_COMMAND_CREATE_RECORD_STREAM request at the end:

 no_remap_channels
 no_remix_channels
 fix_format
 fix_rate
 fix_channels
 no_move
 variable_rate

Reply to these opcodes now includes:

 sample_spec
 channel_map
 device_index
 device_name
 suspended

New opcodes for changing buffer attrs:

 PA_COMMAND_SET_PLAYBACK_STREAM_BUFFER_ATTR
 PA_COMMAND_SET_RECORD_STREAM_BUFFER_ATTR

New opcodes for changing sampling rate:

 PA_COMMAND_UPDATE_PLAYBACK_STREAM_SAMPLE_RATE
 PA_COMMAND_UPDATE_RECORD_STREAM_SAMPLE_RATE

New opcodes for notifications:

 PA_COMMAND_PLAYBACK_STREAM_SUSPENDED
 PA_COMMAND_CAPTURE_STREAM_SUSPENDED
 PA_COMMAND_PLAYBACK_STREAM_MOVED
 PA_COMMAND_CAPTURE_STREAM_MOVED

### v13, implemented by >= 0.9.11

New fields for PA_COMMAND_CREATE_PLAYBACK_STREAM, PA_COMMAND_CREATE_RECORD_STREAM request at the end:

 peak_detect (bool)
 adjust_latency  (bool)

Replace field "name" for PA_COMMAND_CREATE_PLAYBACK_STREAM, PA_COMMAND_CREATE_RECORD_STREAM at the end:

 proplist

Replace field "name" for PA_COMMAND_SET_CLIENT_NAME request at the end:

 proplist

On response of PA_COMMAND_SET_CLIENT_NAME:

 client_index

New proplist field for sink, source, sink input, source output introspection opcodes and at the end:

 proplist

New opcodes for proplist modifications

  PA_COMMAND_UPDATE_RECORD_STREAM_PROPLIST
  PA_COMMAND_UPDATE_PLAYBACK_STREAM_PROPLIST
  PA_COMMAND_UPDATE_CLIENT_PROPLIST
  PA_COMMAND_REMOVE_RECORD_STREAM_PROPLIST
  PA_COMMAND_REMOVE_PLAYBACK_STREAM_PROPLIST
  PA_COMMAND_REMOVE_CLIENT_PROPLIST

New field for PA_COMMAND_PLAY_SAMPLE:

  proplist

New field for PA_COMMAND_PLAY_SAMPLE response:

  idx

New field for PA_COMMAND_CREATE_PLAYBACK_STREAM at the end:

  start_muted

Buffer attributes for PA_COMMAND_CREATE_PLAYBACK_STREAM and
PA_COMMAND_CREATE_RECORD_STREAM may now be 0 for default values.

New field for PA_COMMAND_SET_PLAYBACK_STREAM_BUFFER_ATTR,
PA_COMMAND_SET_RECORD_STREAM_BUFFER_ATTR at the end:

  adjust_latency (bool)

new message:

  PA_COMMAND_STARTED

### v14, implemented by >= 0.9.12

new message:

  PA_COMMAND_EXTENSION

PA_COMMAND_CREATE_PLAYBACK_STREAM:

  bool volume_set at the end

PA_COMMAND_CREATE_RECORD_STREAM, PA_COMMAND_CREATE_PLAYBACK_STREAM:

  bool early_requests at the end

New field for PA_COMMAND_SET_PLAYBACK_STREAM_BUFFER_ATTR,
PA_COMMAND_SET_RECORD_STREAM_BUFFER_ATTR at the end:

  early_requests (bool)

### v15, implemented by >= 0.9.15

PA_COMMAND_CREATE_PLAYBACK_STREAM

  bool muted at the end

PA_COMMAND_CREATE_PLAYBACK_STREAM, PA_COMMAND_CREATE_RECORD_STREAM:

  bool dont_inhibit_auto_suspend at the end

PA_COMMAND_GET_MODULE_INFO_LIST

  remove bool auto_unload
  add proplist at the end

new messages:

  PA_COMMAND_GET_CARD_INFO
  PA_COMMAND_GET_CARD_INFO_LIST
  PA_COMMAND_SET_CARD_PROFILE

  PA_COMMAND_CLIENT_EVENT
  PA_COMMAND_PLAYBACK_STREAM_EVENT
  PA_COMMAND_RECORD_STREAM_EVENT

  PA_COMMAND_PLAYBACK_BUFFER_ATTR_CHANGED
  PA_COMMAND_RECORD_BUFFER_ATTR_CHANGED

### v16, implemented by >= 0.9.15

new messages:

  PA_COMMAND_SET_SINK_PORT
  PA_COMMAND_SET_SOURCE_PORT

## v17, implemented by >= 0.9.20

new flag at end of CREATE_PLAYBACK_STREAM:

    bool relative_volume

## v18, implemented by >= 0.9.22

new flag at end of CREATE_PLAYBACK_STREAM:

    bool passthrough

## v19, implemented by >= 0.9.22

New flag at the end of sink input and source output introspection data:

    bool corked

## v20, implemented by >= 1.0

Two new flags at the end of sink input introspection data:

    bool has_volume
    bool volume_writable

## v21, implemented by >= 1.0

Changes for format negotiation in the extended API.

New fields PA_COMMAND_CREATE_PLAYBACK_STREAM:

    uint8_t n_formats
    format_info format1
    ...
    format_info formatn

One new field in reply from PA_COMMAND_CREATE_PLAYBACK_STREAM:

    format_info format

New fields in reply from PA_COMMAND_GET_SINK_INFO (and thus
PA_COMMAND_GET_SINK_INFO_LIST)

    uint8_t n_formats
    format_info format1
    ...
    format_info formatn

One new field in reply from PA_COMMAND_GET_SINK_INPUT_INFO (and thus
PA_COMMAND_GET_SINK_INPUT_INFO_LIST)

    format_info format

## v22, implemented by >= 1.0

New fields PA_COMMAND_CREATE_RECORD_STREAM:

    uint8_t n_formats
    format_info format1
    ...
    format_info formatn
    volume
    bool muted
    bool volume_set
    bool muted_set
    bool relative_volume
    bool passthrough

One new field in reply from PA_COMMAND_CREATE_RECORD_STREAM:

    format_info format

New fields in reply from PA_COMMAND_GET_SOURCE_INFO (and thus
PA_COMMAND_GET_SOURCE_INFO_LIST)

    uint8_t n_formats
    format_info format1
    ...
    format_info formatn

Five new fields in reply from PA_COMMAND_GET_SOURCE_OUTPUT_INFO (and thus
PA_COMMAND_GET_SOURCE_OUTPUT_INFO_LIST)

    volume
    bool mute
    bool has_volume
    bool volume_writable
    format_info format

## v23, implemented by >= 1.0

New field in PA_COMMAND_UNDERFLOW:

    int64_t index

## v24, implemented by >= 2.0

New field in all commands that send/receive port introspection data
(PA_COMMAND_GET_(SOURCE|SINK)_INFO,
PA_COMMAND_GET_(SOURCE|SINK)_INFO_LIST):

    uint32_t available

The field is added once for every port.

## v25, implemented by >= 2.0

When port availability changes, send a subscription event for the
owning card.

## v26, implemented by >= 2.0

In reply from PA_COMMAND_GET_CARD_INFO (and thus
PA_COMMAND_GET_CARD_INFO_LIST), the following is added:

    uint32_t n_ports

...followed by n_ports extended port entries, which look like this:

    string name
    string description
    uint32_t priority
    uint32_t available
    uint8_t direction
    proplist
    uint32_t n_profiles
    string profile_name_1
    ...
    string profile_name_n

Profile names must match earlier sent profile names for the same card.

## v27, implemented by >= 3.0

New opcodes:
    PA_COMMAND_SET_PORT_LATENCY_OFFSET

New field in the card commands that send/receive port introspection data
PA_COMMAND_GET_CARD_INFO(_LIST)):

    int64_t latency_offset

The field is added once for every port.

## v28, implemented by >= 4.0

New value for encoding format type in format_info
PA_COMMAND_CREATE_(PLAYBACK|RECORDING)_STREAM and its reply,
In reply from PA_COMMAND_GET_(SOURCE|SOURCE_OUTPUT|SINK|SINK_INPUT)_INFO[_LIST],
SUBCOMMAND_SAVE_FORMATS, in reply from SUBCOMMAND_READ_FORMATS[_ALL]

    (uint8_t ) PA_ENCODING_MPEG2_AAC_IEC61937 := 6

## v29, implemented by >= 5.0
#
New field in all commands that send/receive profile introspection data
(PA_COMMAND_GET_CARD_INFO)

    uint32 available

The field is added once for every profile.

## v30, implemented by >= 6.0
#
A new protocol mechanism supported: Two ringbuffers in shared memory.
Pulseaudio fdsem (wrappers around event file descriptors) are used for
signalling new data.
The protocol has a new SHM flag telling whether a SHM memblock is writable
by both sides.

PA_COMMAND_ENABLE_SRBCHANNEL
First sent from server to client, tells the client to start listening on
the additional SHM ringbuffer channel.
This command also has ancillary data (two eventfds attached to it).
Must be directly followed by a memblock which is the ringbuffer memory.
When memblock is received by the client, it acks by sending
PA_COMMAND_ENABLE_SRBCHANNEL back (without ancillary or memblock data).

PA_COMMAND_DISABLE_SRBCHANNEL
Tells the client to stop listening on the additional SHM ringbuffer channel.
Acked by client by sending PA_COMMAND_DISABLE_SRBCHANNEL back.

## v31, implemented by >= 9.0

Memfd shared-memory support is now added to PulseAudio as an opt-in feature.
Add 'enable-memfd=yes' to daemon's configuration to use memfds, instead of
POSIX shm, by default.

Memfd is a simple memory sharing mechanism, added by the systemd/kdbus
developers, to share pages between processes in an anonymous, no global
registry needed, no mount-point required, relatively secure, manner.

PulseAudio memfd support builds the necessary (but not yet sufficient)
groundwork for a better integration with per-app containers (e.g. xdg-app)

For further details on memfds in general, please check:

  https://dvdhrm.wordpress.com/2014/06/10/memfd_create2/
  Archived at: http://www.webcitation.org/6gnHTy9Kr

Moreover, for both client and server, the second most-significant bit of
the version tag is now used to flag memfd SHM support. On the way forward,
the two most-significant _bytes_ of the version tag are now also reserved
for flags.

PA_COMMAND_REGISTER_MEMFD_SHMID
New command that can be sent both ways, from client to server and vice versa.
This is needed to transfer a memfd pool's blocks without passing its fd every
time, thus minimizing overhead and avoiding fd leaks.

The registration command above sends a packet with the pool's memfd fd as
ancillary data. Such packet has an ID that uniquely identifies the pool's
memfd memory area. Upon arrival, the other end (client or server) creates a
permanent ID<->memfd mapping.

By doing so, there's need to reference the pool's memfd file descriptor any
further -- just its ID. Thus both endpoints can then quickly and safely
close their memfd file descriptors.

## v32, implemented by >= 10.0

Enable memfd transport by default.

This protocol bump was only created to mark 9.0 clients. Although they
support memfd transport, such older clients has an iochannel bug that would
break memfd audio if they're run in 32-bit mode over a 64-bit kernel. Thus
influence these buggy libraries to use POSIX shared memory, by signalling
memfd support only to 10.0+ clients.

Check commit 451d1d676237c81 for further details.

## v33, implemented by >= 13.0

Added two values to the pa_encoding_t enum:

    PA_ENCODING_TRUEHD_IEC61937 := 7
    PA_ENCODING_DTSHD_IEC61937 := 8

## v34, implemented by >= 14.0

New fields in the port introspection data (duplicated for all port types:
sink, source and card ports):

    string availability_group
    uint32 type

## v35, implemented by >= 15.0

Added new command for communication with objects.

PA_COMMAND_SEND_OBJECT_MESSAGE:
sends a message to an object identified by an object path

parameters:
    string object_path - unique path identifying the object
    string message - message name
    string message_parameters - additional parameters if required (may be
                                NULL, which should be treated the same as an
                                empty string)

The command returns a string, which may be empty or NULL (NULL should be
treated the same as an empty string).

#### If you just changed the protocol, read this
## module-tunnel depends on the sink/source/sink-input/source-input protocol
## internals, so if you changed these, you might have broken module-tunnel.
## Don't forget to test module-tunnel-{source,sink} when pushing protocol
## changes.
