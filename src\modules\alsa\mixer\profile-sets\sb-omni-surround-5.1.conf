# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Creative Sound Blaster Omni Surround 5.1
;
; This config supports Linux 4.3-rc1+.
; By default there are some non-existing (physically) inputs and outputs that
; are not present in this config.
; Also in addition to natively supported modes (such as stereo, 5.1 and stereo
; S/PDIF) following useful output modes are added: 2.1, 4.0, 4.1 and 5.0.
;
; NOTE: in 2.1 and 4.1 physical LFE output will be different than in 5.1 mode.
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-stereo-input]
device-strings = hw:%f
channel-map = left,right
paths-input = analog-input-mic analog-input-linein
direction = input

[Mapping analog-stereo-output]
device-strings = front:%f
channel-map = left,right
paths-output = analog-output
direction = output

[Mapping analog-surround-21]
device-strings = surround51:%f
channel-map = front-left,front-right,aux1,aux2,aux3,lfe
paths-output = analog-output
direction = output

[Mapping analog-surround-40]
device-strings = surround51:%f
channel-map = front-left,front-right,rear-left,rear-right
paths-output = analog-output
direction = output

[Mapping analog-surround-41]
device-strings = surround51:%f
channel-map = front-left,front-right,rear-left,rear-right,aux1,lfe
paths-output = analog-output
direction = output

[Mapping analog-surround-50]
device-strings = surround51:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center
paths-output = analog-output
direction = output

[Mapping analog-surround-51]
device-strings = surround51:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
paths-output = analog-output
direction = output

[Mapping iec958-stereo]
device-strings = iec958:%f
channel-map = left,right
paths-output = iec958-stereo-output
direction = output

[Profile output:analog-stereo-output+input:analog-stereo-input]
output-mappings = analog-stereo-output
input-mappings = analog-stereo-input
priority = 7

[Profile output:analog-surround-21+input:analog-stereo-input]
output-mappings = analog-surround-21
input-mappings = analog-stereo-input
priority = 6

[Profile output:analog-surround-40+input:analog-stereo-input]
output-mappings = analog-surround-40
input-mappings = analog-stereo-input
priority = 5

[Profile output:analog-surround-41+input:analog-stereo-input]
output-mappings = analog-surround-41
input-mappings = analog-stereo-input
priority = 4

[Profile output:analog-surround-50+input:analog-stereo-input]
output-mappings = analog-surround-50
input-mappings = analog-stereo-input
priority = 3

[Profile output:analog-surround-51+input:analog-stereo-input]
output-mappings = analog-surround-51
input-mappings = analog-stereo-input
priority = 2

[Profile output:iec958-stereo+input:analog-stereo-input]
output-mappings = iec958-stereo
input-mappings = analog-stereo-input
priority = 1
