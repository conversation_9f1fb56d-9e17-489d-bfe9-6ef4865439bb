# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; USB Gaming DAC.
; These devices have two output devices. The first one is mono, meant for
; voice audio, and the second one is 7.1 surround, meant for everything
; else. The 7.1 surround is mapped to headphones within the device.
; The purpose of the mono/7.1 design is to provide separate volume
; controls for voice and other audio, which can be useful in gaming.
;
; Works with:
; Sennheiser GSX 1000
; Sennheiser GSX 1200
;
; See default.conf for an explanation on the directives used here.

[General]
auto-profiles = no

[Mapping analog-chat-output]
device-strings = hw:%f,0
channel-map = mono
paths-output = analog-chat-output
direction = output
priority = 4000
intended-roles = phone

[Mapping analog-output-surround71]
device-strings = hw:%f,1
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
paths-output = virtual-surround-7.1
priority = 4100
direction = output

[Mapping analog-chat-input]
device-strings = hw:%f,0
channel-map = mono
paths-input = analog-chat-input
priority = 4100
direction = input

[Profile output:analog-output-surround71+output:analog-output-chat+input:analog-input]
output-mappings = analog-output-surround71 analog-chat-output
input-mappings = analog-chat-input
priority = 5100
skip-probe = yes
