#!/bin/sh -e
[ -z "$DEBUG" ] || set -x
echo "########## pulseaudio-17.0: deploy ##########"
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot
cd /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/target
/home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/host/bin/tar --no-recursion --ignore-failed-read -cf /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/pulseaudio-17.0.tar -T /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/.files-list-target.txt
adb shell true >/dev/null
adb push /home/<USER>/samba/rk3562_linux_release_v1.2.0_20240620/buildroot/output/rockchip_rk3562/build/pulseaudio-17.0/pulseaudio-17.0.tar /tmp/
adb shell tar xvf /tmp/pulseaudio-17.0.tar
