# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Intended for usage on boards that have a separate Mono output plug.
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 50

[Element Hardware Master]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Master]
switch = off
volume = off

[Element Master Mono]
required = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

; This profile path is intended to control the speaker, not the
; headphones. But it should not hurt if we leave the headphone jack
; enabled nonetheless.
[Element Headphone]
switch = mute
volume = zero

[Element Headphone,1]
switch = mute
volume = zero

[Element Headphone+LO]
switch = mute
volume = zero

[Element Headphone2]
switch = mute
volume = zero

[Element Speaker]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Speaker+LO]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Desktop Speaker]
switch = off
volume = off

[Element Front]
switch = off
volume = off

[Element Rear]
switch = off
volume = off

[Element Surround]
switch = off
volume = off

[Element Side]
switch = off
volume = off

[Element Center]
switch = off
volume = off

[Element LFE]
switch = off
volume = off

.include analog-output.conf.common
