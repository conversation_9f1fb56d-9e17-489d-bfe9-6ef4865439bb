#!/bin/sh

# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published by
# the Free Software Foundation; either version 2 of the License, or
# (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

set -e

if [ -n "$1" ] ; then
    case $1 in
    stop)
      @PACTL_BINARY@ unload-module module-x11-publish > /dev/null
      @PACTL_BINARY@ unload-module module-x11-cork-request > /dev/null
      @PACTL_BINARY@ unload-module module-device-manager > /dev/null
      @PACTL_BINARY@ unload-module module-x11-xsmp > /dev/null
      exit 0
      ;;
    start)
      # Let it continue further down
      ;;
    *)
      echo "Unknown argument $1"
      exit 1
      ;;
    esac
fi

if [ x"$DISPLAY" != x ] ; then

    @PACTL_BINARY@ load-module module-x11-publish "display=$DISPLAY xauthority=$XAUTHORITY" > /dev/null
    @PACTL_BINARY@ load-module module-x11-cork-request "display=$DISPLAY xauthority=$XAUTHORITY" > /dev/null

    # KDE plasma versions older than 5.17.0 use module-device-manager's routing API.
    # Check for current plasma version and load module if it's necessary.
    if [ x"$KDE_FULL_SESSION" = x"true" ]; then
        plasmaversion="$(plasmashell -v 2>/dev/null | sed -n 's/^plasmashell \([0-9][0-9]*\)\.\([0-9][0-9]*\)\.\([0-9][0-9]*\)/\1*1000000+\2*1000+\3/p' | head -1)"
        if [ -n "$plasmaversion" ] && [ "$(($plasmaversion))" -lt "5017000" ]; then
            @PACTL_BINARY@ load-module module-device-manager "do_routing=1" > /dev/null
        fi
    fi

    if [ x"$SESSION_MANAGER" != x ] ; then
	@PACTL_BINARY@ load-module module-x11-xsmp "display=$DISPLAY xauthority=$XAUTHORITY session_manager=$SESSION_MANAGER" > /dev/null
    fi
fi
