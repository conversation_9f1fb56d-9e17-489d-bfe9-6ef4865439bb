# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; For devices where a 'Dock Mic' or 'Dock Mic Boost' element exists
;
; See analog-output.conf.common for an explanation on the directives

[General]
priority = 78
description-key = analog-input-microphone-dock

[Jack Dock Mic]
required-any = any

[Jack Dock Mic Phantom]
state.plugged = unknown
state.unplugged = unknown
required-any = any

[Element Capture]
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Dock Mic Boost]
required-any = any
switch = select
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Option Dock Mic Boost:on]
name = input-boost-on

[Option Dock Mic Boost:off]
name = input-boost-off

[Element Dock Mic]
required-any = any
switch = mute
volume = merge
override-map.1 = all
override-map.2 = all-left,all-right

[Element Input Source]
enumeration = select

[Option Input Source:Dock Mic]
name = analog-input-microphone-dock
required-any = any

[Element Capture Source]
enumeration = select

[Option Capture Source:Dock Mic]
name = analog-input-microphone-dock
required-any = any

[Element Mic]
switch = off
volume = off

[Element Internal Mic]
switch = off
volume = off

[Element Front Mic]
switch = off
volume = off

[Element Rear Mic]
switch = off
volume = off

[Element Mic Boost]
switch = off
volume = off

[Element Internal Mic Boost]
switch = off
volume = off

[Element Front Mic Boost]
switch = off
volume = off

[Element Rear Mic Boost]
switch = off
volume = off

.include analog-input-mic.conf.common
