# translation of pulseaudio.master-tx.pa.po to Punjabi
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2008.
# A <PERSON>am <<EMAIL>>, 2009.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2009, 2012.
#
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio.master-tx.pa\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2012-01-30 09:55+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Punjabi/Panjabi <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Lokalize 1.0\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"

#: src/daemon/cmdline.c:113
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow module user requested "
"module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --module-idle-time=SECS           Unload autoloaded modules when idle "
"and\n"
"                                        this time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v                                    Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr} Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "--daemonize ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "--fail ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level ਨੂੰ ਲਾਗ ਲੈਵਲ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ (ਜਾਂ ਤਾਂ ਅੰਕੀ ਰੇਂਜ 0..4 ਜਾਂ debug, info, "
"notice, warn, error ਵਿੱਚੋਂ ਇੱਕ)।"

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "--high-priority ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "--realtime ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "--disallow-module-loading ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "--use-pid-file ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:328
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr "ਗਲਤ ਲਾਗ ਟਾਰਗੇਟ: 'syslog', 'stderr' ਜਾਂ 'auto' ਵਰਤੋਂ।"

#: src/daemon/cmdline.c:330
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr "ਗਲਤ ਲਾਗ ਟਾਰਗੇਟ: 'syslog', 'stderr' ਜਾਂ 'auto' ਵਰਤੋਂ।"

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--log-time ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "ਅਢੁੱਕਵਾਂ ਰੀਸੈਂਪਲ ਢੰਗ '%s'।"

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--system ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "--no-cpu-limit ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "--disable-shm ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/cmdline.c:397
#, fuzzy
msgid "--enable-memfd expects boolean argument"
msgstr "--realtime ਨੂੰ ਬੁਲੀਅਨ ਆਰਗੂਮੈਂਟ ਦੀ ਲੋੜ ਹੈ"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] ਗਲਤ ਲਾਗ ਟਾਰਗੇਟ '%s'।"

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] ਗਲਤੀ ਲਾਗ ਲੈਵਲ '%s'।"

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵੀਂ ਰੀਸੈਂਪਲ ਢੰਗ '%s'।"

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵੀਂ rlimit '%s'।"

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਫਾਰਮੈਟ '%s'।"

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਰੇਟ '%s'।"

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਚੈਨਲ '%s'।"

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵਾਂ ਚੈਨਲ ਮੈਪ '%s'।"

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] ਫਰੈਗਮੈਂਟਾਂ ਦਾ ਅਢੁੱਕਵਾਂ ਨੰਬਰ '%s'।"

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵਾਂ ਫਰੈਗਮੈਂਟ ਅਕਾਰ '%s'।"

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵਾਂ nice ਲੈਵਲ '%s'।"

#: src/daemon/daemon-conf.c:552
#, fuzzy, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਰੇਟ '%s'।"

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "ਸੰਰਚਨਾ ਫਾਇਲ ਖੋਲ੍ਹਣ ਵਿੱਚ ਫੇਲ੍ਹ: %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr "ਦਿੱਤੇ ਡਿਫਾਲਟ ਚੈਨਲ ਮੈਪ ਦੀ ਦਿੱਤੇਤ ਚੈਨਲ ਗਿਣਤੀ ਨਾਲੋਂ ਇੱਕ ਵੱਖਰੀ ਚੈਨਲ ਗਿਣਤੀ ਹੈ।"

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### ਸੰਰਚਨਾ ਫਾਇਲ ਵਿੱਚੋਂ ਪੜਿਆ: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "ਨਾਂ: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "ਕੋਈ ਮੋਡੀਊਲ ਜਾਣਕਾਰੀ ਉਪਲੱਬਧ ਨਹੀਂ\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "ਵਰਜਨ: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "ਵੇਰਵਾ: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "ਲੇਖਕ: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "ਵਰਤੋਂ: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "ਇੱਕ ਵਾਰ ਲੋਡ ਕਰੋ: %s\n"

#: src/daemon/dumpmodules.c:72
#, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "DEPRECATION WARNING: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "ਪਾਥ: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, fuzzy, c-format
msgid "Failed to open module %s: %s"
msgstr "ਸੰਰਚਨਾ ਫਾਇਲ '%s' ਨੂੰ ਖੋਲ੍ਹਣ ਵਿੱਚ ਫੇਲ੍ਹ: %s"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "ਅਸਲੀ lt_dlopen ਲੋਡਰ ਲੱਭਣ ਵਿੱਚ ਫੇਲ੍ਹ ਹੋਇਆ।"

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "ਨਵਾਂ dl ਲੋਡਰ ਦੇਣ ਲਈ ਫੇਲ੍ਹ।"

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "ਬਾਈਂਡ-ਨਾਓ-ਲੋਡਰ ਜੋੜਨ ਵਿੱਚ ਫੇਲ੍ਹ ਹੋਇਆ।"

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "'%s' ਯੂਜ਼ਰ ਲੱਭਣ ਵਿੱਚ ਫੇਲ੍ਹ ਹੋਇਆ ਹੈ।"

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "'%s' ਗਰੁੱਪ ਲੱਭਣ ਵਿੱਚ ਫੇਲ ਹੋਇਆ ਹੈ।"

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "ਯੂਜ਼ੂ '%s' ਅਤੇ ਗਰੁੱਪ '%s' ਦਾ GID ਮੇਲ ਨਹੀਂ ਖਾਂਦੇ।"

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "ਯੂਜ਼ੂ '%s' ਦੀ ਘਰ ਡਾਇਰੈਕਟਰੀ '%s' ਨਹੀਂ, ਅਣਡਿੱਠਾ ਕਰ ਰਿਹਾ।"

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "'%s' ਬਣਾਉਣ ਵਿੱਚ ਫੇਲ੍ਹ: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "ਗਰੁੱਪ ਲਿਸਟ ਬਦਲਣ ਲਈ ਫੇਲ੍ਹ: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "GID ਬਦਲਣ ਲਈ ਫੇਲ੍ਹ: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "UID ਬਦਲਣ ਲਈ ਫੇਲ੍ਹ: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "ਇਸ ਪਲੇਟਫਾਰਮ ਤੇ ਸਿਸਟਮ ਸੰਬੰਧੀ ਮੋਡ ਨੂੰ ਸਹਿਯੋਗ ਨਹੀਂ ਹੈ।"

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "ਕਮਾਂਡ ਲਾਈਨ ਪਾਰਸ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ।"

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "ਡੈਮਨ ਖਤਮ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr "ਇਹ ਪਰੋਗਰਾਮ ਰੂਟ ਦੇ ਤੌਰ ਤੇ ਚਲਾਉਣ ਲਈ ਨਹੀਂ ਹੈ (ਜਦੋਂ ਤੱਕ --system ਦਿੱਤਾ ਨਹੀਂ ਜਾਂਦਾ)।"

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "ਰੂਟ ਅਧਿਕਾਰਾਂ ਦੀ ਲੋੜ ਹੈ।"

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start ਨੂੰ ਸਿਸਟਮ ਮੌਕਿਆਂ ਲਈ ਸਹਿਯੋਗ ਨਹੀਂ ਹੈ।"

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""

#: src/daemon/main.c:878
#, fuzzy
msgid "Running in system mode, but --disallow-exit not set."
msgstr "ਸਿਸਟਮ ਮੋਡ ਵਿੱਚ ਚੱਲ ਰਿਹਾ ਹੈ, ਪਰ --disallow-exit ਸੈੱਟ ਨਹੀਂ ਕੀਤਾ!"

#: src/daemon/main.c:881
#, fuzzy
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr "ਸਿਸਟਮ ਮੋਡ ਵਿੱਚ ਚੱਲ ਰਿਹਾ ਹੈ, ਪਰ --disallow-module-loading ਸੈੱਟ ਨਹੀਂ ਕੀਤਾ!"

#: src/daemon/main.c:884
#, fuzzy
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "ਸਿਸਟਮ ਮੋਡ ਵਿੱਚ ਚੱਲ ਰਿਹਾ ਹੈ, ਜ਼ਬਰਦਸਤੀ SHM ਮੋਡ ਨੂੰ ਅਯੋਗ ਕਰ ਰਿਹਾ ਹੈ!"

#: src/daemon/main.c:889
#, fuzzy
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr "ਸਿਸਟਮ ਮੋਡ ਵਿੱਚ ਚੱਲ ਰਿਹਾ ਹੈ, ਜ਼ਬਰਦਸਤੀ idle ਟਾਈਲ ਬੰਦ ਨੂੰ ਅਯੋਗ ਕਰ ਰਿਹਾ ਹੈ!"

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "ਸਟੂਡੀਓ ਪ੍ਰਾਪਤ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ।"

#: src/daemon/main.c:928 src/daemon/main.c:999
#, fuzzy, c-format
msgid "pipe() failed: %s"
msgstr "pipe ਫੇਲ੍ਹ: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "ਡੈਮਨ ਸ਼ੁਰੂਆਤੀ ਫੇਲ੍ਹ ਹੋਈ।"

#: src/daemon/main.c:987
#, fuzzy, c-format
msgid "setsid() failed: %s"
msgstr "read() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "ਮਸ਼ੀਨ ID ਪ੍ਰਾਪਤ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ"

#: src/daemon/main.c:1145
#, fuzzy
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""
"ਠੀਕ ਹੈ, ਤਾਂ ਤੁਸੀਂ PA ਨੂੰ ਸਿਸਟਮ ਮੋਡ ਵਿੱਚ ਚਲਾ ਰਹੇ ਹੋ। ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਤੁਹਾਨੂੰ ਇਹ ਕਰਨਾ "
"ਨਹੀਂ ਚਾਹੀਦਾ।\n"
"ਜੇ ਤੁਸੀਂ ਅਜਿਹਾ ਕੀਤਾ ਹੈ ਤਾਂ ਇਹ ਤੁਹਾਡੀ ਗਲਤੀ ਹੈ ਜੇ ਲੋੜ-ਮੁਤਾਬਕ ਠੀਕ ਕੰਮ ਨਾ ਚੱਲਿਆ।\n"
"ਕਿਰਪਾ ਕਰਕੇ ਸਿਸਟਮ ਮੋਡ ਦੇ ਗਲਤ ਹੋਣ ਬਾਰੇ ਵਧੇਰੇ ਜਾਣਕਾਰੀ ਲਈ http://www.freedesktop.org/"
"wiki/Software/PulseAudio/Documentation/User/WhatIsWrongWithSystemWide/ ਵੇਖੋ।"

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() ਫੇਲ੍ਹ ਹੈ।"

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() ਫੇਲ੍ਹ ਹੈ।"

#: src/daemon/main.c:1268
#, fuzzy
msgid "command line arguments"
msgstr "ਬਹੁਤ ਵੱਧ ਆਰਗੂਮੈਂਟ।"

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "ਡੈਮਨ ਸ਼ੁਰੂਆਤੀ ਬਿਨਾਂ ਕਿਸੇ ਲੋਡ ਕੀਤੇ ਮੈਡਿਊਲ, ਕੰਮ ਕਰਨ ਤੋਂ ਰੋਕ ਰਿਹਾ ਹੈ।"

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "ਪਲਸਆਡੀਓ ਸਾਊਂਡ ਸਿਸਟਮ"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "ਪਲਸਆਡੀਓ ਸਾਊਂਡ ਸਿਸਟਮ ਚਲਾਓ"

#: src/modules/alsa/alsa-mixer.c:2708
msgid "Input"
msgstr "ਇੰਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr "ਡੌਕਿੰਗ ਸਟੇਸ਼ਨ ਇੰਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2710
#, fuzzy
msgid "Docking Station Microphone"
msgstr "ਡੌਕਿੰਗ ਸਟੇਸ਼ਨ ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2711
#, fuzzy
msgid "Docking Station Line In"
msgstr "ਡੌਕਿੰਗ ਸਟੇਸ਼ਨ ਇੰਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr "ਲਾਈਨ-ਇਨ"

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr "ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
#, fuzzy
msgid "Front Microphone"
msgstr "ਡੌਕਿੰਗ ਸਟੇਸ਼ਨ ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
#, fuzzy
msgid "Rear Microphone"
msgstr "ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr "ਬਾਹਰੀ ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
msgid "Internal Microphone"
msgstr "ਅੰਦਰੂਨੀ ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr "ਰੇਡੀਓ"

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr "ਵੀਡੀਓ"

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr "ਆਟੋਮੈਟਿਕ ਗੇਨ ਕੰਟਰੋਲ"

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr "ਕੋਈ ਆਟੋਮੈਟਿਕ ਗੇਨ ਕੰਟਰੋਲ ਨਹੀਂ"

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr "ਬੂਸਟ"

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr "ਕੋਈ ਬੂਸਟ ਨਹੀਂ"

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr "ਐਂਪਲੀਫਾਇਰ"

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr "ਕੋਈ ਐਂਪਲੀਫਾਇਰ ਨਹੀਂ"

#: src/modules/alsa/alsa-mixer.c:2726
#, fuzzy
msgid "Bass Boost"
msgstr "ਬੂਸਟ"

#: src/modules/alsa/alsa-mixer.c:2727
#, fuzzy
msgid "No Bass Boost"
msgstr "ਕੋਈ ਬੂਸਟ ਨਹੀਂ"

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
msgid "Headphones"
msgstr "ਐਨਾਲਾਗ ਹੈੱਡਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2796
msgid "Analog Input"
msgstr "ਐਨਾਲਾਗ ਇੰਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr "ਡੌਕਿੰਗ ਸਟੇਸ਼ਨ ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2802
#, fuzzy
msgid "Headset Microphone"
msgstr "ਮਾਈਕਰੋਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2806
msgid "Analog Output"
msgstr "ਐਨਾਲਾਗ ਆਉਟਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2808
#, fuzzy
msgid "Headphones 2"
msgstr "ਐਨਾਲਾਗ ਹੈੱਡਫੋਨ"

#: src/modules/alsa/alsa-mixer.c:2809
#, fuzzy
msgid "Headphones Mono Output"
msgstr "ਐਨਾਲਾਗ ਮੋਨੋ ਆਊਟਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2810
#, fuzzy
msgid "Line Out"
msgstr "ਲਾਈਨ-ਇਨ"

#: src/modules/alsa/alsa-mixer.c:2811
msgid "Analog Mono Output"
msgstr "ਐਨਾਲਾਗ ਮੋਨੋ ਆਊਟਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2812
#, fuzzy
msgid "Speakers"
msgstr "ਐਨਾਲਾਗ ਸਟੀਰੀਓ"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2814
#, fuzzy
msgid "Digital Output (S/PDIF)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਟੀਰੀਓ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2815
#, fuzzy
msgid "Digital Input (S/PDIF)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਟੀਰੀਓ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2817
#, fuzzy
msgid "Multichannel Output"
msgstr "ਜ਼ੀਰੋ (Null) ਆਉਟਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2818
#, fuzzy
msgid "Game Output"
msgstr "ਜ਼ੀਰੋ (Null) ਆਉਟਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
#, fuzzy
msgid "Chat Output"
msgstr "ਜ਼ੀਰੋ (Null) ਆਉਟਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2821
#, fuzzy
msgid "Chat Input"
msgstr "ਇੰਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:2822
#, fuzzy
msgid "Virtual Surround 7.1"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "ਐਨਾਲਾਗ ਮੋਨੋ"

#: src/modules/alsa/alsa-mixer.c:4564
#, fuzzy
msgid "Analog Mono (Left)"
msgstr "ਐਨਾਲਾਗ ਮੋਨੋ"

#: src/modules/alsa/alsa-mixer.c:4565
#, fuzzy
msgid "Analog Mono (Right)"
msgstr "ਐਨਾਲਾਗ ਮੋਨੋ"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "ਐਨਾਲਾਗ ਸਟੀਰੀਓ"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "ਮੋਨੋ"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "ਸਟੀਰੀਓ"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
#, fuzzy
msgid "Speakerphone"
msgstr "ਐਨਾਲਾਗ ਸਟੀਰੀਓ"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4580
msgid "Analog Surround 2.1"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 2.1"

#: src/modules/alsa/alsa-mixer.c:4581
msgid "Analog Surround 3.0"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 3.0"

#: src/modules/alsa/alsa-mixer.c:4582
msgid "Analog Surround 3.1"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 3.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
msgid "Analog Surround 6.0"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 6.0"

#: src/modules/alsa/alsa-mixer.c:4588
msgid "Analog Surround 6.1"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 6.1"

#: src/modules/alsa/alsa-mixer.c:4589
msgid "Analog Surround 7.0"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 7.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "ਐਨਾਲਾਗ ਸਰਾਊਂਡ 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਟੀਰੀਓ (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਰਾਊਂਡ 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਰਾਊਂਡ 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
#, fuzzy
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਰਾਊਂਡ 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਟੀਰੀਓ (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
#, fuzzy
msgid "Digital Surround 5.1 (HDMI)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਰਾਊਂਡ 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4732
msgid "Analog Mono Duplex"
msgstr "ਐਨਾਲਾਗ ਮੋਨੋ ਡੁਪਲੈਕਸ"

#: src/modules/alsa/alsa-mixer.c:4733
msgid "Analog Stereo Duplex"
msgstr "ਐਨਾਲਾਗ ਸਟੀਰੀਓ ਡੁਪਲੈਕਸ"

#: src/modules/alsa/alsa-mixer.c:4736
msgid "Digital Stereo Duplex (IEC958)"
msgstr "ਡਿਜ਼ੀਟਲ ਸਟੀਰੀਓ ਡੁਪਲੈਕਸ (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4738
#, fuzzy
msgid "Stereo Duplex"
msgstr "ਐਨਾਲਾਗ ਸਟੀਰੀਓ ਡੁਪਲੈਕਸ"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "ਬੰਦ"

#: src/modules/alsa/alsa-mixer.c:4840
#, fuzzy, c-format
msgid "%s Output"
msgstr "ਜ਼ੀਰੋ (Null) ਆਉਟਪੁੱਟ"

#: src/modules/alsa/alsa-mixer.c:4848
#, fuzzy, c-format
msgid "%s Input"
msgstr "ਇੰਪੁੱਟ"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, fuzzy, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write!\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, fuzzy, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read!\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, fuzzy, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() ਤੋਂ ਇੱਕ ਮੁੱਲ ਮਿਲਿਆ ਹੈ, ਜੋ ਬਹੁਤ ਵੱਡਾ ਹੈ: %lu ਬਾਈਟ (%lu ms)।\n"
"ਇਹ ALSA ਡਰਾਈਵਰ '%s' ਵਿਚਲਾ ਬੱਗ ਲੱਗਦਾ ਹੈ। ਇਸ ਮੁੱਦੇ ਦੀ ALSA ਡਿਵੈਲਪਰਾਂ ਨੂੰ ਰਿਪੋਰਟ ਦਿਓ ਜੀ।"
msgstr[1] ""
"snd_pcm_avail() ਤੋਂ ਇੱਕ ਮੁੱਲ ਮਿਲਿਆ ਹੈ, ਜੋ ਬਹੁਤ ਵੱਡਾ ਹੈ: %lu ਬਾਈਟ (%lu ms)।\n"
"ਇਹ ALSA ਡਰਾਈਵਰ '%s' ਵਿਚਲਾ ਬੱਗ ਲੱਗਦਾ ਹੈ। ਇਸ ਮੁੱਦੇ ਦੀ ALSA ਡਿਵੈਲਪਰਾਂ ਨੂੰ ਰਿਪੋਰਟ ਦਿਓ ਜੀ।"

#: src/modules/alsa/alsa-util.c:1249
#, fuzzy, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() ਤੋਂ ਇੱਕ ਮੁੱਲ ਮਿਲਿਆ ਹੈ, ਜੋ ਬਹੁਤ ਵੱਡਾ ਹੈ: %li ਬਾਈਟ (%s%lu ms)।\n"
"ਇਹ ALSA ਡਰਾਈਵਰ '%s' ਵਿਚਲਾ ਬੱਗ ਲੱਗਦਾ ਹੈ। ਇਸ ਮੁੱਦੇ ਦੀ ALSA ਡਿਵੈਲਪਰਾਂ ਨੂੰ ਰਿਪੋਰਟ ਦਿਓ ਜੀ।"
msgstr[1] ""
"snd_pcm_delay() ਤੋਂ ਇੱਕ ਮੁੱਲ ਮਿਲਿਆ ਹੈ, ਜੋ ਬਹੁਤ ਵੱਡਾ ਹੈ: %li ਬਾਈਟ (%s%lu ms)।\n"
"ਇਹ ALSA ਡਰਾਈਵਰ '%s' ਵਿਚਲਾ ਬੱਗ ਲੱਗਦਾ ਹੈ। ਇਸ ਮੁੱਦੇ ਦੀ ALSA ਡਿਵੈਲਪਰਾਂ ਨੂੰ ਰਿਪੋਰਟ ਦਿਓ ਜੀ।"

#: src/modules/alsa/alsa-util.c:1296
#, fuzzy, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail() ਤੋਂ ਇੱਕ ਮੁੱਲ ਮਿਲਿਆ ਹੈ, ਜੋ ਬਹੁਤ ਵੱਡਾ ਹੈ: %lu ਬਾਈਟ (%lu ms)।\n"
"ਇਹ ALSA ਡਰਾਈਵਰ '%s' ਵਿਚਲਾ ਬੱਗ ਲੱਗਦਾ ਹੈ। ਇਸ ਮੁੱਦੇ ਦੀ ALSA ਡਿਵੈਲਪਰਾਂ ਨੂੰ ਰਿਪੋਰਟ ਦਿਓ ਜੀ।"

#: src/modules/alsa/alsa-util.c:1339
#, fuzzy, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() ਤੋਂ ਇੱਕ ਮੁੱਲ ਮਿਲਿਆ ਹੈ, ਜੋ ਬਹੁਤ ਵੱਡਾ ਹੈ: %lu ਬਾਈਟ (%lu ms)।\n"
"ਇਹ ALSA ਡਰਾਈਵਰ '%s' ਵਿਚਲਾ ਬੱਗ ਲੱਗਦਾ ਹੈ। ਇਸ ਮੁੱਦੇ ਦੀ ALSA ਡਿਵੈਲਪਰਾਂ ਨੂੰ ਰਿਪੋਰਟ ਦਿਓ ਜੀ।"
msgstr[1] ""
"snd_pcm_mmap_begin() ਤੋਂ ਇੱਕ ਮੁੱਲ ਮਿਲਿਆ ਹੈ, ਜੋ ਬਹੁਤ ਵੱਡਾ ਹੈ: %lu ਬਾਈਟ (%lu ms)।\n"
"ਇਹ ALSA ਡਰਾਈਵਰ '%s' ਵਿਚਲਾ ਬੱਗ ਲੱਗਦਾ ਹੈ। ਇਸ ਮੁੱਦੇ ਦੀ ALSA ਡਿਵੈਲਪਰਾਂ ਨੂੰ ਰਿਪੋਰਟ ਦਿਓ ਜੀ।"

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
#, fuzzy
msgid "Bluetooth Output"
msgstr "ਐਨਾਲਾਗ ਆਉਟਪੁੱਟ"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1971
#, fuzzy
msgid "Headphone"
msgstr "ਐਨਾਲਾਗ ਹੈੱਡਫੋਨ"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2042
#, fuzzy
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "ਹਾਈ ਫਡੈਲਿਟੀ ਪਲੇਅਬੈਕ (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
#, fuzzy
msgid "High Fidelity Capture (A2DP Source)"
msgstr "ਹਾਈ ਫਡੈਲਿਟੀ ਪਲੇਅਬੈਕ (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr ""

#: src/modules/echo-cancel/module-echo-cancel.c:59
#, fuzzy
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr ""

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "ਡੰਮੀ ਆਊਟਪੁੱਟ"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr "ਹਮੇਸ਼ਾਂ ਘੱਟੋ-ਘੱਟ ਇੱਕ ਸਿੰਕ ਲੋਡ ਹੀ ਰੱਖੋ ਭਾਵੇਂ ਇਹ ਇੱਕ ਜ਼ੀਰੋ (null) ਹੇਵੋ"

#: src/modules/module-always-source.c:35
#, fuzzy
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr "ਹਮੇਸ਼ਾਂ ਘੱਟੋ-ਘੱਟ ਇੱਕ ਸਿੰਕ ਲੋਡ ਹੀ ਰੱਖੋ ਭਾਵੇਂ ਇਹ ਇੱਕ ਜ਼ੀਰੋ (null) ਹੇਵੋ"

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr ""

#: src/modules/module-equalizer-sink.c:72
#, fuzzy
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr ""

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr ""

#: src/modules/module-ladspa-sink.c:50
msgid "Virtual LADSPA sink"
msgstr "ਵਰਚੁਅਲ LADSPA ਸਿੰਕ"

#: src/modules/module-ladspa-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr "ਕਲਾਕਡ NULL ਸਿੰਕ"

#: src/modules/module-null-sink.c:356
msgid "Null Output"
msgstr "ਜ਼ੀਰੋ (Null) ਆਉਟਪੁੱਟ"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, fuzzy, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "ਸਰੋਤ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr "ਆਊਟਪੁੱਟ ਜੰਤਰ"

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
msgid "Input Devices"
msgstr "ਇੰਪੁੱਟ ਜੰਤਰ"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr "@HOSTNAME@ ਉੱਪਰ ਆਡੀਓ"

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr ""

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr ""

#: src/modules/module-virtual-surround-sink.c:50
#, fuzzy
msgid "Virtual surround sink"
msgstr "ਵਰਚੁਅਲ LADSPA ਸਿੰਕ"

#: src/modules/module-virtual-surround-sink.c:54
#, fuzzy
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> plugin=<ladspa "
"plugin name> label=<ladspa plugin label> control=<comma separated list of "
"input control values>"

#: src/modules/raop/module-raop-discover.c:295
#, fuzzy
msgid "Unknown device model"
msgstr "ਅਣਜਾਣ ਗਲਤੀ ਕੋਡ"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr ""

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "ਪਲਸਆਡੀਓ ਸਾਊਂਡ ਡਰਾਇਵਰ"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "ਅੱਗੇ ਸੈਂਟਰ"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "ਅੱਗੇ ਖੱਬੇ"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "ਅੱਗੇ ਸੱਜਾ"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "ਪਿੱਛੇ ਸੈਂਟਰ"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "ਪਿੱਛੇ ਖੱਬਾ"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "ਪਿੱਛੇ ਸੱਜਾ"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr ""

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "ਅੱਗੇ ਸੈਂਟਰ ਦਾ ਖੱਬੇ"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "ਅੱਗੇ ਸੈਂਟਰ ਦਾ ਸੱਜਾ"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "ਖੱਬੇ ਪਾਸੇ"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "ਸੱਜੇ ਪਾਸੇ"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "ਐਗਜਿਲਰੀ 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "ਐਗਜਿਲਰੀ 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "ਐਗਜਿਲਰੀ 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "ਐਗਜਿਲਰੀ 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "ਐਗਜਿਲਰੀ 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "ਐਗਜਿਲਰੀ 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "ਐਗਜਿਲਰੀ 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "ਐਗਜਿਲਰੀ 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "ਐਗਜਿਲਰੀ 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "ਐਗਜਿਲਰੀ 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "ਐਗਜਿਲਰੀ 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "ਐਗਜਿਲਰੀ 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "ਐਗਜਿਲਰੀ 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "ਐਗਜਿਲਰੀ 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "ਐਗਜਿਲਰੀ 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "ਐਗਜਿਲਰੀ 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "ਐਗਜਿਲਰੀ 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "ਐਗਜਿਲਰੀ 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "ਐਗਜਿਲਰੀ 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "ਐਗਜਿਲਰੀ 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "ਐਗਜਿਲਰੀ 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "ਐਗਜਿਲਰੀ 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "ਐਗਜਿਲਰੀ 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "ਐਗਜਿਲਰੀ 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "ਐਗਜਿਲਰੀ 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "ਐਗਜਿਲਰੀ 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "ਐਗਜਿਲਰੀ 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "ਐਗਜਿਲਰੀ 27"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "ਐਗਜਿਲਰੀ 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "ਐਗਜਿਲਰੀ 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "ਐਗਜਿਲਰੀ 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "ਐਗਜਿਲਰੀ 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "ਉੱਤੇ ਕੇਂਦਰੀ"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "ਉੱਤੇ ਅੱਗੇ ਸੈਂਟਰ"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "ਉੱਤੇ ਅੱਗੇ ਖੱਬੇ"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "ਉੱਤੇ ਅੱਗੇ ਸੱਜੇ"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "ਉੱਤੇ ਪਿੱਛੇ ਸੈਂਟਰ"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "ਉੱਤੇ ਪਿੱਛੇ ਖੱਬੇ"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "ਉੱਤੇ ਪਿੱਛੇ ਸੱਜੇ"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(ਅਢੁੱਕਵਾਂ)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "ਸਰਾਊਂਡਿੰਗ 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "ਸਰਾਊਂਡਿੰਗ 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "ਸਰਾਊਂਡਿੰਗ 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "ਸਰਾਊਂਡਿੰਗ 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "ਸਰਾਊਂਡਿੰਗ 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
#, fuzzy
msgid "xcb_connect() failed"
msgstr "pa_context_connect() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr ""

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "ਕੂਕੀ ਡਾਟਾ ਪਾਰਸ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "ਅਣਜਾਣੀ ਇਕਸਟੈਂਸ਼ਨ '%s' ਲਈ ਸੁਨੇਹਾ ਮਿਲਿਆ ਹੈ"

#: src/pulse/direction.c:37
#, fuzzy
msgid "input"
msgstr "ਇੰਪੁੱਟ"

#: src/pulse/direction.c:39
#, fuzzy
msgid "output"
msgstr "ਜ਼ੀਰੋ (Null) ਆਉਟਪੁੱਟ"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr ""

#: src/pulse/direction.c:43
#, fuzzy
msgid "invalid"
msgstr "(ਅਢੁੱਕਵਾਂ)"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr ""

#: src/pulsecore/core-util.h:97
#, fuzzy
msgid "no"
msgstr "ਮੋਨੋ"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "autospawn ਲਾਕ ਵਰਤ ਨਹੀਂ ਸਕਦਾ।"

#: src/pulsecore/log.c:165
#, fuzzy, c-format
msgid "Failed to open target file '%s'."
msgstr "ਸਾਊਂਡ ਫਾਇਲ ਖੋਲ੍ਹਣ ਲਈ ਫੇਲ੍ਹ ਹੈ।"

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""

#: src/pulsecore/log.c:651
#, fuzzy
msgid "Invalid log target."
msgstr "[%s:%u] ਗਲਤ ਲਾਗ ਟਾਰਗੇਟ '%s'।"

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "ਅੰਦਰੂਨੀ ਆਡੀਓ"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "ਮਾਡਮ"

#: src/pulse/error.c:38
msgid "OK"
msgstr "ਠੀਕ ਹੈ"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "ਅਸੈੱਸ ਪਾਬੰਦੀ ਹੈ"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "ਅਣਜਾਣ ਕਮਾਂਡ"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "ਅਢੁੱਕਵਾਂ ਆਰਗੂਮੈਂਟ"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "ਐਂਟਟੀ ਮੌਜੂਦ ਹੈ"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "ਕੋਈ ਐਂਟਟੀ ਨਹੀਂ"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "ਕੁਨੈਕਸ਼ਨ ਤੋਂ ਇਨਕਾਰ"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "ਪਰੋਟੋਕਾਲ ਗਲਤੀ"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "ਸਮਾਂ-ਸਮਾਪਤ"

#: src/pulse/error.c:47
#, fuzzy
msgid "No authentication key"
msgstr "ਕੋਈ ਪ੍ਰਮਾਣਿਕਤਾ ਕੁੰਜੀ ਨਹੀਂ"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "ਅੰਦਰੂਨੀ ਗਲਤੀ"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "ਕੁਨੈਕਸ਼ਨ ਖਤਮ ਕੀਤਾ"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "ਐਂਟਟੀ ਖਤਮ ਹੋ ਗਈ"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "ਅਢੁੱਕਵਾਂ ਸਰਵਰ"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "ਮੋਡੀਊਲ ਸ਼ੁਰੂ ਕਰਨਾ ਫੇਲ੍ਹ"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "ਖਰਾਬ ਹਾਲਤ"

#: src/pulse/error.c:54
msgid "No data"
msgstr "ਕੋਈ ਡਾਟਾ ਨਹੀਂ"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "ਨਾ-ਅਨੁਕੂਲ ਪਰੋਟੋਕਾਲ ਵਰਜਨ"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "ਬਹੁਤ ਵੱਡਾ"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "ਸਹਾਇਕ ਨਹੀਂ"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "ਅਣਜਾਣ ਗਲਤੀ ਕੋਡ"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "ਕੋਈ ਅਜਿਹੀ ਇਕਸਟੈਂਸ਼ਨ ਨਹੀਂ"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "ਛੱਡੀ ਗਈ ਫੰਕਸ਼ਨੈਲਿਟੀ"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "ਗੈਰ-ਮੌਜੂਦ ਨਿਰਧਾਰਨ"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "ਕਲਾਇਟ ਅੱਡ ਕੀਤਾ"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "ਇੰਪੁੱਟ/ਆਊਟਪੁੱਟ ਗਲਤੀ"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "ਜਤੰਰ ਜਾਂ ਸਰੋਤ ਵਰਤੋਂ ਅਧੀਨ ਹੈ"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GiB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MiB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KiB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "ਸਟਰੀਮ ਡਰੇਨ ਫੇਲ੍ਹ ਹੋਇਆ: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "ਪਲੇਬੈਕ ਸਟਰੀਮ ਡਰੇਨ ਕੀਤੀ।"

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "ਸਰਵਰ ਨਾਲ ਕੁਨੈਕਸ਼ਨ ਡਰੇਨ ਹੋ ਰਿਹਾ ਹੈ।"

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_write() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "ਸਟਰੀਮ ਸਫਲਤਾਪੂਰਕ ਬਣ ਗਈ ਹੈ।"

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Buffer metrics: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "ਸਧਾਰਨ spec '%s', ਚੈਨਲ ਮੈਪ '%s' ਦੀ ਵਰਤੋਂ।"

#: src/utils/pacat.c:342
#, fuzzy, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "%s ਜੰਤਰ ਨਾਲ ਕੁਨਕੈਟ ਕੀਤਾ (%u, %ssuspended)।"

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "ਸਟਰੀਮ ਗਲਤੀ: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "ਸਟਰੀਮ ਜੰਤਰ ਸਸਪੈਂਡ ਕੀਤਾ ਹੈ।%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "ਸਟਰੀਮ ਜੰਤਰ ਮੁੜ-ਪ੍ਰਾਪਤ ਕੀਤਾ।%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "ਸਟਰੀਮ ਅੰਡਰਰਨ।%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "ਸਟਰੀਮ ਓਵਰਰਨ।%s"

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "ਸਟਰੀਮ ਸ਼ੁਰੂ ਕੀਤੀ। %s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "ਸਟਰੀਮ ਨੂੰ ਜੰਤਰ %s ਤੋਂ ਤਬਦੀਲ ਕੀਤਾ ਗਿਆ ਹੈ (%u, %ssuspended)।%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "ਨਹੀਂ "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "ਸਟਰੀਮ ਬਫਰ ਐਟਰੀਬਿਊਟ ਤਬਦੀਲ ਕੀਤੇ ਗਏ।%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr ""

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr ""

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "ਕੁਨੈਕਸ਼ਨ ਬਣ ਗਿਆ।%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:497
#, fuzzy, c-format
msgid "Failed to set monitor stream: %s"
msgstr "ਸਟਰੀਮ ਡਰੇਨ ਫੇਲ੍ਹ ਹੋਇਆ: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "ਕੁਨੈਕਸ਼ਨ ਫੇਲ: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF ਮਿਲਿਆ।"

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "ਸਿਗਨਲ ਮਿਲਿਆ, ਬੰਦ ਹੋ ਰਿਹਾ ਹੈ।"

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "ਵਕਫਾ ਪ੍ਰਾਪਤੀ ਫੇਲ ਹੋਈ: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "ਟਾਈਮ: %0.3f ਸਕਿੰਟ; ਵਕਫਾ: %0.0f usec।"

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [options]\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample type, one of s16le, "
"s16be, u8, float32le,\n"
"                                        float32be, ulaw, alaw, s32le, s32be, "
"s24le, s24be,\n"
"                                        s24-32le, s24-32be (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink "
"the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink "
"the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink the stream is being "
"connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --file-format=FFORMAT             Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s ਦੇ ਕੰਪਾਇਲ\n"
"libpulse %s ਨਾਲ ਲਿੰਕ ਕੀਤਾ\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "ਅਢੁੱਕਵਾਂ ਚੈਨਲ ਮੈਪ '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "ਅਢੁੱਕਵਾਂ ਰੀਸੈਂਪਲ ਢੰਗ '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "ਅਢੁੱਕਵਾਂ ਚੈਨਲ ਮੈਪ '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "ਅਢੁੱਕਵਾਂ ਵਕਫਾ ਹਦਾਇਤ '%s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "ਅਢੁੱਕਵਾਂ ਪਰੋਸੈੱਸ ਟਾਈਮ ਹਦਾਇਤ '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "ਅਢੁੱਕਵਾਂ ਰੀਸੈਂਪਲ ਢੰਗ '%s'"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "ਅਣਜਾਣ ਫਾਇਲ ਫਾਰਮੈਟ %s"

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr ""

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "ਬਹੁਤ ਵੱਧ ਆਰਗੂਮੈਂਟ।"

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "ਸੈਂਪਲ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "ਸਾਊਂਡ ਫਾਇਲ ਖੋਲ੍ਹਣ ਲਈ ਫੇਲ੍ਹ ਹੈ।"

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr "ਇੱਕ %s ਸਟਰੀਮ ਨੂੰ ਸੈਂਪਲ ਹਦਾਇਤ '%s' ਨਾਲ ਖੋਲ੍ਹਿਆ ਜਾ ਰਿਹਾ ਹੈ।"

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "ਸੈਂਪਲ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "ਇੱਕ %s ਸਟਰੀਮ ਨੂੰ ਸੈਂਪਲ ਹਦਾਇਤ '%s' ਨਾਲ ਖੋਲ੍ਹਿਆ ਜਾ ਰਿਹਾ ਹੈ।"

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "ਚੈਨਲ ਮੈਪ ਸੈਂਪਲ ਹਦਾਇਤ ਨਾਲ ਨਹੀਂ ਮਿਲਦਾ"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "ਇੱਕ %s ਸਟਰੀਮ ਨੂੰ ਸੈਂਪਲ ਹਦਾਇਤ '%s' ਨਾਲ ਖੋਲ੍ਹਿਆ ਜਾ ਰਿਹਾ ਹੈ।"

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr "ਇੱਕ %s ਸਟਰੀਮ ਨੂੰ ਸੈਂਪਲ ਹਦਾਇਤ '%s' ਅਤੇ ਚੈਨਲ ਮੈਪ '%s' ਨਾਲ ਖੋਲ੍ਹਿਆ ਜਾ ਰਿਹਾ ਹੈ।"

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "ਰਿਕਾਰਡਿੰਗ"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "ਪਲੇਅਬੈਕ"

#: src/utils/pacat.c:1162
#, fuzzy
msgid "Failed to set media name."
msgstr "ਕਮਾਂਡ ਲਾਈਨ ਪਾਰਸ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ।"

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() ਫੇਲ੍ਹ ਹੈ।"

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() ਫੇਲ੍ਹ ਹੈ।"

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() ਫੇਲ੍ਹ ਹੈ।"

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_connect() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_new() ਫੇਲ੍ਹ ਹੈ।"

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() ਫੇਲ੍ਹ ਹੈ।"

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr ""

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr ""

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr ""

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr ""

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr ""

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:61
msgid "#N"
msgstr ""

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr ""

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr ""

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr ""

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr ""

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr ""

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr ""

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr ""

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr ""

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr ""

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr ""

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr ""

#: src/utils/pacmd.c:82
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"

#: src/utils/pacmd.c:129
#, fuzzy, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"libpulse %s ਦੇ ਕੰਪਾਇਲ\n"
"libpulse %s ਨਾਲ ਲਿੰਕ ਕੀਤਾ\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr "ਕੋਈ ਪਲਸ-ਆਡੀਓ ਡੈਮਨ ਨਹੀਂ ਚੱਲ ਰਿਹਾ, ਜਾਂ ਸ਼ੈਸ਼ਨ ਡੈਮਨ ਤੌਰ ਤੇ ਨਹੀਂ ਚੱਲ ਰਿਹਾ।"

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "ਪਲਸਆਡੀਓ ਡੈਮਨ ਬੰਦ ਕਰਨ ਵਿੱਚ ਫੇਲ।"

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "ਡੈਮਨ ਜਵਾਬ ਨਹੀਂ ਦੇ ਰਹੀ।"

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "ਅੰਕੜੇ ਪ੍ਰਾਪਤੀ ਫੇਲ੍ਹ: %s"

#: src/utils/pactl.c:199
#, fuzzy, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "ਹੁਣ ਵਰਤੋਂ ਵਿੱਚ ਹੈ: %u ਬਲਾਕ ਵਿੱਚ ਕੁੱਲ %s ਬਾਈਟ ਹਨ।\n"
msgstr[1] "ਹੁਣ ਵਰਤੋਂ ਵਿੱਚ ਹੈ: %u ਬਲਾਕ ਵਿੱਚ ਕੁੱਲ %s ਬਾਈਟ ਹਨ।\n"

#: src/utils/pactl.c:205
#, fuzzy, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "ਪੂਰੇ ਲਾਈਫਟਾਈਮ ਵਿੱਚ ਜਾਰੀ ਕੀਤਾ ਗਿਆ: %u ਬਲਾਕ ਵਿੱਚ ਕੁੱਲ %s ਬਾਈਟ ਹਨ।\n"
msgstr[1] "ਪੂਰੇ ਲਾਈਫਟਾਈਮ ਵਿੱਚ ਜਾਰੀ ਕੀਤਾ ਗਿਆ: %u ਬਲਾਕ ਵਿੱਚ ਕੁੱਲ %s ਬਾਈਟ ਹਨ।\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "ਸੈਂਪਲ ਕੈਸ਼ ਸਾਈਜ਼: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "ਸਰਵਰ ਜਾਣਕਾਰੀ ਪ੍ਰਾਪਤ ਕਰਨ ਵਿੱਚ ਫੇਲ ਹੋਇਆ: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, fuzzy, c-format
msgid "%s\n"
msgstr "%s %s"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""

#: src/utils/pactl.c:294
#, fuzzy, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"User name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %08x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr ""

#: src/utils/pactl.c:321
msgid "available"
msgstr ""

#: src/utils/pactl.c:322
msgid "not available"
msgstr ""

#: src/utils/pactl.c:331 src/utils/pactl.c:355
#, fuzzy
msgid "Unknown"
msgstr "ਅਣਜਾਣ ਕਮਾਂਡ"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr ""

#: src/utils/pactl.c:335
#, fuzzy
msgid "Line"
msgstr "ਲਾਈਨ-ਇਨ"

#: src/utils/pactl.c:336
msgid "Mic"
msgstr ""

#: src/utils/pactl.c:338
msgid "Handset"
msgstr ""

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr ""

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr ""

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr ""

#: src/utils/pactl.c:342
msgid "TV"
msgstr ""

#: src/utils/pactl.c:345
msgid "USB"
msgstr ""

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr ""

#: src/utils/pactl.c:352
msgid "Network"
msgstr ""

#: src/utils/pactl.c:353
#, fuzzy
msgid "Analog"
msgstr "ਐਨਾਲਾਗ ਮੋਨੋ"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "ਸਿੰਕ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ੍ਹ: %s"

#: src/utils/pactl.c:664
#, fuzzy, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s%s%s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s%s%s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tਪੋਰਟ:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ""

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tਸਰਗਰਮ ਪੋਰਟ: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, fuzzy, c-format
msgid "\tFormats:\n"
msgstr "\tਪੋਰਟ:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "ਸਰੋਤ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:849
#, fuzzy, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s%s%s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s%s%s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "ਉਪਲੱਬਧ ਨਹੀਂ"

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "ਮੋਡੀਊਲ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "ਕਲਾਇਟ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "ਕਾਰਡ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tਪਰੋਫਾਈਲ:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tਸਰਗਰਮ ਪਰੋਫਾਈਲ: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr ""

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "ਇੰਪੁੱਟ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ੍ਹ: %s"

#: src/utils/pactl.c:1366
#, fuzzy, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "ਸਰੋਤ ਆਉਟਪੁੱਟ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:1489
#, fuzzy, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "ਸੈਂਪਲ ਜਾਣਕਾਰੀ ਲੈਣ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:1604
#, fuzzy, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "ਫੇਲ੍ਹ: %s"

#: src/utils/pactl.c:1667
#, fuzzy, c-format
msgid "Send message failed: %s"
msgstr "read() ਫੇਲ੍ਹ ਹੈ: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr ""

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr ""

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, fuzzy, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "ਸੈਂਪਲ ਅੱਪਲੋਡ ਕਰਨ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
msgstr[1] ""

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "ਸੈਂਪਲ ਅੱਪਲੋਡ ਕਰਨ ਵਿੱਚ ਫੇਲ: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "ਫਾਇਲ ਦਾ ਸਮੇਂ ਤੋਂ ਪਹਿਲਾਂ ਅੰਤ"

#: src/utils/pactl.c:2144
msgid "new"
msgstr ""

#: src/utils/pactl.c:2147
msgid "change"
msgstr ""

#: src/utils/pactl.c:2150
msgid "remove"
msgstr ""

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr ""

#: src/utils/pactl.c:2161
msgid "sink"
msgstr ""

#: src/utils/pactl.c:2164
msgid "source"
msgstr ""

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr ""

#: src/utils/pactl.c:2170
msgid "source-output"
msgstr ""

#: src/utils/pactl.c:2173
msgid "module"
msgstr ""

#: src/utils/pactl.c:2176
msgid "client"
msgstr ""

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr ""

#: src/utils/pactl.c:2182
#, fuzzy
msgid "server"
msgstr "ਅਢੁੱਕਵਾਂ ਸਰਵਰ"

#: src/utils/pactl.c:2185
msgid "card"
msgstr ""

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr ""

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT ਮਿਲਿਆ, ਬੰਦ ਹੋ ਰਿਹਾ ਹੈ।"

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr ""

#: src/utils/pactl.c:2594
#, fuzzy
msgid "Invalid number of volume specifications.\n"
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pactl.c:2606
#, fuzzy
msgid "Inconsistent volume specification.\n"
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr ""

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr ""

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr ""

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr ""

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr ""

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "ਅਢੁੱਕਵਾਂ ਰੀਸੈਂਪਲ ਢੰਗ '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr ""

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "ਲੋਡ ਕਰਨ ਲਈ ਸੈਂਪਲ ਫਾਇਲ ਦਿਓ"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "ਸਾਊਂਡ ਫਾਇਲ ਖੋਲ੍ਹਣ ਲਈ ਫੇਲ੍ਹ ਹੈ।"

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "ਇੱਕ %s ਸਟਰੀਮ ਨੂੰ ਸੈਂਪਲ ਹਦਾਇਤ '%s' ਨਾਲ ਖੋਲ੍ਹਿਆ ਜਾ ਰਿਹਾ ਹੈ।"

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "ਖੇਡਣ ਲਈ ਤੁਹਾਨੂੰ ਸੈਂਪਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "ਹਟਾਉਣ ਲਈ ਤੁਹਾਨੂੰ ਸੈਂਪਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "ਤੁਹਾਨੂੰ ਇੰਪੁੱਟ ਲਿਸਟ ਅਤੇ ਇੱਕ ਸਿੰਕ ਨੂੰ ਸਿੰਕ ਕਰਨਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਸਰੋਤ ਆਉਟਪੁੱਟ ਲਿਸਟ ਅਤੇ ਇੱਕ ਸਰੋਤ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਮੋਡੀਊਲ ਨਾਂ ਅਤੇ ਆਰਗੂਮੈਂਟ ਦੇਣਾ ਪਵੇਗਾ।"

#: src/utils/pactl.c:2889
#, fuzzy
msgid "You have to specify a module index or name"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਮੈਡੀਊਲ ਲਿਸਟ ਦੇਣੀ ਪਵੇਗੀ"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr "ਤੁਸੀਂ ਇੱਕ ਤੋਂ ਵੱਧ ਸਿੰਕ ਨਹੀਂ ਦੇ ਸਕਦੇ। ਤੁਹਾਨੂੰ ਇੱਕ ਬੁਲੀਅਨ ਮੁੱਲ ਦੇਣਾ ਪਵੇਗਾ।"

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
#, fuzzy
msgid "Invalid suspend specification."
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr "ਤੁਸੀਂ ਇੱਕ ਤੋਂ ਵੱਧ ਸਰੋਤ ਨਹੀਂ ਦੇ ਸਕਦੇ। ਤੁਹਾਨੂੰ ਬੁਲੀਅਨ ਮੁੱਲ ਦੇਣਾ ਪਵੇਗਾ।"

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2961
#, fuzzy
msgid "You have to specify a sink name"
msgstr "ਖੇਡਣ ਲਈ ਤੁਹਾਨੂੰ ਸੈਂਪਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:2985
#, fuzzy
msgid "You have to specify a source name"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਮੈਡੀਊਲ ਲਿਸਟ ਦੇਣੀ ਪਵੇਗੀ"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
#, fuzzy
msgid "You have to specify a sink name/index"
msgstr "ਖੇਡਣ ਲਈ ਤੁਹਾਨੂੰ ਸੈਂਪਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
#, fuzzy
msgid "You have to specify a source name/index"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਮੈਡੀਊਲ ਲਿਸਟ ਦੇਣੀ ਪਵੇਗੀ"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "ਤੁਹਾਨੂੰ ਇੰਪੁੱਟ ਲਿਸਟ ਅਤੇ ਇੱਕ ਸਿੰਕ ਨੂੰ ਸਿੰਕ ਕਰਨਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "ਅਢੁੱਕਵੀਂ ਸਿੰਕ ਇੰਪੁੱਟ ਸੂਚੀ"

#: src/utils/pactl.c:3060
#, fuzzy
msgid "You have to specify a source output index and a volume"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਸਰੋਤ ਆਉਟਪੁੱਟ ਲਿਸਟ ਅਤੇ ਇੱਕ ਸਰੋਤ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3065
#, fuzzy
msgid "Invalid source output index"
msgstr "ਅਢੁੱਕਵੀਂ ਸਿੰਕ ਇੰਪੁੱਟ ਸੂਚੀ"

#: src/utils/pactl.c:3086
#, fuzzy
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
#, fuzzy
msgid "Invalid mute specification"
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pactl.c:3111
#, fuzzy
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3126
#, fuzzy
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr "ਤੁਹਾਨੂੰ ਇੰਪੁੱਟ ਲਿਸਟ ਅਤੇ ਇੱਕ ਸਿੰਕ ਨੂੰ ਸਿੰਕ ਕਰਨਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pactl.c:3144
#, fuzzy
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3149
#, fuzzy
msgid "Invalid source output index specification"
msgstr "ਅਢੁੱਕਵਾਂ ਸੈਂਪਲ ਹਦਾਇਤ"

#: src/utils/pactl.c:3162
#, fuzzy
msgid "You have to specify at least an object path and a message name"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""

#: src/utils/pactl.c:3182
#, fuzzy
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3194
#, fuzzy
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "ਤੁਹਾਨੂੰ ਇੱਕ ਕਾਰਡ ਨਾਂ/ਲਿਸਟ ਅਤੇ ਪਰੋਫਾਈਲ ਨਾਂ ਦੇਣਾ ਪਵੇਗਾ"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr ""

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "ਕੋਈ ਯੋਗ ਕਮਾਂਡ ਨਹੀਂ ਦਿੱਤੀ।"

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "ਮੁੜ-ਪ੍ਰਾਪਤੀ ਫੇਲ: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "ਸਸਪੈਂਡ ਕਰਨ ਵਿੱਚ ਫੇਲ: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "ਚੇਤਾਵਨੀ: ਸਾਊਂਡ ਸਰਵਰ ਲੋਕਲ ਨਹੀਂ ਹੈ, ਸਸਪੈਂਡ ਨਹੀਂ ਹੋ ਰਿਹਾ।\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "ਕੁਨੈਕਸ਼ਨ ਫੇਲ: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT ਮਿਲਿਆ, ਬੰਦ ਹੋ ਰਿਹਾ ਹੈ।\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "ਚੇਤਾਵਨੀ: ਚਲਾਈਡ ਪਰੋਸੈੱਸ ਨੂੰ ਸਿਗਨਲ %u ਵਲੋਂ ਬੰਦ ਕੀਤਾ ਗਿਆ ਹੈ\n"

#: src/utils/pasuspender.c:228
#, fuzzy, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"libpulse %s ਨਾਲ ਕੰਪਾਇਲ\n"
"libpulse %s ਨਾਲ ਲਿੰਕ\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() ਫੇਲ੍ਹ ਹੈ।\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() ਫੇਲ੍ਹ ਹੈ।\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() ਫੇਲ੍ਹ ਹੈ।\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "ਕਮਾਂਡ ਲਾਈਨ ਪਾਰਸ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ।\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "ਸਰਵਰ: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "ਸਰੋਤ: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "ਸਿੰਕ: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "ਕੂਕੀਜ਼: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "ਕੂਕੀ ਡਾਟਾ ਪਾਰਸ ਕਰਨ ਵਿੱਚ ਫੇਲ\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "ਕੂਕੀ ਡਾਟਾ ਸੰਭਾਲਣ ਵਿੱਚ ਫੇਲ\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "FQDN ਪ੍ਰਾਪਤ ਕਰਨ ਵਿੱਚ ਫੇਲ।\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "ਕੂਕੀ ਡਾਟਾ ਲੋਡ ਕਰਨ ਵਿੱਚ ਫੇਲ\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "ਹਾਲੇ ਬਣਾਇਆ ਨਹੀਂ।\n"

#~ msgid "Got signal %s."
#~ msgstr "%s ਸਿਗਨਲ ਮਿਲਿਆ ਹੈ।"

#~ msgid "Exiting."
#~ msgstr "ਬੰਦ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ।"

#~ msgid "Found user '%s' (UID %lu) and group '%s' (GID %lu)."
#~ msgstr "ਯੂਜ਼ਰ '%s' (UID %lu) ਅਤੇ ਗਰੁੱਪ '%s' (GID %lu) ਲੱਭੇ ਹਨ।"

#~ msgid "Successfully dropped root privileges."
#~ msgstr "ਰੂਟ ਅਧਿਕਾਰ ਸਫਲਤਾਪੂਰਕ ਹਟਾਏ ਗਏ।"

#~ msgid "setrlimit(%s, (%u, %u)) failed: %s"
#~ msgstr "setrlimit(%s, (%u, %u)) ਫੇਲ੍ਹ ਹੋਇਆ: %s"

#~ msgid "Daemon not running"
#~ msgstr "ਡੈਮਨ ਚੱਲ ਨਹੀਂ ਰਿਹਾ"

#~ msgid "Daemon running as PID %u"
#~ msgstr "ਡੈਮਨ PID %u ਤੌਰ ਤੇ ਚੱਲ ਰਿਹਾ ਹੈ"

#~ msgid "Daemon startup successful."
#~ msgstr "ਡੈਮਨ ਸ਼ੁਰੂਆਤੀ ਸਫ਼ਲ ਹੋਈ।"

#~ msgid "This is PulseAudio %s"
#~ msgstr "ਇਹ ਪਲਸਆਡੀਓ %s ਹੈ"

#~ msgid "Compilation host: %s"
#~ msgstr "ਕੰਪਾਈਲੇਸ਼ਨ ਹੋਸਟ: %s"

#~ msgid "Compilation CFLAGS: %s"
#~ msgstr "ਕੰਪਾਈਲੇਸ਼ਨ CFLAGS: %s"

#~ msgid "Running on host: %s"
#~ msgstr "ਹੋਸਟ ਤੇ ਚੱਲ ਰਿਹਾ ਹੈ: %s"

#~ msgid "Found %u CPUs."
#~ msgstr "%u CPUs ਲੱਭੇ।"

#~ msgid "Page size is %lu bytes"
#~ msgstr "ਪੇਜ਼ ਸਾਈਜ਼ %lu ਬਾਈਟ ਹੈ"

#~ msgid "Compiled with Valgrind support: yes"
#~ msgstr "Valgrind ਸਹਿਯੋਗ ਨਾਲ ਕੰਪਾਈਲ: ਹਾਂ"

#~ msgid "Compiled with Valgrind support: no"
#~ msgstr "Valgrind ਸਹਿਯੋਗ ਨਾਲ ਕੰਪਾਈਲ: ਨਹੀਂ"

#~ msgid "Running in valgrind mode: %s"
#~ msgstr "Valgrind ਮੋਡ ਵਿੱਚ ਚੱਲ ਰਿਹਾ ਹੈ: %s"

#, fuzzy
#~ msgid "Running in VM: %s"
#~ msgstr "ਹੋਸਟ ਤੇ ਚੱਲ ਰਿਹਾ ਹੈ: %s"

#~ msgid "Optimized build: yes"
#~ msgstr "ਓਪਟੀਮਾਈਜ਼ਡ ਬਿਲਡ: ਹਾਂ"

#~ msgid "Optimized build: no"
#~ msgstr "ਓਪਟੀਮਾਈਜ਼ਡ ਬਿਲਡ: ਨਹੀਂ"

#~ msgid "NDEBUG defined, all asserts disabled."
#~ msgstr "NDEBUG ਪਰਿਭਾਸ਼ਤ, ਸਭ asserts ਅਯੋਗ ਹਨ।"

#~ msgid "FASTPATH defined, only fast path asserts disabled."
#~ msgstr "FASTPATH ਪਰਿਭਾਸ਼ਤ, ਸਿਰਫ ਫਾਸਟ ਪਾਥ asserts ਅਯੋਗ ਹਨ।"

#~ msgid "All asserts enabled."
#~ msgstr "ਸਭ asserts ਯੋਗ ਕੀਤੇ ਹਨ।"

#~ msgid "Machine ID is %s."
#~ msgstr "ਮਸ਼ੀਨ ID %s ਹੈ।"

#~ msgid "Session ID is %s."
#~ msgstr "ਸ਼ੈਸ਼ਨ ID %s ਹੈ।"

#~ msgid "Using runtime directory %s."
#~ msgstr "ਰਨਟਾਈਮ ਡਾਇਰੈਕਟਰੀ %s ਦੀ ਵਰਤੋਂ।"

#~ msgid "Using state directory %s."
#~ msgstr "ਸਟੇਟ ਡਾਇਰੈਕਟਰੀ %s ਦੀ ਵਰਤੋਂ।"

#~ msgid "Using modules directory %s."
#~ msgstr "ਮੈਡਿਊਲ ਡਾਇਰੈਕਟਰੀ %s ਦੀ ਵਰਤੋਂ।"

#~ msgid "Running in system mode: %s"
#~ msgstr "ਸਿਸਟਮ ਮੋਡ ਵਿੱਚ ਚੱਲ ਰਿਹਾ ਹੈ: %s"

#~ msgid "Fresh high-resolution timers available! Bon appetit!"
#~ msgstr "ਤਾਜ਼ੀ ਹਾਈ-ਰੈਜ਼ੋਲੂਸ਼ਨ ਟਾਈਮਰ ਉਪਲੱਬਧ ਹੈ! Bon appetit!"

#~ msgid ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"
#~ msgstr ""
#~ "ਮਿੱਤਰਾ, ਤੇਰਾ ਕਰਨਲ ਪੁਰਾਣਾ ਹੈ! ਚੀਫ ਦੀ ਅੱਜ ਦੀ ਸਿਫਾਰਸ਼ ਹਾਈ-ਰੈਜ਼ੋਲੂਸ਼ਨ ਟਾਈਮਰ ਯੋਗ ਨਾਲ ਲੀਨਕਸ "
#~ "ਹੈ!"

#~ msgid "Failed to initialize daemon."
#~ msgstr "ਡੈਮਨ ਸ਼ੁਰੂ ਕਰਨ ਵਿੱਚ ਫੇਲ੍ਹ।"

#~ msgid "Daemon startup complete."
#~ msgstr "ਡੈਮਨ ਸ਼ੁਰੂਆਤੀ ਮੁਕੰਮਲ।"

#~ msgid "Daemon shutdown initiated."
#~ msgstr "ਡੈਮਨ ਬੰਦ ਕਰਨਾ ਸ਼ੁਰੂ ਹੋ ਗਿਆ।"

#~ msgid "Daemon terminated."
#~ msgstr "ਡੈਮਨ ਬੰਦ ਹੋ ਗਿਆ।"

#~ msgid "Cleaning up privileges."
#~ msgstr "ਅਧਿਕਾਰ ਹਟਾ ਰਿਹਾ ਹੈ।"

#, fuzzy
#~ msgid "PulseAudio Sound System KDE Routing Policy"
#~ msgstr "ਪਲਸਆਡੀਓ ਸਾਊਂਡ ਸਿਸਟਮ"

#, fuzzy
#~ msgid "Start the PulseAudio Sound System with KDE Routing Policy"
#~ msgstr "ਪਲਸਆਡੀਓ ਸਾਊਂਡ ਸਿਸਟਮ ਚਲਾਓ"

#~ msgid "No cookie loaded. Attempting to connect without."
#~ msgstr "ਕੋਈ ਕੂਕੀ ਲੋਡ ਨਹੀਂ ਕੀਤੀ। ਇਸ ਤੋਂ ਬਿਨਾਂ ਕੁਨੈਕਟ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ।"

#~ msgid "Failed to load client configuration file.\n"
#~ msgstr "ਕਲਾਇਟ ਸੰਰਚਨਾ ਫਾਇਲ ਲੋਡ ਕਰਨ ਵਿੱਚ ਫੇਲ।\n"

#~ msgid "Failed to read environment configuration data.\n"
#~ msgstr "ਇੰਵਾਇਰਨਮੈਂਟ ਸੰਰਚਨਾ ਡਾਟਾ ਪੜ੍ਹਨ ਵਿੱਚ ਫੇਲ੍ਹ।\n"

#~ msgid "Telephony Duplex (HSP/HFP)"
#~ msgstr "ਟੈਲੀਫੋਨੀ ਡੁਪਲੈਕਸ (HSP/HFP)"

#~ msgid "LFE on Separate Mono Output"
#~ msgstr "ਐਨਾਲਾਗ ਆਊਟਪੁੱਟ (LFE)"

#, fuzzy
#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "ਡਿਜ਼ੀਟਲ ਸਟੀਰੀਓ (HDMI)"

#, fuzzy
#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "ਡਿਜ਼ੀਟਲ ਸਟੀਰੀਓ (IEC958)"

#~ msgid "[%s:%u] rlimit not supported on this platform."
#~ msgstr "[%s:%u] rlimit ਨੂੰ ਇਸ ਪਲੇਟਫਾਰਮ ਤੇ ਸਹਿਯੋਗ ਨਹੀਂ ਹੈ।"

#~ msgid "XOpenDisplay() failed"
#~ msgstr "XOpenDisplay() ਫੇਲ੍ਹ ਹੈ"

#~ msgid ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"
#~ msgstr ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"

#, fuzzy
#~ msgid ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "%s [options] subscribe\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ msgstr ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"

#~ msgid "%s+%s"
#~ msgstr "%s+%s"

#~ msgid "%s / %s"
#~ msgstr "%s / %s"

#~ msgid "Digital Surround 4.0 (IEC958)"
#~ msgstr "ਡਿਜ਼ੀਟਲ ਸਰਾਊਂਡ 4.0 (IEC958)"

#~ msgid "Low Frequency Emmiter"
#~ msgstr "ਘੱਟ ਫਰੀਕਿਊਂਸੀ ਇੱਮਟਰ"
