# German translation of pulseaudio
# Copyright (C) 2008 pulseaudio
# This file is distributed under the same license as the pulseaudio package.
#
# <PERSON><PERSON> <<EMAIL>>, 2008, 2009.
# <PERSON> <<EMAIL>>, 2008-2009, 2012.
#
msgid ""
msgstr ""
"Project-Id-Version: pulseaudio\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/pulseaudio/pulseaudio/"
"issues/new\n"
"POT-Creation-Date: 2022-06-18 09:49+0300\n"
"PO-Revision-Date: 2012-01-30 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Language: German\n"

#: src/daemon/cmdline.c:113
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"\n"
"COMMANDS:\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"      --dump-conf                       Dump default configuration\n"
"      --dump-modules                    Dump list of available modules\n"
"      --dump-resample-methods           Dump available resample methods\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Start the daemon if it is not "
"running\n"
"  -k  --kill                            Kill a running daemon\n"
"      --check                           Check for a running daemon (only "
"returns exit code)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Run as system-wide instance\n"
"  -D, --daemonize[=BOOL]                Daemonize after startup\n"
"      --fail[=BOOL]                     Quit when startup fails\n"
"      --high-priority[=BOOL]            Try to set high nice level\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Try to enable realtime scheduling\n"
"                                        (only available as root, when SUID "
"or\n"
"                                        with elevated RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Disallow user requested module\n"
"                                        loading/unloading after startup\n"
"      --disallow-exit[=BOOL]            Disallow user requested exit\n"
"      --exit-idle-time=SECS             Terminate the daemon when idle and "
"this\n"
"                                        time passed\n"
"      --scache-idle-time=SECS           Unload autoloaded samples when idle "
"and\n"
"                                        this time passed\n"
"      --log-level[=LEVEL]               Increase or set verbosity level\n"
"  -v  --verbose                         Increase the verbosity level\n"
"      --log-target={auto,syslog,stderr,file:PATH,newfile:PATH}\n"
"                                        Specify the log target\n"
"      --log-meta[=BOOL]                 Include code location in log "
"messages\n"
"      --log-time[=BOOL]                 Include timestamps in log messages\n"
"      --log-backtrace=FRAMES            Include a backtrace in log messages\n"
"  -p, --dl-search-path=PATH             Set the search path for dynamic "
"shared\n"
"                                        objects (plugins)\n"
"      --resample-method=METHOD          Use the specified resampling method\n"
"                                        (See --dump-resample-methods for\n"
"                                        possible values)\n"
"      --use-pid-file[=BOOL]             Create a PID file\n"
"      --no-cpu-limit[=BOOL]             Do not install CPU load limiter on\n"
"                                        platforms that support it.\n"
"      --disable-shm[=BOOL]              Disable shared memory support.\n"
"      --enable-memfd[=BOOL]             Enable memfd shared memory support.\n"
"\n"
"STARTUP SCRIPT:\n"
"  -L, --load=\"MODULE ARGUMENTS\"         Load the specified plugin module "
"with\n"
"                                        the specified argument\n"
"  -F, --file=FILENAME                   Run the specified script\n"
"  -C                                    Open a command line on the running "
"TTY\n"
"                                        after startup\n"
"\n"
"  -n                                    Don't load default script file\n"
msgstr ""
"%s [options]\n"
"\n"
"BEFEHLE:\n"
"  -h, --help                            Zeige diese Hilfe\n"
"      --version                         Zeige Version\n"
"      --dump-conf                       Zeige Standardkonfiguration\n"
"      --dump-modules                    Zeige Liste verfügbarer Module\n"
"      --dump-resample-methods           Zeige verfügbare Resample-Methoden\n"
"      --cleanup-shm                     Cleanup stale shared memory "
"segments\n"
"      --start                           Starte Daemon, falls noch nicht "
"geschehen\n"
"  -k  --kill                            Laufenden Daemon beenden\n"
"      --check                           Prüfe laufende Daemone (gibt nur "
"einen Exit-Code zurück)\n"
"\n"
"OPTIONS:\n"
"      --system[=BOOL]                   Als systemweite Instanz ausführen\n"
"  -D, --daemonize[=BOOL]                Nach Start zum Daemon machen\n"
"      --fail[=BOOL]                     Beenden, wenn Start fehlschlägt\n"
"      --high-priority[=BOOL]            Nutze höchste Priorität\n"
"                                        (Nur verfügbar als root, wenn SUID "
"oder\n"
"                                        mit erhöhtem RLIMIT_NICE)\n"
"      --realtime[=BOOL]                 Versuche, Echtzeit-Scheduling zu "
"aktivieren\n"
"                                        (Nur verfügbar als root, wenn SUID "
"oder\n"
"                                        mit erhöhtem RLIMIT_RTPRIO)\n"
"      --disallow-module-loading[=BOOL]  Verbiete (Ent-)laden durch Nutzer "
"angeforderter\n"
"                                        Module nach dem Start\n"
"      --disallow-exit[=BOOL]            Verbiete Beenden auf Anfrage des "
"Nutzers\n"
"      --exit-idle-time=SECS             Beende Daemon, wenn für diese Zeit \n"
"                                        untätig\n"
"      --module-idle-time=SECS           Entlade untätige Module nach dieser "
"Zeit\n"
"      --scache-idle-time=SECS           Entlade untätige automatisch "
"geladene \n"
"                                        Samples nach dieser Zeit\n"
"      --log-level[=STUFE]               Grad der Ausführlichkeit angeben\n"
"  -v                                    Ausführliche Meldungen\n"
"      --log-target={auto,syslog,stderr} Protokoll-Ziel angeben\n"
"  -p, --dl-search-path=PFAD             Suchpfad für dynamisch "
"freigegebene \n"
"                                        Objekte (Plugins)\n"
"      --resample-method=METHODE          Nutze diese Resampling-Methode\n"
"                                        (Siehe --dump-resample-methods für\n"
"                                        mögliche Werte)\n"
"      --use-pid-file[=BOOL]             Eine PID-Datei erstellen\n"
"      --no-cpu-limit[=BOOL]             CPU-Lastbegrenzung auf "
"unterstützten\n"
"                                        Systemen nicht installieren.\n"
"      --disable-shm[=BOOL]              Keine Unterstützung für Shared "
"Memory.\n"
"\n"
"STARTUP-SCRIPT:\n"
"  -L, --load=\"MODUL-ARGUMENTE\"       Plugin-Modul mit diesen Parametern \n"
"                                        laden.\n"
"  -F, --file=DATEINAMEN                   Dieses Skript ausführen\n"
"  -C                                    Nach Start auf laufendem TTY \n"
"                                        eine Kommandozeile öffnen\n"
"\n"
"  -n                                    Standardskript nicht laden\n"

#: src/daemon/cmdline.c:246
msgid "--daemonize expects boolean argument"
msgstr "Option --daemonize erfordert bool'schen Wert"

#: src/daemon/cmdline.c:254
msgid "--fail expects boolean argument"
msgstr "Option --fail erfordert bool'schen Wert"

#: src/daemon/cmdline.c:265
#, fuzzy
msgid ""
"--log-level expects log level argument (either numeric in range 0..4 or one "
"of error, warn, notice, info, debug)."
msgstr ""
"--log-level erfordert Wert für Grad der Protokollierung (entweder numerisch "
"im Bereich 0..4 or einen dieser: debug, info, notice, warn, error)."

#: src/daemon/cmdline.c:277
msgid "--high-priority expects boolean argument"
msgstr "Option --high-priority erfordert bool'schen Wert"

#: src/daemon/cmdline.c:285
msgid "--realtime expects boolean argument"
msgstr "Option --realtime erfordert bool'schen Wert"

#: src/daemon/cmdline.c:293
msgid "--disallow-module-loading expects boolean argument"
msgstr "Option --disallow-module-loading erfordert bool'schen Wert"

#: src/daemon/cmdline.c:301
msgid "--disallow-exit expects boolean argument"
msgstr "--disallow-exit erfordert boolsches Argument"

#: src/daemon/cmdline.c:309
msgid "--use-pid-file expects boolean argument"
msgstr "Option --use-pid-file erfordert bool'schen Wert"

#: src/daemon/cmdline.c:328
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'journal', 'stderr' or 'auto' or a "
"valid file name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Ungültiges Log-Ziel: Benutzen Sie entweder 'syslog', 'stderr' oder 'auto'."

#: src/daemon/cmdline.c:330
#, fuzzy
msgid ""
"Invalid log target: use either 'syslog', 'stderr' or 'auto' or a valid file "
"name 'file:<path>', 'newfile:<path>'."
msgstr ""
"Ungültiges Log-Ziel: Benutzen Sie entweder 'syslog', 'stderr' oder 'auto'."

#: src/daemon/cmdline.c:338
msgid "--log-time expects boolean argument"
msgstr "--realtime erfordert boolsches Argument"

#: src/daemon/cmdline.c:346
msgid "--log-meta expects boolean argument"
msgstr "--log-meta erfordert boolschen Wert"

#: src/daemon/cmdline.c:366
#, c-format
msgid "Invalid resample method '%s'."
msgstr "Ungültige Resample-Methode '%s'."

#: src/daemon/cmdline.c:373
msgid "--system expects boolean argument"
msgstr "--System erwartet Boolean-Argument"

#: src/daemon/cmdline.c:381
msgid "--no-cpu-limit expects boolean argument"
msgstr "Option --no-cpu-limit erfordert bool'schen Wert"

#: src/daemon/cmdline.c:389
msgid "--disable-shm expects boolean argument"
msgstr "Option --disable-shm erfordert bool'schen Wert"

#: src/daemon/cmdline.c:397
#, fuzzy
msgid "--enable-memfd expects boolean argument"
msgstr "Option --realtime erfordert bool'schen Wert"

#: src/daemon/daemon-conf.c:270
#, c-format
msgid "[%s:%u] Invalid log target '%s'."
msgstr "[%s:%u] Ungültiges Log-Ziel '%s'."

#: src/daemon/daemon-conf.c:285
#, c-format
msgid "[%s:%u] Invalid log level '%s'."
msgstr "[%s:%u] Ungültige Log-Stufe '%s'."

#: src/daemon/daemon-conf.c:300
#, c-format
msgid "[%s:%u] Invalid resample method '%s'."
msgstr "[%s:%u] Ungültige Resample-Methode '%s'."

#: src/daemon/daemon-conf.c:322
#, c-format
msgid "[%s:%u] Invalid rlimit '%s'."
msgstr "[%s:%u] Ungültiges rlimit '%s'."

#: src/daemon/daemon-conf.c:342
#, c-format
msgid "[%s:%u] Invalid sample format '%s'."
msgstr "[%s:%u] Ungültiges Sample-Format '%s'."

#: src/daemon/daemon-conf.c:359 src/daemon/daemon-conf.c:376
#, c-format
msgid "[%s:%u] Invalid sample rate '%s'."
msgstr "[%s:%u] Ungültige Sample-Rate '%s'."

#: src/daemon/daemon-conf.c:399
#, c-format
msgid "[%s:%u] Invalid sample channels '%s'."
msgstr "[%s:%u] Ungültige Sample-Kanäle '%s'."

#: src/daemon/daemon-conf.c:416
#, c-format
msgid "[%s:%u] Invalid channel map '%s'."
msgstr "[%s:%u] Ungültige Kanal-Zuordnung '%s'."

#: src/daemon/daemon-conf.c:433
#, c-format
msgid "[%s:%u] Invalid number of fragments '%s'."
msgstr "[%s:%u] Ungültige Anzahl von Fragmenten '%s'."

#: src/daemon/daemon-conf.c:450
#, c-format
msgid "[%s:%u] Invalid fragment size '%s'."
msgstr "[%s:%u] Ungültige Fragmentgröße '%s'."

#: src/daemon/daemon-conf.c:467
#, c-format
msgid "[%s:%u] Invalid nice level '%s'."
msgstr "[%s:%u] Ungültige Nice-Stufe '%s'."

#: src/daemon/daemon-conf.c:552
#, fuzzy, c-format
msgid "[%s:%u] Invalid server type '%s'."
msgstr "[%s:%u] Ungültige Sample-Rate '%s'."

#: src/daemon/daemon-conf.c:685
#, c-format
msgid "Failed to open configuration file: %s"
msgstr "Öffnen der Konfigurationsdatei fehlgeschlagen : %s"

#: src/daemon/daemon-conf.c:701
msgid ""
"The specified default channel map has a different number of channels than "
"the specified default number of channels."
msgstr ""
"Die angegebene Standard-Kanalzuordnung hat eine andere Anzahl von Kanälen "
"als die angegebene Standard-Kanal-Anzahl."

#: src/daemon/daemon-conf.c:788
#, c-format
msgid "### Read from configuration file: %s ###\n"
msgstr "### Lese von Konfigurationsdatei: %s ###\n"

#: src/daemon/dumpmodules.c:57
#, c-format
msgid "Name: %s\n"
msgstr "Name: %s\n"

#: src/daemon/dumpmodules.c:60
#, c-format
msgid "No module information available\n"
msgstr "Keine Modul-Informationen verfügbar\n"

#: src/daemon/dumpmodules.c:63
#, c-format
msgid "Version: %s\n"
msgstr "Version: %s\n"

#: src/daemon/dumpmodules.c:65
#, c-format
msgid "Description: %s\n"
msgstr "Beschreibung: %s\n"

#: src/daemon/dumpmodules.c:67
#, c-format
msgid "Author: %s\n"
msgstr "Autor: %s\n"

#: src/daemon/dumpmodules.c:69
#, c-format
msgid "Usage: %s\n"
msgstr "Verwendung: %s\n"

#: src/daemon/dumpmodules.c:70
#, c-format
msgid "Load Once: %s\n"
msgstr "Lade einmalig: %s\n"

#: src/daemon/dumpmodules.c:72
#, fuzzy, c-format
msgid "DEPRECATION WARNING: %s\n"
msgstr "DEPRECATION WARNING: %s\n"

#: src/daemon/dumpmodules.c:76
#, c-format
msgid "Path: %s\n"
msgstr "Pfad: %s\n"

#: src/daemon/ltdl-bind-now.c:75
#, fuzzy, c-format
msgid "Failed to open module %s: %s"
msgstr "Öffnen der Datei '%s' fehlgeschlagen\n"

#: src/daemon/ltdl-bind-now.c:126
msgid "Failed to find original lt_dlopen loader."
msgstr "Ursprünglicher dlopen-Loader konnte nicht gefunden werden."

#: src/daemon/ltdl-bind-now.c:131
msgid "Failed to allocate new dl loader."
msgstr "Neuer dlopen-Loader konnte nicht gefunden werden."

#: src/daemon/ltdl-bind-now.c:144
msgid "Failed to add bind-now-loader."
msgstr "Hinzufügen von Bind-Now-Loader fehlgeschlagen."

#: src/daemon/main.c:265
#, c-format
msgid "Failed to find user '%s'."
msgstr "Benutzer '%s' nicht gefunden."

#: src/daemon/main.c:270
#, c-format
msgid "Failed to find group '%s'."
msgstr "Gruppe '%s' nicht gefunden."

#: src/daemon/main.c:279
#, c-format
msgid "GID of user '%s' and of group '%s' don't match."
msgstr "GID von Benutzer '%s' und Gruppe '%s' stimmen nicht überein."

#: src/daemon/main.c:284
#, c-format
msgid "Home directory of user '%s' is not '%s', ignoring."
msgstr "Benutzerverzeichnis von Benutzer '%s' ist nicht '%s', ignoriere."

#: src/daemon/main.c:287 src/daemon/main.c:292
#, c-format
msgid "Failed to create '%s': %s"
msgstr "Konnte '%s' nciht erzeugen: %s"

#: src/daemon/main.c:299
#, c-format
msgid "Failed to change group list: %s"
msgstr "Wechseln der Gruppen-Liste fehlgeschlagen: %s"

#: src/daemon/main.c:315
#, c-format
msgid "Failed to change GID: %s"
msgstr "Wechseln der GID fehlgeschlagen: %s"

#: src/daemon/main.c:331
#, c-format
msgid "Failed to change UID: %s"
msgstr "Wechseln der UID fehlgeschlagen: %s"

#: src/daemon/main.c:360
msgid "System wide mode unsupported on this platform."
msgstr "System-Modus auf dieser Plattform nicht unterstützt."

#: src/daemon/main.c:650
msgid "Failed to parse command line."
msgstr "Parsen der Kommandzeile fehlgeschlagen."

#: src/daemon/main.c:689
msgid ""
"System mode refused for non-root user. Only starting the D-Bus server lookup "
"service."
msgstr ""

#: src/daemon/main.c:788
#, c-format
msgid "Failed to kill daemon: %s"
msgstr "Konnte Prozess nicht abbrechen: %s"

#: src/daemon/main.c:817
msgid ""
"This program is not intended to be run as root (unless --system is "
"specified)."
msgstr ""
"Dieses Programm sollte ohne die Option --system nicht als Administrator "
"ausgeführt werden."

#: src/daemon/main.c:820
msgid "Root privileges required."
msgstr "Root-Berechtigungen benötigt."

#: src/daemon/main.c:827
msgid "--start not supported for system instances."
msgstr "--start nicht unterstützt für System-Instanzen."

#: src/daemon/main.c:867
#, c-format
msgid "User-configured server at %s, refusing to start/autospawn."
msgstr ""

#: src/daemon/main.c:873
#, c-format
msgid ""
"User-configured server at %s, which appears to be local. Probing deeper."
msgstr ""

#: src/daemon/main.c:878
#, fuzzy
msgid "Running in system mode, but --disallow-exit not set."
msgstr "System-Modus aktiv, jeodch --disallow-exit nicht gesetzt!"

#: src/daemon/main.c:881
#, fuzzy
msgid "Running in system mode, but --disallow-module-loading not set."
msgstr "System-Modus aktiv, jedoch --disallow-module-loading nicht gesetzt!"

#: src/daemon/main.c:884
#, fuzzy
msgid "Running in system mode, forcibly disabling SHM mode."
msgstr "System-Modus aktiv, SHM-Modus gezwungenermaßen deaktiviert!"

#: src/daemon/main.c:889
#, fuzzy
msgid "Running in system mode, forcibly disabling exit idle time."
msgstr "System-Modus aktiv, Exit-Idle-Time gezwungenermaßen deaktiviert!"

#: src/daemon/main.c:922
msgid "Failed to acquire stdio."
msgstr "Reservieren von STDIO fehlgeschlagen."

#: src/daemon/main.c:928 src/daemon/main.c:999
#, fuzzy, c-format
msgid "pipe() failed: %s"
msgstr "pipe fehlgeschlagen: %s"

#: src/daemon/main.c:933 src/daemon/main.c:1004
#, c-format
msgid "fork() failed: %s"
msgstr "fork() fehlgeschlagen: %s"

#: src/daemon/main.c:948 src/daemon/main.c:1019 src/utils/pacat.c:562
#, c-format
msgid "read() failed: %s"
msgstr "read() fehlgeschlagen: %s"

#: src/daemon/main.c:954
msgid "Daemon startup failed."
msgstr "Start des Daemons fehlgeschlagen."

#: src/daemon/main.c:987
#, fuzzy, c-format
msgid "setsid() failed: %s"
msgstr "read() fehlgeschlagen: %s"

#: src/daemon/main.c:1119
msgid "Failed to get machine ID"
msgstr "Beziehen der Maschinen-ID fehlgeschlagen"

#: src/daemon/main.c:1145
msgid ""
"OK, so you are running PA in system mode. Please make sure that you actually "
"do want to do that.\n"
"Please read http://www.freedesktop.org/wiki/Software/PulseAudio/"
"Documentation/User/WhatIsWrongWithSystemWide/ for an explanation why system "
"mode is usually a bad idea."
msgstr ""

#: src/daemon/main.c:1161
msgid "pa_pid_file_create() failed."
msgstr "pa_pid_file_create() fehlgeschlagen."

#: src/daemon/main.c:1193
msgid "pa_core_new() failed."
msgstr "pa_core_new() fehlgeschlagen."

#: src/daemon/main.c:1268
#, fuzzy
msgid "command line arguments"
msgstr "Zu viele Argumente."

#: src/daemon/main.c:1275
#, c-format
msgid ""
"Failed to initialize daemon due to errors while executing startup commands. "
"Source of commands: %s"
msgstr ""

#: src/daemon/main.c:1280
msgid "Daemon startup without any loaded modules, refusing to work."
msgstr "Daemon verweigert Ausführung, da keine Module geladen."

#: src/daemon/pulseaudio.desktop.in:4
msgid "PulseAudio Sound System"
msgstr "PulseAudio Sound System"

#: src/daemon/pulseaudio.desktop.in:5
msgid "Start the PulseAudio Sound System"
msgstr "Das PulseAudio Sound System starten"

#: src/modules/alsa/alsa-mixer.c:2708
#, fuzzy
msgid "Input"
msgstr "Eingang %s"

#: src/modules/alsa/alsa-mixer.c:2709
msgid "Docking Station Input"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2710
#, fuzzy
msgid "Docking Station Microphone"
msgstr "Internes Audio"

#: src/modules/alsa/alsa-mixer.c:2711
msgid "Docking Station Line In"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2712 src/modules/alsa/alsa-mixer.c:2803
msgid "Line In"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2713 src/modules/alsa/alsa-mixer.c:2797
#: src/modules/bluetooth/module-bluez5-device.c:1956
msgid "Microphone"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2714 src/modules/alsa/alsa-mixer.c:2798
#, fuzzy
msgid "Front Microphone"
msgstr "Internes Audio"

#: src/modules/alsa/alsa-mixer.c:2715 src/modules/alsa/alsa-mixer.c:2799
#, fuzzy
msgid "Rear Microphone"
msgstr "Internes Audio"

#: src/modules/alsa/alsa-mixer.c:2716
msgid "External Microphone"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2717 src/modules/alsa/alsa-mixer.c:2801
#, fuzzy
msgid "Internal Microphone"
msgstr "Internes Audio"

#: src/modules/alsa/alsa-mixer.c:2718 src/modules/alsa/alsa-mixer.c:2804
#: src/utils/pactl.c:343
msgid "Radio"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2719 src/modules/alsa/alsa-mixer.c:2805
#: src/utils/pactl.c:344
msgid "Video"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2720
msgid "Automatic Gain Control"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2721
msgid "No Automatic Gain Control"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2722
msgid "Boost"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2723
msgid "No Boost"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2724
msgid "Amplifier"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2725
msgid "No Amplifier"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2726
msgid "Bass Boost"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2727
msgid "No Bass Boost"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2728
#: src/modules/bluetooth/module-bluez5-device.c:1964 src/utils/pactl.c:333
msgid "Speaker"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2729 src/modules/alsa/alsa-mixer.c:2807
#: src/utils/pactl.c:334
#, fuzzy
msgid "Headphones"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:2796
#, fuzzy
msgid "Analog Input"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:2800
msgid "Dock Microphone"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2802
#, fuzzy
msgid "Headset Microphone"
msgstr "Internes Audio"

#: src/modules/alsa/alsa-mixer.c:2806
#, fuzzy
msgid "Analog Output"
msgstr "Ausgang %s"

#: src/modules/alsa/alsa-mixer.c:2808
#, fuzzy
msgid "Headphones 2"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:2809
#, fuzzy
msgid "Headphones Mono Output"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:2810
msgid "Line Out"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2811
#, fuzzy
msgid "Analog Mono Output"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:2812
#, fuzzy
msgid "Speakers"
msgstr "Analog Stereo"

#: src/modules/alsa/alsa-mixer.c:2813
msgid "HDMI / DisplayPort"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2814
#, fuzzy
msgid "Digital Output (S/PDIF)"
msgstr "Digital Stereo (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2815
#, fuzzy
msgid "Digital Input (S/PDIF)"
msgstr "Digital Stereo (HDMI)"

#: src/modules/alsa/alsa-mixer.c:2816
msgid "Multichannel Input"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:2817
#, fuzzy
msgid "Multichannel Output"
msgstr "Ausgang %s"

#: src/modules/alsa/alsa-mixer.c:2818
#, fuzzy
msgid "Game Output"
msgstr "Ausgang %s"

#: src/modules/alsa/alsa-mixer.c:2819 src/modules/alsa/alsa-mixer.c:2820
#, fuzzy
msgid "Chat Output"
msgstr "Ausgang %s"

#: src/modules/alsa/alsa-mixer.c:2821
#, fuzzy
msgid "Chat Input"
msgstr "Eingang %s"

#: src/modules/alsa/alsa-mixer.c:2822
#, fuzzy
msgid "Virtual Surround 7.1"
msgstr "Analog Surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4563
msgid "Analog Mono"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:4564
#, fuzzy
msgid "Analog Mono (Left)"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:4565
#, fuzzy
msgid "Analog Mono (Right)"
msgstr "Analog Mono"

#. Note: Not translated to "Analog Stereo Input", because the source
#. * name gets "Input" appended to it automatically, so adding "Input"
#. * here would lead to the source name to become "Analog Stereo Input
#. * Input". The same logic applies to analog-stereo-output,
#. * multichannel-input and multichannel-output.
#: src/modules/alsa/alsa-mixer.c:4566 src/modules/alsa/alsa-mixer.c:4574
#: src/modules/alsa/alsa-mixer.c:4575
msgid "Analog Stereo"
msgstr "Analog Stereo"

#: src/modules/alsa/alsa-mixer.c:4567 src/pulse/channelmap.c:103
#: src/pulse/channelmap.c:770
msgid "Mono"
msgstr "Mono"

#: src/modules/alsa/alsa-mixer.c:4568 src/pulse/channelmap.c:774
msgid "Stereo"
msgstr "Stereo"

#: src/modules/alsa/alsa-mixer.c:4576 src/modules/alsa/alsa-mixer.c:4734
#: src/modules/bluetooth/module-bluez5-device.c:1944 src/utils/pactl.c:337
msgid "Headset"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4577 src/modules/alsa/alsa-mixer.c:4735
#, fuzzy
msgid "Speakerphone"
msgstr "Analog Stereo"

#: src/modules/alsa/alsa-mixer.c:4578 src/modules/alsa/alsa-mixer.c:4579
msgid "Multichannel"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4580
#, fuzzy
msgid "Analog Surround 2.1"
msgstr "Analog Surround 4.1"

#: src/modules/alsa/alsa-mixer.c:4581
#, fuzzy
msgid "Analog Surround 3.0"
msgstr "Analog Surround 4.0"

#: src/modules/alsa/alsa-mixer.c:4582
#, fuzzy
msgid "Analog Surround 3.1"
msgstr "Analog Surround 4.1"

#: src/modules/alsa/alsa-mixer.c:4583
msgid "Analog Surround 4.0"
msgstr "Analog Surround 4.0"

#: src/modules/alsa/alsa-mixer.c:4584
msgid "Analog Surround 4.1"
msgstr "Analog Surround 4.1"

#: src/modules/alsa/alsa-mixer.c:4585
msgid "Analog Surround 5.0"
msgstr "Analog Surround 5.0"

#: src/modules/alsa/alsa-mixer.c:4586
msgid "Analog Surround 5.1"
msgstr "Analog Surround 5.1"

#: src/modules/alsa/alsa-mixer.c:4587
#, fuzzy
msgid "Analog Surround 6.0"
msgstr "Analog Surround 4.0"

#: src/modules/alsa/alsa-mixer.c:4588
#, fuzzy
msgid "Analog Surround 6.1"
msgstr "Analog Surround 4.1"

#: src/modules/alsa/alsa-mixer.c:4589
#, fuzzy
msgid "Analog Surround 7.0"
msgstr "Analog Surround 4.0"

#: src/modules/alsa/alsa-mixer.c:4590
msgid "Analog Surround 7.1"
msgstr "Analog Surround 7.1"

#: src/modules/alsa/alsa-mixer.c:4591
msgid "Digital Stereo (IEC958)"
msgstr "Digital Stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4592
msgid "Digital Surround 4.0 (IEC958/AC3)"
msgstr "Digital Surround 4.0 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4593
msgid "Digital Surround 5.1 (IEC958/AC3)"
msgstr "Digital Surround 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4594
#, fuzzy
msgid "Digital Surround 5.1 (IEC958/DTS)"
msgstr "Digital Surround 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4595
msgid "Digital Stereo (HDMI)"
msgstr "Digital Stereo (HDMI)"

#: src/modules/alsa/alsa-mixer.c:4596
#, fuzzy
msgid "Digital Surround 5.1 (HDMI)"
msgstr "Digital Surround 5.1 (IEC958/AC3)"

#: src/modules/alsa/alsa-mixer.c:4597
msgid "Chat"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4598
msgid "Game"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4732
#, fuzzy
msgid "Analog Mono Duplex"
msgstr "Analog Mono"

#: src/modules/alsa/alsa-mixer.c:4733
#, fuzzy
msgid "Analog Stereo Duplex"
msgstr "Analog Stereo"

#: src/modules/alsa/alsa-mixer.c:4736
#, fuzzy
msgid "Digital Stereo Duplex (IEC958)"
msgstr "Digital Stereo (IEC958)"

#: src/modules/alsa/alsa-mixer.c:4737
msgid "Multichannel Duplex"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4738
#, fuzzy
msgid "Stereo Duplex"
msgstr "Analog Stereo"

#: src/modules/alsa/alsa-mixer.c:4739
msgid "Mono Chat + 7.1 Surround"
msgstr ""

#: src/modules/alsa/alsa-mixer.c:4740 src/modules/alsa/module-alsa-card.c:197
#: src/modules/bluetooth/module-bluez5-device.c:2263
msgid "Off"
msgstr "Aus"

#: src/modules/alsa/alsa-mixer.c:4840
#, fuzzy, c-format
msgid "%s Output"
msgstr "Ausgang %s"

#: src/modules/alsa/alsa-mixer.c:4848
#, fuzzy, c-format
msgid "%s Input"
msgstr "Eingang %s"

#: src/modules/alsa/alsa-sink.c:672 src/modules/alsa/alsa-sink.c:862
#, c-format
msgid ""
"ALSA woke us up to write new data to the device, but there was actually "
"nothing to write.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLOUT set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""

#: src/modules/alsa/alsa-source.c:636 src/modules/alsa/alsa-source.c:802
#, c-format
msgid ""
"ALSA woke us up to read new data from the device, but there was actually "
"nothing to read.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers.\n"
"We were woken up with POLLIN set -- however a subsequent snd_pcm_avail() "
"returned 0 or another value < min_avail."
msgstr ""

#: src/modules/alsa/alsa-util.c:1183 src/modules/alsa/alsa-util.c:1277
#, fuzzy, c-format
msgid ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu byte (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_avail() returned a value that is exceptionally large: %lu bytes (%lu "
"ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_avail() gibt einen Wert zurück, welche außerordentlich groß ist: %lu "
"bytes (%lu ms).\n"
"Dies ist wahrscheinlich ein Fehler im ALSA-Treiber '%s'. Bitte melden Sie "
"diesen Punkt den ALSA-Entwicklern."
msgstr[1] ""
"snd_pcm_avail() gibt einen Wert zurück, welche außerordentlich groß ist: %lu "
"bytes (%lu ms).\n"
"Dies ist wahrscheinlich ein Fehler im ALSA-Treiber '%s'. Bitte melden Sie "
"diesen Punkt den ALSA-Entwicklern."

#: src/modules/alsa/alsa-util.c:1249
#, fuzzy, c-format
msgid ""
"snd_pcm_delay() returned a value that is exceptionally large: %li byte (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_delay() returned a value that is exceptionally large: %li bytes (%s"
"%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_delay() gibt einen Wert zurück, welche außerordentlich groß ist: %li "
"bytes (%s%lu ms).\n"
"Dies ist wahrscheinlich ein Fehler im ALSA-Treiber '%s'. Bitte melden Sie "
"diesen Punkt den ALSA-Entwicklern."
msgstr[1] ""
"snd_pcm_delay() gibt einen Wert zurück, welche außerordentlich groß ist: %li "
"bytes (%s%lu ms).\n"
"Dies ist wahrscheinlich ein Fehler im ALSA-Treiber '%s'. Bitte melden Sie "
"diesen Punkt den ALSA-Entwicklern."

#: src/modules/alsa/alsa-util.c:1296
#, fuzzy, c-format
msgid ""
"snd_pcm_avail_delay() returned strange values: delay %lu is less than avail "
"%lu.\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr ""
"snd_pcm_avail() gibt einen Wert zurück, welche außerordentlich groß ist: %lu "
"bytes (%lu ms).\n"
"Dies ist wahrscheinlich ein Fehler im ALSA-Treiber '%s'. Bitte melden Sie "
"diesen Punkt den ALSA-Entwicklern."

#: src/modules/alsa/alsa-util.c:1339
#, fuzzy, c-format
msgid ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu byte "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgid_plural ""
"snd_pcm_mmap_begin() returned a value that is exceptionally large: %lu bytes "
"(%lu ms).\n"
"Most likely this is a bug in the ALSA driver '%s'. Please report this issue "
"to the ALSA developers."
msgstr[0] ""
"snd_pcm_mmap_begin() gibt einen Wert zurück, welche außerordentlich groß "
"ist: %lu bytes (%lu ms).\n"
"Dies ist wahrscheinlich ein Fehler im ALSA-Treiber '%s'. Bitte melden Sie "
"diesen Punkt den ALSA-Entwicklern."
msgstr[1] ""
"snd_pcm_mmap_begin() gibt einen Wert zurück, welche außerordentlich groß "
"ist: %lu bytes (%lu ms).\n"
"Dies ist wahrscheinlich ein Fehler im ALSA-Treiber '%s'. Bitte melden Sie "
"diesen Punkt den ALSA-Entwicklern."

#: src/modules/bluetooth/module-bluez5-device.c:1937
#: src/modules/bluetooth/module-bluez5-device.c:1963
#: src/modules/bluetooth/module-bluez5-device.c:1970
msgid "Bluetooth Input"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1938
#: src/modules/bluetooth/module-bluez5-device.c:1957
#, fuzzy
msgid "Bluetooth Output"
msgstr "Ausgang %s"

#: src/modules/bluetooth/module-bluez5-device.c:1950 src/utils/pactl.c:348
msgid "Handsfree"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1971
#, fuzzy
msgid "Headphone"
msgstr "Analog Mono"

#: src/modules/bluetooth/module-bluez5-device.c:1977 src/utils/pactl.c:347
msgid "Portable"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1983 src/utils/pactl.c:349
msgid "Car"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1989 src/utils/pactl.c:350
msgid "HiFi"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:1995 src/utils/pactl.c:351
msgid "Phone"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2042
#, fuzzy
msgid "High Fidelity Playback (A2DP Sink)"
msgstr "High Fidelity-Wiedergabe (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2054
#, fuzzy
msgid "High Fidelity Capture (A2DP Source)"
msgstr "High Fidelity-Aufnahme (A2DP)"

#: src/modules/bluetooth/module-bluez5-device.c:2066
msgid "Headset Head Unit (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2079
msgid "Headset Audio Gateway (HSP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2092
msgid "Handsfree Head Unit (HFP)"
msgstr ""

#: src/modules/bluetooth/module-bluez5-device.c:2105
msgid "Handsfree Audio Gateway (HFP)"
msgstr ""

#: src/modules/echo-cancel/module-echo-cancel.c:59
msgid ""
"source_name=<name for the source> source_properties=<properties for the "
"source> source_master=<name of source to filter> sink_name=<name for the "
"sink> sink_properties=<properties for the sink> sink_master=<name of sink to "
"filter> adjust_time=<how often to readjust rates in s> adjust_threshold=<how "
"much drift to readjust after in ms> format=<sample format> rate=<sample "
"rate> channels=<number of channels> channel_map=<channel map> "
"aec_method=<implementation to use> aec_args=<parameters for the AEC engine> "
"save_aec=<save AEC data in /tmp> autoloaded=<set if this module is being "
"loaded automatically> use_volume_sharing=<yes or no> use_master_format=<yes "
"or no> "
msgstr ""

#. add on profile
#: src/modules/macosx/module-coreaudio-device.c:825
msgid "On"
msgstr ""

#: src/modules/module-allow-passthrough.c:71
#: src/modules/module-always-sink.c:80
msgid "Dummy Output"
msgstr "Dummy-Ausgabe"

#: src/modules/module-always-sink.c:34
msgid "Always keeps at least one sink loaded even if it's a null one"
msgstr ""

#: src/modules/module-always-source.c:35
msgid "Always keeps at least one source loaded even if it's a null one"
msgstr ""

#: src/modules/module-equalizer-sink.c:68
msgid "General Purpose Equalizer"
msgstr ""

#: src/modules/module-equalizer-sink.c:72
msgid ""
"sink_name=<name of the sink> sink_properties=<properties for the sink> "
"sink_master=<sink to connect to> format=<sample format> rate=<sample rate> "
"channels=<number of channels> channel_map=<channel map> autoloaded=<set if "
"this module is being loaded automatically> use_volume_sharing=<yes or no> "
msgstr ""

#: src/modules/module-equalizer-sink.c:1097
#: src/modules/module-equalizer-sink.c:1220
#, c-format
msgid "FFT based equalizer on %s"
msgstr ""

#: src/modules/module-filter-apply.c:47
msgid "autoclean=<automatically unload unused filters?>"
msgstr ""

#: src/modules/module-ladspa-sink.c:50
#, fuzzy
msgid "Virtual LADSPA sink"
msgstr "Virtueller LADSPA-Sink"

#: src/modules/module-ladspa-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"sink_input_properties=<properties for the sink input> master=<name of sink "
"to filter> sink_master=<name of sink to filter> format=<sample format> "
"rate=<sample rate> channels=<number of channels> channel_map=<input channel "
"map> plugin=<ladspa plugin name> label=<ladspa plugin label> control=<comma "
"separated list of input control values> input_ladspaport_map=<comma "
"separated list of input LADSPA port names> output_ladspaport_map=<comma "
"separated list of output LADSPA port names> autoloaded=<set if this module "
"is being loaded automatically> "
msgstr ""

#: src/modules/module-null-sink.c:46
msgid "Clocked NULL sink"
msgstr ""

#: src/modules/module-null-sink.c:356
#, fuzzy
msgid "Null Output"
msgstr "Ausgang %s"

#: src/modules/module-null-sink.c:368 src/utils/pactl.c:2064
#, fuzzy, c-format
msgid "Failed to set format: invalid format string %s"
msgstr "Beziehen der Quellen-Informationen fehlgeschlagen: %s"

#: src/modules/module-rygel-media-server.c:506
#: src/modules/module-rygel-media-server.c:544
#: src/modules/module-rygel-media-server.c:903
msgid "Output Devices"
msgstr ""

#: src/modules/module-rygel-media-server.c:507
#: src/modules/module-rygel-media-server.c:545
#: src/modules/module-rygel-media-server.c:904
#, fuzzy
msgid "Input Devices"
msgstr "Eingang %s"

#: src/modules/module-rygel-media-server.c:1061
msgid "Audio on @HOSTNAME@"
msgstr ""

#. TODO: old tunnel put here the remote sink_name into stream name e.g. 'Null Output for lynxis@lazus'
#. TODO: old tunnel put here the remote source_name into stream name e.g. 'Null Output for lynxis@lazus'
#: src/modules/module-tunnel-sink-new.c:370
#: src/modules/module-tunnel-source-new.c:354
#, c-format
msgid "Tunnel for %s@%s"
msgstr ""

#: src/modules/module-tunnel-sink-new.c:715
#: src/modules/module-tunnel-source-new.c:684
#, c-format
msgid "Tunnel to %s/%s"
msgstr ""

#: src/modules/module-virtual-surround-sink.c:50
#, fuzzy
msgid "Virtual surround sink"
msgstr "Virtueller LADSPA-Sink"

#: src/modules/module-virtual-surround-sink.c:54
msgid ""
"sink_name=<name for the sink> sink_properties=<properties for the sink> "
"master=<name of sink to filter> sink_master=<name of sink to filter> "
"format=<sample format> rate=<sample rate> channels=<number of channels> "
"channel_map=<channel map> use_volume_sharing=<yes or no> "
"force_flat_volume=<yes or no> hrir=/path/to/left_hrir.wav hrir_left=/path/to/"
"left_hrir.wav hrir_right=/path/to/optional/right_hrir.wav autoloaded=<set if "
"this module is being loaded automatically> "
msgstr ""

#: src/modules/raop/module-raop-discover.c:295
#, fuzzy
msgid "Unknown device model"
msgstr "Unbekannter Fehlercode"

#: src/modules/raop/raop-sink.c:689
msgid "RAOP standard profile"
msgstr ""

#: src/modules/reserve-wrap.c:149
msgid "PulseAudio Sound Server"
msgstr "PulseAudio Sound Server"

#: src/pulse/channelmap.c:105
msgid "Front Center"
msgstr "Vorne Mitte"

#: src/pulse/channelmap.c:106
msgid "Front Left"
msgstr "Vorne Links"

#: src/pulse/channelmap.c:107
msgid "Front Right"
msgstr "Vorne Rechts"

#: src/pulse/channelmap.c:109
msgid "Rear Center"
msgstr "Hinten Mitte"

#: src/pulse/channelmap.c:110
msgid "Rear Left"
msgstr "Hinten Links"

#: src/pulse/channelmap.c:111
msgid "Rear Right"
msgstr "Hinten Rechts"

#: src/pulse/channelmap.c:113
msgid "Subwoofer"
msgstr ""

#: src/pulse/channelmap.c:115
msgid "Front Left-of-center"
msgstr "Vorne Links der Mitte"

#: src/pulse/channelmap.c:116
msgid "Front Right-of-center"
msgstr "Vorne Rechts der Mitte"

#: src/pulse/channelmap.c:118
msgid "Side Left"
msgstr "Seite Links"

#: src/pulse/channelmap.c:119
msgid "Side Right"
msgstr "Seite Rechts"

#: src/pulse/channelmap.c:121
msgid "Auxiliary 0"
msgstr "Zusatz 0"

#: src/pulse/channelmap.c:122
msgid "Auxiliary 1"
msgstr "Zusatz 1"

#: src/pulse/channelmap.c:123
msgid "Auxiliary 2"
msgstr "Zusatz 2"

#: src/pulse/channelmap.c:124
msgid "Auxiliary 3"
msgstr "Zusatz 3"

#: src/pulse/channelmap.c:125
msgid "Auxiliary 4"
msgstr "Zusatz 4"

#: src/pulse/channelmap.c:126
msgid "Auxiliary 5"
msgstr "Zusatz 5"

#: src/pulse/channelmap.c:127
msgid "Auxiliary 6"
msgstr "Zusatz 6"

#: src/pulse/channelmap.c:128
msgid "Auxiliary 7"
msgstr "Zusatz 7"

#: src/pulse/channelmap.c:129
msgid "Auxiliary 8"
msgstr "Zusatz 8"

#: src/pulse/channelmap.c:130
msgid "Auxiliary 9"
msgstr "Zusatz 9"

#: src/pulse/channelmap.c:131
msgid "Auxiliary 10"
msgstr "Zusatz 10"

#: src/pulse/channelmap.c:132
msgid "Auxiliary 11"
msgstr "Zusatz 11"

#: src/pulse/channelmap.c:133
msgid "Auxiliary 12"
msgstr "Zusatz 12"

#: src/pulse/channelmap.c:134
msgid "Auxiliary 13"
msgstr "Zusatz 13"

#: src/pulse/channelmap.c:135
msgid "Auxiliary 14"
msgstr "Zusatz 14"

#: src/pulse/channelmap.c:136
msgid "Auxiliary 15"
msgstr "Zusatz 15"

#: src/pulse/channelmap.c:137
msgid "Auxiliary 16"
msgstr "Zusatz 16"

#: src/pulse/channelmap.c:138
msgid "Auxiliary 17"
msgstr "Zusatz 17"

#: src/pulse/channelmap.c:139
msgid "Auxiliary 18"
msgstr "Zusatz 18"

#: src/pulse/channelmap.c:140
msgid "Auxiliary 19"
msgstr "Zusatz 19"

#: src/pulse/channelmap.c:141
msgid "Auxiliary 20"
msgstr "Zusatz 20"

#: src/pulse/channelmap.c:142
msgid "Auxiliary 21"
msgstr "Zusatz 21"

#: src/pulse/channelmap.c:143
msgid "Auxiliary 22"
msgstr "Zusatz 22"

#: src/pulse/channelmap.c:144
msgid "Auxiliary 23"
msgstr "Zusatz 23"

#: src/pulse/channelmap.c:145
msgid "Auxiliary 24"
msgstr "Zusatz 24"

#: src/pulse/channelmap.c:146
msgid "Auxiliary 25"
msgstr "Zusatz 25"

#: src/pulse/channelmap.c:147
msgid "Auxiliary 26"
msgstr "Zusatz 26"

#: src/pulse/channelmap.c:148
msgid "Auxiliary 27"
msgstr "Zusatz 26"

#: src/pulse/channelmap.c:149
msgid "Auxiliary 28"
msgstr "Zusatz 28"

#: src/pulse/channelmap.c:150
msgid "Auxiliary 29"
msgstr "Zusatz 29"

#: src/pulse/channelmap.c:151
msgid "Auxiliary 30"
msgstr "Zusatz 30"

#: src/pulse/channelmap.c:152
msgid "Auxiliary 31"
msgstr "Zusatz 31"

#: src/pulse/channelmap.c:154
msgid "Top Center"
msgstr "Oben Mitte"

#: src/pulse/channelmap.c:156
msgid "Top Front Center"
msgstr "Oben Vorne Mitte"

#: src/pulse/channelmap.c:157
msgid "Top Front Left"
msgstr "Oben Vorne Links"

#: src/pulse/channelmap.c:158
msgid "Top Front Right"
msgstr "Oben Vorne Rechts"

#: src/pulse/channelmap.c:160
msgid "Top Rear Center"
msgstr "Oben Hinten Mitte"

#: src/pulse/channelmap.c:161
msgid "Top Rear Left"
msgstr "Oben Hinten Links"

#: src/pulse/channelmap.c:162
msgid "Top Rear Right"
msgstr "Oben Hinten Rechts"

#: src/pulse/channelmap.c:478 src/pulse/format.c:123 src/pulse/sample.c:177
#: src/pulse/volume.c:306 src/pulse/volume.c:332 src/pulse/volume.c:352
#: src/pulse/volume.c:384 src/pulse/volume.c:424 src/pulse/volume.c:443
#: src/utils/pactl.c:483 src/utils/pactl.c:504
msgid "(invalid)"
msgstr "(ungültig)"

#: src/pulse/channelmap.c:779
msgid "Surround 4.0"
msgstr "Surround 4.0"

#: src/pulse/channelmap.c:785
msgid "Surround 4.1"
msgstr "Surround 4.1"

#: src/pulse/channelmap.c:791
msgid "Surround 5.0"
msgstr "Surround 5.0"

#: src/pulse/channelmap.c:797
msgid "Surround 5.1"
msgstr "Surround 5.1"

#: src/pulse/channelmap.c:804
msgid "Surround 7.1"
msgstr "Surround 7.1"

#: src/pulse/client-conf-x11.c:61 src/utils/pax11publish.c:97
#, fuzzy
msgid "xcb_connect() failed"
msgstr "pa_context_new() fehlgeschlagen: %s"

#: src/pulse/client-conf-x11.c:66 src/utils/pax11publish.c:102
msgid "xcb_connection_has_error() returned true"
msgstr ""

#: src/pulse/client-conf-x11.c:102
msgid "Failed to parse cookie data"
msgstr "Parsen der Cookie-Daten fehlgeschlagen"

#: src/pulse/context.c:717
#, c-format
msgid "fork(): %s"
msgstr "fork(): %s"

#: src/pulse/context.c:772
#, c-format
msgid "waitpid(): %s"
msgstr "waitpid(): %s"

#: src/pulse/context.c:1488
#, c-format
msgid "Received message for unknown extension '%s'"
msgstr "Nachricht für unbekannte Erweiterung '%s' erhalten"

#: src/pulse/direction.c:37
#, fuzzy
msgid "input"
msgstr "Eingang %s"

#: src/pulse/direction.c:39
#, fuzzy
msgid "output"
msgstr "Ausgang %s"

#: src/pulse/direction.c:41
msgid "bidirectional"
msgstr ""

#: src/pulse/direction.c:43
#, fuzzy
msgid "invalid"
msgstr "(ungültig)"

#: src/pulsecore/core-util.c:1790
#, c-format
msgid ""
"XDG_RUNTIME_DIR (%s) is not owned by us (uid %d), but by uid %d! (This could "
"e.g. happen if you try to connect to a non-root PulseAudio as a root user, "
"over the native protocol. Don't do that.)"
msgstr ""

#: src/pulsecore/core-util.h:97
msgid "yes"
msgstr ""

#: src/pulsecore/core-util.h:97
#, fuzzy
msgid "no"
msgstr "Mono"

#: src/pulsecore/lock-autospawn.c:141 src/pulsecore/lock-autospawn.c:227
msgid "Cannot access autospawn lock."
msgstr "Fehler beim Zugriff auf Autostart -Sperre."

#: src/pulsecore/log.c:165
#, fuzzy, c-format
msgid "Failed to open target file '%s'."
msgstr "Öffnen der Datei '%s' fehlgeschlagen\n"

#: src/pulsecore/log.c:188
#, c-format
msgid ""
"Tried to open target file '%s', '%s.1', '%s.2' ... '%s.%d', but all failed."
msgstr ""

#: src/pulsecore/log.c:651
#, fuzzy
msgid "Invalid log target."
msgstr "[%s:%u] Ungültiges Log-Ziel '%s'."

#: src/pulsecore/sink.c:3609
msgid "Built-in Audio"
msgstr "Internes Audio"

#: src/pulsecore/sink.c:3614
msgid "Modem"
msgstr "Modem"

#: src/pulse/error.c:38
msgid "OK"
msgstr "OK"

#: src/pulse/error.c:39
msgid "Access denied"
msgstr "Zugriff abgelehnt"

#: src/pulse/error.c:40
msgid "Unknown command"
msgstr "Unbekannter Befehl"

#: src/pulse/error.c:41
msgid "Invalid argument"
msgstr "Ungültiges Argument"

#: src/pulse/error.c:42
msgid "Entity exists"
msgstr "Entität existiert bereits"

#: src/pulse/error.c:43
msgid "No such entity"
msgstr "Entität nicht vorhanden"

#: src/pulse/error.c:44
msgid "Connection refused"
msgstr "Verbindung verweigert"

#: src/pulse/error.c:45
msgid "Protocol error"
msgstr "Protokollfehler"

#: src/pulse/error.c:46
msgid "Timeout"
msgstr "Zeitüberschreitung"

#: src/pulse/error.c:47
#, fuzzy
msgid "No authentication key"
msgstr "Kein Authorisierungsschlüssel"

#: src/pulse/error.c:48
msgid "Internal error"
msgstr "Interner Fehler"

#: src/pulse/error.c:49
msgid "Connection terminated"
msgstr "Verbindung beendet"

#: src/pulse/error.c:50
msgid "Entity killed"
msgstr "Entität terminiert"

#: src/pulse/error.c:51
msgid "Invalid server"
msgstr "Ungültiger Server"

#: src/pulse/error.c:52
msgid "Module initialization failed"
msgstr "Modulinitialisierung fehlgeschlagen"

#: src/pulse/error.c:53
msgid "Bad state"
msgstr "Ungültiger Zustand"

#: src/pulse/error.c:54
msgid "No data"
msgstr "Keine Daten"

#: src/pulse/error.c:55
msgid "Incompatible protocol version"
msgstr "Inkompatible Protokollversion"

#: src/pulse/error.c:56
msgid "Too large"
msgstr "Zu groß"

#: src/pulse/error.c:57
msgid "Not supported"
msgstr "Nicht unterstützt"

#: src/pulse/error.c:58
msgid "Unknown error code"
msgstr "Unbekannter Fehlercode"

#: src/pulse/error.c:59
msgid "No such extension"
msgstr "Erweiterung nicht vorhanden"

#: src/pulse/error.c:60
msgid "Obsolete functionality"
msgstr "Veraltete Funktion"

#: src/pulse/error.c:61
msgid "Missing implementation"
msgstr "Fehlende Implementation"

#: src/pulse/error.c:62
msgid "Client forked"
msgstr "Client geteilt"

#: src/pulse/error.c:63
msgid "Input/Output error"
msgstr "Eingabe/Ausgabe-Fehler"

#: src/pulse/error.c:64
msgid "Device or resource busy"
msgstr "Gerät oder Ressource beschäftigt"

#: src/pulse/sample.c:179
#, c-format
msgid "%s %uch %uHz"
msgstr "%s %uch %uHz"

#: src/pulse/sample.c:191
#, c-format
msgid "%0.1f GiB"
msgstr "%0.1f GB"

#: src/pulse/sample.c:193
#, c-format
msgid "%0.1f MiB"
msgstr "%0.1f MB"

#: src/pulse/sample.c:195
#, c-format
msgid "%0.1f KiB"
msgstr "%0.1f KB"

#: src/pulse/sample.c:197
#, c-format
msgid "%u B"
msgstr "%u B"

#: src/utils/pacat.c:134
#, c-format
msgid "Failed to drain stream: %s"
msgstr "Entleeren des Streams fehlgeschlagen: %s"

#: src/utils/pacat.c:139
msgid "Playback stream drained."
msgstr "Wiedergabe-Stream entleert."

#: src/utils/pacat.c:150
msgid "Draining connection to server."
msgstr "Verbindung zu Server entleert."

#: src/utils/pacat.c:163
#, c-format
msgid "pa_stream_drain(): %s"
msgstr "pa_stream_drain(): %s"

#: src/utils/pacat.c:194 src/utils/pacat.c:543
#, c-format
msgid "pa_stream_begin_write() failed: %s"
msgstr "pa_stream_write() fehlgeschlagen: %s"

#: src/utils/pacat.c:244 src/utils/pacat.c:274
#, c-format
msgid "pa_stream_peek() failed: %s"
msgstr "pa_stream_peek() fehlgeschlagen: %s"

#: src/utils/pacat.c:324
msgid "Stream successfully created."
msgstr "Stream wurde erfolgreich erstellt."

#: src/utils/pacat.c:327
#, c-format
msgid "pa_stream_get_buffer_attr() failed: %s"
msgstr "pa_stream_get_buffer_attr() fehlgeschlagen: %s"

#: src/utils/pacat.c:331
#, c-format
msgid "Buffer metrics: maxlength=%u, tlength=%u, prebuf=%u, minreq=%u"
msgstr "Pufferdaten: maxlenght=%u, tlength=%u, prebuf=%u, minreq=%u"

#: src/utils/pacat.c:334
#, c-format
msgid "Buffer metrics: maxlength=%u, fragsize=%u"
msgstr "Pufferdaten: maxlength=%u, fragsize=%u"

#: src/utils/pacat.c:338
#, c-format
msgid "Using sample spec '%s', channel map '%s'."
msgstr "Benutze Sample-Angabe '%s', Kanalzuordnung '%s'."

#: src/utils/pacat.c:342
#, fuzzy, c-format
msgid "Connected to device %s (index: %u, suspended: %s)."
msgstr "Verbunden mit Gerät %s (%u, %sausgesetzt)."

#: src/utils/pacat.c:352
#, c-format
msgid "Stream error: %s"
msgstr "Stream-Fehler: %s"

#: src/utils/pacat.c:362
#, c-format
msgid "Stream device suspended.%s"
msgstr "Stream-Gerät ausgesetzt.%s"

#: src/utils/pacat.c:364
#, c-format
msgid "Stream device resumed.%s"
msgstr "Stream-Gerät reaktiviert.%s"

#: src/utils/pacat.c:372
#, c-format
msgid "Stream underrun.%s"
msgstr "Stream leergelaufen.%s"

#: src/utils/pacat.c:379
#, c-format
msgid "Stream overrun.%s"
msgstr "Stream überlaufen.%s "

#: src/utils/pacat.c:386
#, c-format
msgid "Stream started.%s"
msgstr "Stream gestartet: %s"

#: src/utils/pacat.c:393
#, c-format
msgid "Stream moved to device %s (%u, %ssuspended).%s"
msgstr "Stream an Gerät %s übergeben (%u, %sausgesetzt).%s"

#: src/utils/pacat.c:393
msgid "not "
msgstr "nicht "

#: src/utils/pacat.c:400
#, c-format
msgid "Stream buffer attributes changed.%s"
msgstr "Stream-Zwischenspeicher-Attribute geändert.%s"

#: src/utils/pacat.c:415
msgid "Cork request stack is empty: corking stream"
msgstr ""

#: src/utils/pacat.c:421
msgid "Cork request stack is empty: uncorking stream"
msgstr ""

#: src/utils/pacat.c:425
msgid "Warning: Received more uncork requests than cork requests."
msgstr ""

#: src/utils/pacat.c:450
#, c-format
msgid "Connection established.%s"
msgstr "Verbindung hergestellt.%s"

#: src/utils/pacat.c:453
#, c-format
msgid "pa_stream_new() failed: %s"
msgstr "pa_stream_new() fehlgeschlagen: %s"

#: src/utils/pacat.c:491
#, c-format
msgid "pa_stream_connect_playback() failed: %s"
msgstr "pa_stream_connect_playback() fehlgeschlagen: %s"

#: src/utils/pacat.c:497
#, fuzzy, c-format
msgid "Failed to set monitor stream: %s"
msgstr "Entleeren des Streams fehlgeschlagen: %s"

#: src/utils/pacat.c:501
#, c-format
msgid "pa_stream_connect_record() failed: %s"
msgstr "pa_stream_connect_record() fehlgeschlagen: %s"

#: src/utils/pacat.c:514 src/utils/pactl.c:2508
#, c-format
msgid "Connection failure: %s"
msgstr "Verbindungsfehler: %s"

#: src/utils/pacat.c:557
msgid "Got EOF."
msgstr "EOF empfangen."

#: src/utils/pacat.c:581
#, c-format
msgid "pa_stream_write() failed: %s"
msgstr "pa_stream_write() fehlgeschlagen: %s"

#: src/utils/pacat.c:605
#, c-format
msgid "write() failed: %s"
msgstr "write() fehlgeschlagen: %s"

#: src/utils/pacat.c:626
msgid "Got signal, exiting."
msgstr "Signal empfangen, beenden."

#: src/utils/pacat.c:640
#, c-format
msgid "Failed to get latency: %s"
msgstr "Erhalten der Latenz fehlgeschlagen: %s"

#: src/utils/pacat.c:645
#, c-format
msgid "Time: %0.3f sec; Latency: %0.0f usec."
msgstr "Zeit: %0.3f sec; Latenz: %0.0f usec."

#: src/utils/pacat.c:666
#, c-format
msgid "pa_stream_update_timing_info() failed: %s"
msgstr "pa_stream_update_timing_info() fehlgeschlagen: %s"

#: src/utils/pacat.c:676
#, fuzzy, c-format
msgid ""
"%s [options]\n"
"%s\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -r, --record                          Create a connection for recording\n"
"  -p, --playback                        Create a connection for playback\n"
"\n"
"  -v, --verbose                         Enable verbose operations\n"
"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -d, --device=DEVICE                   The name of the sink/source to "
"connect to. The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and "
"@DEFAULT_MONITOR@ can be used to specify the default sink, source and "
"monitor respectively.\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
"      --stream-name=NAME                How to call this stream on the "
"server\n"
"      --volume=VOLUME                   Specify the initial (linear) volume "
"in range 0...65536\n"
"      --rate=SAMPLERATE                 The sample rate in Hz (defaults to "
"44100)\n"
"      --format=SAMPLEFORMAT             The sample format, see\n"
"                                        https://www.freedesktop.org/wiki/"
"Software/PulseAudio/Documentation/User/SupportedAudioFormats/\n"
"                                        for possible values (defaults to "
"s16ne)\n"
"      --channels=CHANNELS               The number of channels, 1 for mono, "
"2 for stereo\n"
"                                        (defaults to 2)\n"
"      --channel-map=CHANNELMAP          Channel map to use instead of the "
"default\n"
"      --fix-format                      Take the sample format from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-rate                        Take the sampling rate from the sink/"
"source the stream is\n"
"                                        being connected to.\n"
"      --fix-channels                    Take the number of channels and the "
"channel map\n"
"                                        from the sink/source the stream is "
"being connected to.\n"
"      --no-remix                        Don't upmix or downmix channels.\n"
"      --no-remap                        Map channels by index instead of "
"name.\n"
"      --latency=BYTES                   Request the specified latency in "
"bytes.\n"
"      --process-time=BYTES              Request the specified process time "
"per request in bytes.\n"
"      --latency-msec=MSEC               Request the specified latency in "
"msec.\n"
"      --process-time-msec=MSEC          Request the specified process time "
"per request in msec.\n"
"      --property=PROPERTY=VALUE         Set the specified property to the "
"specified value.\n"
"      --raw                             Record/play raw PCM data.\n"
"      --passthrough                     Passthrough data.\n"
"      --file-format[=FFORMAT]           Record/play formatted PCM data.\n"
"      --list-file-formats               List available file formats.\n"
"      --monitor-stream=INDEX            Record from the sink input with "
"index INDEX.\n"
msgstr ""
"%s [options]\n"
"\n"
"  -h, --help                            Diese Hilfe anzeigen\n"
"      --version                         Version anzeigen\n"
"\n"
"  -r, --record                          Aufnahme-Verbindung aufbauen\n"
"  -p, --playback                        Wiedergabe-Verbindung aufbauen\n"
"\n"
"  -v, --verbose                         Ausführliche Meldungen\n"
"\n"
"  -s, --server=SERVER                   Name des zu verbindenden Servers\n"
"  -d, --device=DEVICE                   Name zu verbindender Sink/Quelle\n"
"  -n, --client-name=NAME                Rufname des Clients auf dem Server\n"
"      --stream-name=NAME                Rufname des Streams auf dem Server\n"
"      --volume=VOLUME                   Initiale (lineare) Lautstärke "
"zwischen 0...65536 angeben\n"
"      --rate=SAMPLERATE                 Sample-Rate in Hz (Standard 44100)\n"
"      --format=SAMPLEFORMAT             Ein Sample-Format von s16le, s16be, "
"u8, float32le,\n"
"                                        float32be, ulaw, alaw, s32le, s32be "
"(defaults to s16ne)\n"
"      --channels=CHANNELS               Anzahl Kanäle, 1 für mono, 2 für "
"stereo\n"
"                                        (Standard ist 2)\n"
"      --channel-map=CHANNELMAP          Diese geänderte Kanalzuordnung "
"nutzen\n"
"      --fix-format                      Sample-Format des mit Sink\n"
"                                        verbundenen Streams nutzen.\n"
"      --fix-rate                        Sample-Rate des mit Sink\n"
"                                        verbundenen Streams nutzen.\n"
"      --fix-channels                    Anzahl und Zuordnung der Kanäle\n"
"                                        des mit Sink verbundenen\n"
"                                        Streams nutzen.\n"
"      --no-remix                        Kanäle nicht up-/down-mischen.\n"
"      --no-remap                        Kanäle nach Index statt Name "
"zuordnen.\n"
"      --latency=BYTES                   Diese Latenz verwenden.\n"
"      --process-time=BYTES              Diese Prozesszeit pro Anfrage "
"verwenden.\n"

#: src/utils/pacat.c:793
msgid "Play back encoded audio files on a PulseAudio sound server."
msgstr ""

#: src/utils/pacat.c:797
msgid ""
"Capture audio data from a PulseAudio sound server and write it to a file."
msgstr ""

#: src/utils/pacat.c:801
msgid ""
"Capture audio data from a PulseAudio sound server and write it to STDOUT or "
"the specified file."
msgstr ""

#: src/utils/pacat.c:805
msgid ""
"Play back audio data from STDIN or the specified file on a PulseAudio sound "
"server."
msgstr ""

#: src/utils/pacat.c:819
#, c-format
msgid ""
"pacat %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Kompiliert mit libpulse %s\n"
"Gelinkt mit libpulse %s\n"

#: src/utils/pacat.c:852 src/utils/pactl.c:2731
#, c-format
msgid "Invalid client name '%s'"
msgstr "Ungültiger Client-Name '%s'"

#: src/utils/pacat.c:867
#, c-format
msgid "Invalid stream name '%s'"
msgstr "Ungültiger Stream-Name '%s'"

#: src/utils/pacat.c:904
#, c-format
msgid "Invalid channel map '%s'"
msgstr "Ungültige Kanal-Zuweisung '%s'"

#: src/utils/pacat.c:933 src/utils/pacat.c:947
#, c-format
msgid "Invalid latency specification '%s'"
msgstr "Ungültige Latenz-Angaben '%s'"

#: src/utils/pacat.c:940 src/utils/pacat.c:954
#, c-format
msgid "Invalid process time specification '%s'"
msgstr "Ungültige Prozesszeit-Angaben '%s'"

#: src/utils/pacat.c:966
#, c-format
msgid "Invalid property '%s'"
msgstr "Ungültige Eigenschaft '%s'"

#: src/utils/pacat.c:985
#, c-format
msgid "Unknown file format %s."
msgstr "Unbekanntes Dateiformat %s."

#: src/utils/pacat.c:1000
msgid "Failed to parse the argument for --monitor-stream"
msgstr ""

#: src/utils/pacat.c:1011
msgid "Invalid sample specification"
msgstr "Ungültige Sample-Angaben"

#: src/utils/pacat.c:1021
#, c-format
msgid "open(): %s"
msgstr "open(): %s"

#: src/utils/pacat.c:1026
#, c-format
msgid "dup2(): %s"
msgstr "dup2(): %s"

#: src/utils/pacat.c:1033
msgid "Too many arguments."
msgstr "Zu viele Argumente."

#: src/utils/pacat.c:1044
msgid "Failed to generate sample specification for file."
msgstr "Beziehen der Sample-Informationen für die Datei fehlgeschlagen."

#: src/utils/pacat.c:1082
msgid "Failed to open audio file."
msgstr "Öffnen der Audio-Datei fehlgeschlagen."

#: src/utils/pacat.c:1088
msgid ""
"Warning: specified sample specification will be overwritten with "
"specification from file."
msgstr "Warnung: Beziehen der Sample-Angabe aus Datei fehlgeschlagen."

#: src/utils/pacat.c:1091 src/utils/pactl.c:2806
msgid "Failed to determine sample specification from file."
msgstr "Beziehen der Sample-Informationen der Datei fehlgeschlagen."

#: src/utils/pacat.c:1100
msgid "Warning: Failed to determine channel map from file."
msgstr "Warnung: Bestimmung der Kanalzuordnung aus Datei fehlgeschlagen."

#: src/utils/pacat.c:1111
msgid "Channel map doesn't match sample specification"
msgstr "Kanalzuordnung entspricht nicht Einstellungen des Samples"

#: src/utils/pacat.c:1122
msgid "Warning: failed to write channel map to file."
msgstr "Warnung: Schreiben der Kanalzuordnung in Datei fehlgeschlagen."

#: src/utils/pacat.c:1137
#, c-format
msgid ""
"Opening a %s stream with sample specification '%s' and channel map '%s'."
msgstr ""
"Öffnen eines %s-Streams mit Sample-Angabe '%s' und Kanalzuordnung '%s'."

#: src/utils/pacat.c:1138
msgid "recording"
msgstr "aufnehmen"

#: src/utils/pacat.c:1138
msgid "playback"
msgstr "abspielen"

#: src/utils/pacat.c:1162
#, fuzzy
msgid "Failed to set media name."
msgstr "Parsen der Kommandzeile fehlgeschlagen."

#: src/utils/pacat.c:1172 src/utils/pactl.c:3218
msgid "pa_mainloop_new() failed."
msgstr "pa_mainloop_new() fehlgeschlagen"

#: src/utils/pacat.c:1195
msgid "io_new() failed."
msgstr "io_new() fehlgeschlagen."

#: src/utils/pacat.c:1202 src/utils/pactl.c:3230
msgid "pa_context_new() failed."
msgstr "pa_context_new() fehlgeschlagen."

#: src/utils/pacat.c:1210 src/utils/pactl.c:3236
#, c-format
msgid "pa_context_connect() failed: %s"
msgstr "pa_context_new() fehlgeschlagen: %s"

#: src/utils/pacat.c:1216
msgid "pa_context_rttime_new() failed."
msgstr "pa_context_new() fehlgeschlagen."

#: src/utils/pacat.c:1223 src/utils/pactl.c:3241
msgid "pa_mainloop_run() failed."
msgstr "pa_mainloop_run() fehlgeschlagen."

#: src/utils/pacmd.c:51 src/utils/pactl.c:2643
msgid "NAME [ARGS ...]"
msgstr ""

#: src/utils/pacmd.c:52 src/utils/pacmd.c:60 src/utils/pactl.c:2644
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652
msgid "NAME|#N"
msgstr ""

#: src/utils/pacmd.c:53 src/utils/pacmd.c:63 src/utils/pactl.c:2642
#: src/utils/pactl.c:2649
msgid "NAME"
msgstr ""

#: src/utils/pacmd.c:54
msgid "NAME|#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:55
msgid "#N VOLUME"
msgstr ""

#: src/utils/pacmd.c:56 src/utils/pacmd.c:70 src/utils/pactl.c:2646
msgid "NAME|#N 1|0"
msgstr ""

#: src/utils/pacmd.c:57
msgid "#N 1|0"
msgstr ""

#: src/utils/pacmd.c:58
msgid "NAME|#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:59
msgid "#N KEY=VALUE"
msgstr ""

#: src/utils/pacmd.c:61
msgid "#N"
msgstr ""

#: src/utils/pacmd.c:62
msgid "NAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:64 src/utils/pacmd.c:65
msgid "NAME FILENAME"
msgstr ""

#: src/utils/pacmd.c:66
msgid "PATHNAME"
msgstr ""

#: src/utils/pacmd.c:67
msgid "FILENAME SINK|#N"
msgstr ""

#: src/utils/pacmd.c:69 src/utils/pactl.c:2645
msgid "#N SINK|SOURCE"
msgstr ""

#: src/utils/pacmd.c:71 src/utils/pacmd.c:77 src/utils/pacmd.c:78
msgid "1|0"
msgstr ""

#: src/utils/pacmd.c:72 src/utils/pactl.c:2647
msgid "CARD PROFILE"
msgstr ""

#: src/utils/pacmd.c:73 src/utils/pactl.c:2650
msgid "NAME|#N PORT"
msgstr ""

#: src/utils/pacmd.c:74 src/utils/pactl.c:2658
msgid "CARD-NAME|CARD-#N PORT OFFSET"
msgstr ""

#: src/utils/pacmd.c:75
msgid "TARGET"
msgstr ""

#: src/utils/pacmd.c:76
msgid "NUMERIC-LEVEL"
msgstr ""

#: src/utils/pacmd.c:79
msgid "FRAMES"
msgstr ""

#: src/utils/pacmd.c:80 src/utils/pactl.c:2659
msgid "RECIPIENT MESSAGE [MESSAGE_PARAMETERS]"
msgstr ""

#: src/utils/pacmd.c:82
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"When no command is given pacmd starts in the interactive mode.\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Diese Hilfe zeigen\n"
"      --version                         Zeige Version\n"
"  -s, --server=SERVER                   Name des Zielservers\n"
"\n"

#: src/utils/pacmd.c:129
#, fuzzy, c-format
msgid ""
"pacmd %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pacat %s\n"
"Kompiliert mit libpulse %s\n"
"Gelinkt mit libpulse %s\n"

#: src/utils/pacmd.c:143
msgid "No PulseAudio daemon running, or not running as session daemon."
msgstr "Es läuft kein PulseAudio-Dienst oder nicht als Sessiondienst."

#: src/utils/pacmd.c:148
#, c-format
msgid "socket(PF_UNIX, SOCK_STREAM, 0): %s"
msgstr "socket(PF_UNIX, SOCK_STREAM, 0): %s"

#: src/utils/pacmd.c:165
#, c-format
msgid "connect(): %s"
msgstr "connect(): %s"

#: src/utils/pacmd.c:173
msgid "Failed to kill PulseAudio daemon."
msgstr "Terminieren des PulseAudio-Daemon fehlgeschlagen."

#: src/utils/pacmd.c:181
msgid "Daemon not responding."
msgstr "Daemon antwortet nicht."

#: src/utils/pacmd.c:213 src/utils/pacmd.c:322 src/utils/pacmd.c:340
#, c-format
msgid "write(): %s"
msgstr "write(): %s"

#: src/utils/pacmd.c:269
#, c-format
msgid "poll(): %s"
msgstr "poll(): %s"

#: src/utils/pacmd.c:280 src/utils/pacmd.c:300
#, c-format
msgid "read(): %s"
msgstr "read(): %s"

#: src/utils/pactl.c:183
#, c-format
msgid "Failed to get statistics: %s"
msgstr "Beziehen der Statistik fehlgeschlagen: %s"

#: src/utils/pactl.c:199
#, fuzzy, c-format
msgid "Currently in use: %u block containing %s bytes total.\n"
msgid_plural "Currently in use: %u blocks containing %s bytes total.\n"
msgstr[0] "Momentane Nutzung: %u Blöcke mit insgesamt %s Bytes.\n"
msgstr[1] "Momentane Nutzung: %u Blöcke mit insgesamt %s Bytes.\n"

#: src/utils/pactl.c:205
#, fuzzy, c-format
msgid "Allocated during whole lifetime: %u block containing %s bytes total.\n"
msgid_plural ""
"Allocated during whole lifetime: %u blocks containing %s bytes total.\n"
msgstr[0] "Während gesamter Laufzeit: %u Blöcke mit insgesamt %s Bytes.\n"
msgstr[1] "Während gesamter Laufzeit: %u Blöcke mit insgesamt %s Bytes.\n"

#: src/utils/pactl.c:211
#, c-format
msgid "Sample cache size: %s\n"
msgstr "Sample-Pufferspeichergrösse: %s\n"

#: src/utils/pactl.c:219 src/utils/pactl.c:231 src/utils/pactl.c:245
#, c-format
msgid "Failed to get server information: %s"
msgstr "Beziehen der Server-Information fehlgeschlagen: %s"

#: src/utils/pactl.c:224 src/utils/pactl.c:236
#, fuzzy, c-format
msgid "%s\n"
msgstr "%s %s"

#: src/utils/pactl.c:281
#, c-format
msgid ""
"Server String: %s\n"
"Library Protocol Version: %u\n"
"Server Protocol Version: %u\n"
"Is Local: %s\n"
"Client Index: %u\n"
"Tile Size: %zu\n"
msgstr ""

#: src/utils/pactl.c:294
#, fuzzy, c-format
msgid ""
"User Name: %s\n"
"Host Name: %s\n"
"Server Name: %s\n"
"Server Version: %s\n"
"Default Sample Specification: %s\n"
"Default Channel Map: %s\n"
"Default Sink: %s\n"
"Default Source: %s\n"
"Cookie: %04x:%04x\n"
msgstr ""
"Name des Nutzers: %s\n"
"Rechnername: %s\n"
"Name des Servers: %s\n"
"Version des Server: %s\n"
"Standard-Sample-Angabe: %s\n"
"Standard-Kanal-Zuordnung: %s\n"
"Standard-Ausgabe: %s\n"
"-Standard-Quelle: %s\n"
"Cookie: %08x\n"

#: src/utils/pactl.c:320
msgid "availability unknown"
msgstr ""

#: src/utils/pactl.c:321
msgid "available"
msgstr ""

#: src/utils/pactl.c:322
msgid "not available"
msgstr ""

#: src/utils/pactl.c:331 src/utils/pactl.c:355
#, fuzzy
msgid "Unknown"
msgstr "Unbekannter Befehl"

#: src/utils/pactl.c:332
msgid "Aux"
msgstr ""

#: src/utils/pactl.c:335
msgid "Line"
msgstr ""

#: src/utils/pactl.c:336
msgid "Mic"
msgstr ""

#: src/utils/pactl.c:338
msgid "Handset"
msgstr ""

#: src/utils/pactl.c:339
msgid "Earpiece"
msgstr ""

#: src/utils/pactl.c:340
msgid "SPDIF"
msgstr ""

#: src/utils/pactl.c:341
msgid "HDMI"
msgstr ""

#: src/utils/pactl.c:342
msgid "TV"
msgstr ""

#: src/utils/pactl.c:345
msgid "USB"
msgstr ""

#: src/utils/pactl.c:346
msgid "Bluetooth"
msgstr ""

#: src/utils/pactl.c:352
msgid "Network"
msgstr ""

#: src/utils/pactl.c:353
#, fuzzy
msgid "Analog"
msgstr "Analog Mono"

#: src/utils/pactl.c:567 src/utils/pactl.c:1834 src/utils/pactl.c:1852
#: src/utils/pactl.c:1875 src/utils/pactl.c:1992
#, c-format
msgid "Failed to get sink information: %s"
msgstr "Erhalten der Sink-Informationen fehlgeschlagen: %s"

#: src/utils/pactl.c:664
#, fuzzy, c-format
msgid ""
"Sink #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor Source: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Senke #%u\n"
"\tStatus: %s\n"
"\tName: %s\n"
"\tBeschreibung: %s\n"
"\tTreiber: %s\n"
"\tSample-Angabe: %s\n"
"\tKanalzuordnung: %s\n"
"\tOwner-Modul: %u\n"
"\tStumm: %s\n"
"\tLautstärke: %s%s%s\n"
"\t        Verteilung %0.2f\n"
"\tBasis-Lautstärke: %s%s%s\n"
"\tQuelle Monitor: %s\n"
"\tLatenz: %0.0f usec, eingestellt %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:706 src/utils/pactl.c:890 src/utils/pactl.c:1251
#, c-format
msgid "\tPorts:\n"
msgstr "\tProfile:\n"

#: src/utils/pactl.c:708 src/utils/pactl.c:892
#, c-format
msgid "\t\t%s: %s (type: %s, priority: %u%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:710 src/utils/pactl.c:894 src/utils/pactl.c:1256
msgid ", availability group: "
msgstr ""

#: src/utils/pactl.c:715 src/utils/pactl.c:899
#, c-format
msgid "\tActive Port: %s\n"
msgstr "\tAktive Profile: %s\n"

#: src/utils/pactl.c:721 src/utils/pactl.c:905
#, fuzzy, c-format
msgid "\tFormats:\n"
msgstr "\tProfile:\n"

#: src/utils/pactl.c:753 src/utils/pactl.c:1893 src/utils/pactl.c:1911
#: src/utils/pactl.c:1934 src/utils/pactl.c:2007
#, c-format
msgid "Failed to get source information: %s"
msgstr "Beziehen der Quellen-Informationen fehlgeschlagen: %s"

#: src/utils/pactl.c:849
#, fuzzy, c-format
msgid ""
"Source #%u\n"
"\tState: %s\n"
"\tName: %s\n"
"\tDescription: %s\n"
"\tDriver: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tOwner Module: %u\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBase Volume: %s\n"
"\tMonitor of Sink: %s\n"
"\tLatency: %0.0f usec, configured %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Quelle #%u\n"
"\tStatus: %s\n"
"\tName: %s\n"
"\tBeschreibung: %s\n"
"\tTreiber: %s\n"
"\tSample-Angabe: %s\n"
"\tKanalzuordnung: %s\n"
"\tBesitzer-Modul: %u\n"
"\tStumm: %s\n"
"\tLautstärke: %s%s%s\n"
"\t        Verteilung %0.2f\n"
"\tBasis-Lautstärke: %s%s%s\n"
"\tSenke-Monitor: %s\n"
"\tLatenz: %0.0f usec, eingestellt %0.0f usec\n"
"\tFlags: %s%s%s%s%s%s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:877 src/utils/pactl.c:962 src/utils/pactl.c:1062
#: src/utils/pactl.c:1232 src/utils/pactl.c:1384 src/utils/pactl.c:1385
#: src/utils/pactl.c:1396 src/utils/pactl.c:1507 src/utils/pactl.c:1508
#: src/utils/pactl.c:1519 src/utils/pactl.c:1557 src/utils/pactl.c:1582
#: src/utils/pactl.c:1624
msgid "n/a"
msgstr "k. A."

#: src/utils/pactl.c:924 src/utils/pactl.c:1793
#, c-format
msgid "Failed to get module information: %s"
msgstr "Beziehen der Modul-Information fehlgeschlagen: %s"

#: src/utils/pactl.c:976
#, c-format
msgid ""
"Module #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tUsage counter: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Modul #%u\n"
"\tName: %s\n"
"\tArgument: %s\n"
"\tNutzungszähler: %s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1002
#, c-format
msgid "Failed to get client information: %s"
msgstr "Beziehen der Client-Information fehlgeschlagen: %s"

#: src/utils/pactl.c:1056
#, c-format
msgid ""
"Client #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Client #%u\n"
"\tTreiber: %s\n"
"\tOwner-Modul: %s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1168
#, c-format
msgid "Failed to get card information: %s"
msgstr "Beziehen der Karten-Information fehlgeschlagen: %s"

#: src/utils/pactl.c:1224
#, c-format
msgid ""
"Card #%u\n"
"\tName: %s\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Karte #%u\n"
"\tName: %s\n"
"\tTreiber: %s\n"
"\tOwner-Modul: %s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1238
#, c-format
msgid "\tProfiles:\n"
msgstr "\tProfile:\n"

#: src/utils/pactl.c:1240
#, c-format
msgid "\t\t%s: %s (sinks: %u, sources: %u, priority: %u, available: %s)\n"
msgstr ""

#: src/utils/pactl.c:1245
#, c-format
msgid "\tActive Profile: %s\n"
msgstr "\tAktive Profile: %s\n"

#: src/utils/pactl.c:1254
#, c-format
msgid ""
"\t\t%s: %s (type: %s, priority: %u, latency offset: %<PRId64> usec%s%s, %s)\n"
msgstr ""

#: src/utils/pactl.c:1261
#, c-format
msgid ""
"\t\t\tProperties:\n"
"\t\t\t\t%s\n"
msgstr ""

#: src/utils/pactl.c:1265
#, c-format
msgid "\t\t\tPart of profile(s): %s"
msgstr ""

#: src/utils/pactl.c:1290 src/utils/pactl.c:1954 src/utils/pactl.c:2022
#, c-format
msgid "Failed to get sink input information: %s"
msgstr "Konnte Sink-Eingabe-Informationen nicht holen: %s"

#: src/utils/pactl.c:1366
#, fuzzy, c-format
msgid ""
"Sink Input #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSink Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Eingabe-Senke #%u\n"
"\tTreiber: %s\n"
"\tOwner-Modul: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample-Angabe: %s\n"
"\tKanalzuordnung: %s\n"
"\tStumm: %s\n"
"\tLautstärke: %s\n"
"\t        %s\n"
"\t        Verteilung %0.2f\n"
"\tPufferlatenz: %0.0f usec\n"
"\tSink-Latenz: %0.0f usec\n"
"\tResample-Methode: %s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1413 src/utils/pactl.c:1974 src/utils/pactl.c:2037
#, c-format
msgid "Failed to get source output information: %s"
msgstr "Konnte Informationen über Quell-Ausgabe nicht holen: %s"

#: src/utils/pactl.c:1489
#, fuzzy, c-format
msgid ""
"Source Output #%u\n"
"\tDriver: %s\n"
"\tOwner Module: %s\n"
"\tClient: %s\n"
"\tSource: %u\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tFormat: %s\n"
"\tCorked: %s\n"
"\tMute: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tBuffer Latency: %0.0f usec\n"
"\tSource Latency: %0.0f usec\n"
"\tResample method: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Eingabe-Senke #%u\n"
"\tTreiber: %s\n"
"\tOwner-Modul: %s\n"
"\tClient: %s\n"
"\tSink: %u\n"
"\tSample-Angabe: %s\n"
"\tKanalzuordnung: %s\n"
"\tStumm: %s\n"
"\tLautstärke: %s\n"
"\t        %s\n"
"\t        Verteilung %0.2f\n"
"\tPufferlatenz: %0.0f usec\n"
"\tSink-Latenz: %0.0f usec\n"
"\tResample-Methode: %s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1536
#, c-format
msgid "Failed to get sample information: %s"
msgstr "Beziehen der Sample-Informationen fehlgeschlagen: %s"

#: src/utils/pactl.c:1604
#, fuzzy, c-format
msgid ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tVolume: %s\n"
"\t        balance %0.2f\n"
"\tDuration: %0.1fs\n"
"\tSize: %s\n"
"\tLazy: %s\n"
"\tFilename: %s\n"
"\tProperties:\n"
"\t\t%s\n"
msgstr ""
"Sample #%u\n"
"\tName: %s\n"
"\tSample Specification: %s\n"
"\tChannel Map: %s\n"
"\tLautstärke: %s\n"
"\t        %s\n"
"\t        Verteilung %0.2f\n"
"\tDauer: %0.1fs\n"
"\tGrösse: %s\n"
"\tLazy: %s\n"
"\tDateinamen: %s\n"
"\tEigenschaften:\n"
"\t\t%s\n"

#: src/utils/pactl.c:1633 src/utils/pactl.c:1643
#, c-format
msgid "Failure: %s"
msgstr "Fehlgeschlagen: %s"

#: src/utils/pactl.c:1667
#, fuzzy, c-format
msgid "Send message failed: %s"
msgstr "read() fehlgeschlagen: %s"

#: src/utils/pactl.c:1695
#, c-format
msgid "list-handlers message failed: %s"
msgstr ""

#: src/utils/pactl.c:1711 src/utils/pactl.c:1760
msgid "list-handlers message response could not be parsed correctly"
msgstr ""

#: src/utils/pactl.c:1718
msgid "list-handlers message response is not a JSON array"
msgstr ""

#: src/utils/pactl.c:1729
#, c-format
msgid "list-handlers message response array element %d is not a JSON object"
msgstr ""

#: src/utils/pactl.c:1800
#, fuzzy, c-format
msgid "Failed to unload module: Module %s not loaded"
msgstr "Hochladen des Sample fehlgeschlagen: %s"

#: src/utils/pactl.c:1818
#, c-format
msgid ""
"Failed to set volume: You tried to set volumes for %d channel, whereas "
"channel(s) supported = %d\n"
msgid_plural ""
"Failed to set volume: You tried to set volumes for %d channels, whereas "
"channel(s) supported = %d\n"
msgstr[0] ""
msgstr[1] ""

#: src/utils/pactl.c:2107
#, c-format
msgid "Failed to upload sample: %s"
msgstr "Hochladen des Sample fehlgeschlagen: %s"

#: src/utils/pactl.c:2124
msgid "Premature end of file"
msgstr "Dateiende ist zu früh aufgetreten"

#: src/utils/pactl.c:2144
msgid "new"
msgstr ""

#: src/utils/pactl.c:2147
msgid "change"
msgstr ""

#: src/utils/pactl.c:2150
msgid "remove"
msgstr ""

#: src/utils/pactl.c:2153 src/utils/pactl.c:2188
msgid "unknown"
msgstr ""

#: src/utils/pactl.c:2161
msgid "sink"
msgstr "Sink"

#: src/utils/pactl.c:2164
msgid "source"
msgstr "Quelle"

#: src/utils/pactl.c:2167
msgid "sink-input"
msgstr ""

#: src/utils/pactl.c:2170
#, fuzzy
msgid "source-output"
msgstr "Quelle"

#: src/utils/pactl.c:2173
msgid "module"
msgstr ""

#: src/utils/pactl.c:2176
msgid "client"
msgstr ""

#: src/utils/pactl.c:2179
msgid "sample-cache"
msgstr ""

#: src/utils/pactl.c:2182
#, fuzzy
msgid "server"
msgstr "Ungültiger Server"

#: src/utils/pactl.c:2185
msgid "card"
msgstr ""

#: src/utils/pactl.c:2206
#, c-format
msgid "Event '%s' on %s #%u\n"
msgstr ""

#: src/utils/pactl.c:2514
msgid "Got SIGINT, exiting."
msgstr "SIGINT empfangen, beenden."

#: src/utils/pactl.c:2547
msgid "Invalid volume specification"
msgstr "Ungültige Sample-Angaben"

#: src/utils/pactl.c:2581
msgid "Volume outside permissible range.\n"
msgstr ""

#: src/utils/pactl.c:2594
#, fuzzy
msgid "Invalid number of volume specifications.\n"
msgstr "Ungültige Sample-Angaben"

#: src/utils/pactl.c:2606
#, fuzzy
msgid "Inconsistent volume specification.\n"
msgstr "Ungültige Sample-Angaben"

#: src/utils/pactl.c:2636 src/utils/pactl.c:2637 src/utils/pactl.c:2638
#: src/utils/pactl.c:2639 src/utils/pactl.c:2640 src/utils/pactl.c:2641
#: src/utils/pactl.c:2642 src/utils/pactl.c:2643 src/utils/pactl.c:2644
#: src/utils/pactl.c:2645 src/utils/pactl.c:2646 src/utils/pactl.c:2647
#: src/utils/pactl.c:2648 src/utils/pactl.c:2649 src/utils/pactl.c:2650
#: src/utils/pactl.c:2651 src/utils/pactl.c:2652 src/utils/pactl.c:2653
#: src/utils/pactl.c:2654 src/utils/pactl.c:2655 src/utils/pactl.c:2656
#: src/utils/pactl.c:2657 src/utils/pactl.c:2658 src/utils/pactl.c:2659
#: src/utils/pactl.c:2660
msgid "[options]"
msgstr ""

#: src/utils/pactl.c:2638
msgid "[TYPE]"
msgstr ""

#: src/utils/pactl.c:2640
msgid "FILENAME [NAME]"
msgstr ""

#: src/utils/pactl.c:2641
msgid "NAME [SINK]"
msgstr ""

#: src/utils/pactl.c:2653
msgid "NAME|#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2654
msgid "#N VOLUME [VOLUME ...]"
msgstr ""

#: src/utils/pactl.c:2655
msgid "NAME|#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2656
msgid "#N 1|0|toggle"
msgstr ""

#: src/utils/pactl.c:2657
msgid "#N FORMATS"
msgstr ""

#: src/utils/pactl.c:2661
#, c-format
msgid ""
"\n"
"The special names @DEFAULT_SINK@, @DEFAULT_SOURCE@ and @DEFAULT_MONITOR@\n"
"can be used to specify the default sink, source and monitor.\n"
msgstr ""

#: src/utils/pactl.c:2664
#, fuzzy, c-format
msgid ""
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"\n"
"  -f, --format=FORMAT                   The format of the output. Either "
"\"normal\" or \"json\"\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"  -n, --client-name=NAME                How to call this client on the "
"server\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Diese Hilfe zeigen\n"
"      --version                         Zeige Version\n"
"  -s, --server=SERVER                   Name des Zielservers\n"
"\n"

#: src/utils/pactl.c:2707
#, c-format
msgid ""
"pactl %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pactl %s\n"
"Kompiliert mit libpulse %s\n"
"Gelinkt mit libpulse %s\n"

#: src/utils/pactl.c:2751
#, fuzzy, c-format
msgid "Invalid format value '%s'"
msgstr "Ungültiger Stream-Name '%s'"

#: src/utils/pactl.c:2778
#, c-format
msgid "Specify nothing, or one of: %s"
msgstr ""

#: src/utils/pactl.c:2788
msgid "Please specify a sample file to load"
msgstr "Geben Sie eine zu öffnende Sample-Datei an"

#: src/utils/pactl.c:2801
msgid "Failed to open sound file."
msgstr "Öffnen der Audio-Datei fehlgeschlagen."

#: src/utils/pactl.c:2813
msgid "Warning: Failed to determine sample specification from file."
msgstr "Warnung: Beziehen der Sample-Angabe aus Datei fehlgeschlagen."

#: src/utils/pactl.c:2823
msgid "You have to specify a sample name to play"
msgstr "Sie müssen eine abzuspielende Sample-Datei angeben"

#: src/utils/pactl.c:2835
msgid "You have to specify a sample name to remove"
msgstr "Sie müssen eine zu löschende Sample-Datei angeben"

#: src/utils/pactl.c:2844
msgid "You have to specify a sink input index and a sink"
msgstr "Sie müssen einen Sink-Eingabe-Indexwert und einen Sink angeben"

#: src/utils/pactl.c:2854
msgid "You have to specify a source output index and a source"
msgstr ""
"Sie müssen eine Indexwert für die Quell-Ausgabe und eine Quelle angeben"

#: src/utils/pactl.c:2869
msgid "You have to specify a module name and arguments."
msgstr "Sie müssen einen Modulnamen angeben und Argumente übergeben."

#: src/utils/pactl.c:2889
#, fuzzy
msgid "You have to specify a module index or name"
msgstr "Sie müssen einen Indexwert für ein Modul angeben"

#: src/utils/pactl.c:2902
msgid ""
"You may not specify more than one sink. You have to specify a boolean value."
msgstr ""
"Sie sollten nur eine Senke angeben. Sie müssen zumindest einen bool'schen "
"Wert übergeben."

#: src/utils/pactl.c:2907 src/utils/pactl.c:2927
#, fuzzy
msgid "Invalid suspend specification."
msgstr "Ungültige Sample-Angaben"

#: src/utils/pactl.c:2922
msgid ""
"You may not specify more than one source. You have to specify a boolean "
"value."
msgstr ""
"Sie sollten nur eine Quelle angeben. Sie müssen zumindest einen bool'schen "
"Wert übergeben."

#: src/utils/pactl.c:2939
msgid "You have to specify a card name/index and a profile name"
msgstr "Sie müssen einen Karten-Name/Indexwert und einen Profilnamen angeben"

#: src/utils/pactl.c:2950
msgid "You have to specify a sink name/index and a port name"
msgstr "Sie müssen einen Senkennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:2961
#, fuzzy
msgid "You have to specify a sink name"
msgstr "Sie müssen eine abzuspielende Sample-Datei angeben"

#: src/utils/pactl.c:2974
msgid "You have to specify a source name/index and a port name"
msgstr "Sie müssen einen Quellennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:2985
#, fuzzy
msgid "You have to specify a source name"
msgstr "Sie müssen einen Indexwert für ein Modul angeben"

#: src/utils/pactl.c:2998 src/utils/pactl.c:3076
#, fuzzy
msgid "You have to specify a sink name/index"
msgstr "Sie müssen eine abzuspielende Sample-Datei angeben"

#: src/utils/pactl.c:3008
msgid "You have to specify a sink name/index and a volume"
msgstr "Sie müssen einen Senkennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:3021 src/utils/pactl.c:3101
#, fuzzy
msgid "You have to specify a source name/index"
msgstr "Sie müssen einen Indexwert für ein Modul angeben"

#: src/utils/pactl.c:3031
msgid "You have to specify a source name/index and a volume"
msgstr "Sie müssen einen Quellennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:3044
msgid "You have to specify a sink input index and a volume"
msgstr "Sie müssen einen Sink-Eingabe-Indexwert und einen Sink angeben"

#: src/utils/pactl.c:3049
msgid "Invalid sink input index"
msgstr "Ungültiger Sink-Eingabe-Index"

#: src/utils/pactl.c:3060
#, fuzzy
msgid "You have to specify a source output index and a volume"
msgstr ""
"Sie müssen eine Indexwert für die Quell-Ausgabe und eine Quelle angeben"

#: src/utils/pactl.c:3065
#, fuzzy
msgid "Invalid source output index"
msgstr "Ungültiger Sink-Eingabe-Index"

#: src/utils/pactl.c:3086
#, fuzzy
msgid ""
"You have to specify a sink name/index and a mute action (0, 1, or 'toggle')"
msgstr "Sie müssen einen Senkennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:3091 src/utils/pactl.c:3116 src/utils/pactl.c:3136
#: src/utils/pactl.c:3154
#, fuzzy
msgid "Invalid mute specification"
msgstr "Ungültige Sample-Angaben"

#: src/utils/pactl.c:3111
#, fuzzy
msgid ""
"You have to specify a source name/index and a mute action (0, 1, or 'toggle')"
msgstr "Sie müssen einen Quellennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:3126
#, fuzzy
msgid ""
"You have to specify a sink input index and a mute action (0, 1, or 'toggle')"
msgstr "Sie müssen einen Sink-Eingabe-Indexwert und einen Sink angeben"

#: src/utils/pactl.c:3131
msgid "Invalid sink input index specification"
msgstr "Ungültige Sink-Eingabe-Index-Angaben"

#: src/utils/pactl.c:3144
#, fuzzy
msgid ""
"You have to specify a source output index and a mute action (0, 1, or "
"'toggle')"
msgstr "Sie müssen einen Quellennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:3149
#, fuzzy
msgid "Invalid source output index specification"
msgstr "Ungültige Sink-Eingabe-Index-Angaben"

#: src/utils/pactl.c:3162
#, fuzzy
msgid "You have to specify at least an object path and a message name"
msgstr "Sie müssen einen Senkennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:3172
msgid ""
"Excess arguments given, they will be ignored. Note that all message "
"parameters must be given as a single string."
msgstr ""

#: src/utils/pactl.c:3182
#, fuzzy
msgid ""
"You have to specify a sink index and a semicolon-separated list of supported "
"formats"
msgstr "Sie müssen einen Senkennamen/-Indexwert und einen Portnamen angeben"

#: src/utils/pactl.c:3194
#, fuzzy
msgid "You have to specify a card name/index, a port name and a latency offset"
msgstr "Sie müssen einen Karten-Name/Indexwert und einen Profilnamen angeben"

#: src/utils/pactl.c:3201
msgid "Could not parse latency offset"
msgstr ""

#: src/utils/pactl.c:3213
msgid "No valid command specified."
msgstr "Kein gültiger Befehl angegeben."

#: src/utils/pasuspender.c:79
#, c-format
msgid "fork(): %s\n"
msgstr "fork(): %s\n"

#: src/utils/pasuspender.c:92
#, c-format
msgid "execvp(): %s\n"
msgstr "execvp(): %s\n"

#: src/utils/pasuspender.c:111
#, c-format
msgid "Failure to resume: %s\n"
msgstr "Resume fehlgeschlagen: %s\n"

#: src/utils/pasuspender.c:145
#, c-format
msgid "Failure to suspend: %s\n"
msgstr "Aussetzen fehlgeschlagen: %s\n"

#: src/utils/pasuspender.c:170
#, c-format
msgid "WARNING: Sound server is not local, not suspending.\n"
msgstr "WARNUNG: Sound-Server läuft nicht lokal, nicht ausgesetzt.\n"

#: src/utils/pasuspender.c:183
#, c-format
msgid "Connection failure: %s\n"
msgstr "Verbindungsfehler: %s\n"

#: src/utils/pasuspender.c:201
#, c-format
msgid "Got SIGINT, exiting.\n"
msgstr "SIGINT empfangen, beende.\n"

#: src/utils/pasuspender.c:219
#, c-format
msgid "WARNING: Child process terminated by signal %u\n"
msgstr "WARNUNG: Kind-Prozess durch Signal %u beendet\n"

#: src/utils/pasuspender.c:228
#, fuzzy, c-format
msgid ""
"%s [options] -- PROGRAM [ARGUMENTS ...]\n"
"\n"
"Temporarily suspend PulseAudio while PROGRAM runs.\n"
"\n"
"  -h, --help                            Show this help\n"
"      --version                         Show version\n"
"  -s, --server=SERVER                   The name of the server to connect "
"to\n"
"\n"
msgstr ""
"%s [options] ... \n"
"\n"
"  -h, --help                            Diese Hilfe zeigen\n"
"      --version                         Zeige Version\n"
"  -s, --server=SERVER                   Name des Zielservers\n"
"\n"

#: src/utils/pasuspender.c:267
#, c-format
msgid ""
"pasuspender %s\n"
"Compiled with libpulse %s\n"
"Linked with libpulse %s\n"
msgstr ""
"pasuspender %s\n"
"kompiliert mit libpulse %s\n"
"Gelinkt mit libpulse %s\n"

#: src/utils/pasuspender.c:296
#, c-format
msgid "pa_mainloop_new() failed.\n"
msgstr "pa_mainloop_new() fehlgeschlagen.\n"

#: src/utils/pasuspender.c:309
#, c-format
msgid "pa_context_new() failed.\n"
msgstr "pa_context_new() fehlgeschlagen.\n"

#: src/utils/pasuspender.c:321
#, c-format
msgid "pa_mainloop_run() failed.\n"
msgstr "pa_mainloop_run() fehlgeschlagen.\n"

#: src/utils/pax11publish.c:58
#, c-format
msgid ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Show current PulseAudio data attached to X11 display (default)\n"
" -e    Export local PulseAudio data to X11 display\n"
" -i    Import PulseAudio data from X11 display to local environment "
"variables and cookie file.\n"
" -r    Remove PulseAudio data from X11 display\n"
msgstr ""
"%s [-D display] [-S server] [-O sink] [-I source] [-c file]  [-d|-e|-i|-r]\n"
"\n"
" -d    Zeige aktuell mit X11-Anzeige verbundene PulseAudio-Daten (Standard)\n"
" -e    Lokale PulseAudio-Daten an X11-Anzeige exportieren\n"
" -i    PulseAudio-Daten von X11-Anzeige in lokale Umgebungsvariablen und "
"Cookie importieren.\n"
" -r    PulseAudio-Daten von X11-Anzeige löschen\n"

#: src/utils/pax11publish.c:91
#, c-format
msgid "Failed to parse command line.\n"
msgstr "Parsen der Kommandozeile fehlgeschlagen.\n"

#: src/utils/pax11publish.c:110
#, c-format
msgid "Server: %s\n"
msgstr "Server: %s\n"

#: src/utils/pax11publish.c:112
#, c-format
msgid "Source: %s\n"
msgstr "Quelle: %s\n"

#: src/utils/pax11publish.c:114
#, c-format
msgid "Sink: %s\n"
msgstr "Sink: %s\n"

#: src/utils/pax11publish.c:116
#, c-format
msgid "Cookie: %s\n"
msgstr "Cookie: %s\n"

#: src/utils/pax11publish.c:134
#, c-format
msgid "Failed to parse cookie data\n"
msgstr "Paresen der Cookie-Daten fehlgeschlagen.\n"

#: src/utils/pax11publish.c:139
#, c-format
msgid "Failed to save cookie data\n"
msgstr "Speichern der Cookie-Daten fehlgeschlagen\n"

#: src/utils/pax11publish.c:168
#, c-format
msgid "Failed to get FQDN.\n"
msgstr "Beziehen des FQDN fehlgeschlagen.\n"

#: src/utils/pax11publish.c:188
#, c-format
msgid "Failed to load cookie data\n"
msgstr "Laden der Cookie-Daten fehlgeschlagen\n"

#: src/utils/pax11publish.c:206
#, c-format
msgid "Not yet implemented.\n"
msgstr "Noch nicht implementiert.\n"

#~ msgid "Got signal %s."
#~ msgstr "Signal %s empfangen."

#~ msgid "Exiting."
#~ msgstr "Wird beendet."

#~ msgid "Found user '%s' (UID %lu) and group '%s' (GID %lu)."
#~ msgstr "Benutzer '%s' (UID %lu) und Gruppe '%s' (GID %lu) gefunden."

#~ msgid "Successfully dropped root privileges."
#~ msgstr "Root-Berechtigungen erfolgreich zurückgesetzt."

#~ msgid "setrlimit(%s, (%u, %u)) failed: %s"
#~ msgstr "setrlimit(%s, (%u, %u)) fehlgeschlagen: %s"

#~ msgid "Daemon not running"
#~ msgstr "Daemon läuft nicht"

#~ msgid "Daemon running as PID %u"
#~ msgstr "Daemon läuft als PID %u"

#~ msgid "Daemon startup successful."
#~ msgstr "Start des Daemons erfolgreich."

#~ msgid "This is PulseAudio %s"
#~ msgstr "Dies ist PulseAudio %s"

#~ msgid "Compilation host: %s"
#~ msgstr "Kompilier-Host: %s"

#~ msgid "Compilation CFLAGS: %s"
#~ msgstr "Kompilier-CFLAGS: %s"

#~ msgid "Running on host: %s"
#~ msgstr "Laufe auf Host: %s"

#~ msgid "Found %u CPUs."
#~ msgstr "%u CPUs gefunden."

#~ msgid "Page size is %lu bytes"
#~ msgstr "Seitengröße ist %lu Bytes."

#~ msgid "Compiled with Valgrind support: yes"
#~ msgstr "Kompiliere mit Valgrind-Unterstützung: ja"

#~ msgid "Compiled with Valgrind support: no"
#~ msgstr "Kompiliere mit Valgrind-Unterstützung: nein"

#~ msgid "Running in valgrind mode: %s"
#~ msgstr "Läuft im Valgrind-Modus: %s"

#, fuzzy
#~ msgid "Running in VM: %s"
#~ msgstr "Laufe auf Host: %s"

#~ msgid "Optimized build: yes"
#~ msgstr "Optimiertes Build: ja"

#~ msgid "Optimized build: no"
#~ msgstr "Optimiertes Build: nein"

#~ msgid "NDEBUG defined, all asserts disabled."
#~ msgstr "NDEBUG definiert, alle Ansprüche deaktiviert."

#~ msgid "FASTPATH defined, only fast path asserts disabled."
#~ msgstr "FASTPATH definiert, nur fast-path-Ansprüche deaktiviert."

#~ msgid "All asserts enabled."
#~ msgstr "Alle Ansprüche aktiviert."

#~ msgid "Machine ID is %s."
#~ msgstr "System- ID ist %s."

#~ msgid "Session ID is %s."
#~ msgstr "System- ID ist %s."

#~ msgid "Using runtime directory %s."
#~ msgstr "Nutze Laufzeit-Verzeichnis %s."

#~ msgid "Using state directory %s."
#~ msgstr "Nutze Zustands-Verzeichnis %s."

#~ msgid "Using modules directory %s."
#~ msgstr "Modul-Verzeichnis %s benutzen."

#~ msgid "Running in system mode: %s"
#~ msgstr "Laufe im System-Modus: %s"

#~ msgid "Fresh high-resolution timers available! Bon appetit!"
#~ msgstr "Neue hochauslösende Timer verfügbar! Guten Appetit!"

#~ msgid ""
#~ "Dude, your kernel stinks! The chef's recommendation today is Linux with "
#~ "high-resolution timers enabled!"
#~ msgstr ""
#~ "Der Chefkoch empfiehlt: Linux mit aktivierten hochauslösenden Timern!"

#~ msgid "Failed to initialize daemon."
#~ msgstr "Konnte Daemon nicht initialisieren."

#~ msgid "Daemon startup complete."
#~ msgstr "Start des Daemons abgeschlossen."

#~ msgid "Daemon shutdown initiated."
#~ msgstr "Herunterfahren des Daemon gestartet."

#~ msgid "Daemon terminated."
#~ msgstr "Daemon beendet."

#~ msgid "Cleaning up privileges."
#~ msgstr "Root-Privilegien aufräumen."

#, fuzzy
#~ msgid "PulseAudio Sound System KDE Routing Policy"
#~ msgstr "PulseAudio Sound System"

#, fuzzy
#~ msgid "Start the PulseAudio Sound System with KDE Routing Policy"
#~ msgstr "Das PulseAudio Sound System starten"

#~ msgid "Failed to open configuration file '%s': %s"
#~ msgstr "Konfigurationsdatei '%s' konnte nicht geöffnet werden: %s"

#~ msgid "No cookie loaded. Attempting to connect without."
#~ msgstr "Verbindungsversuch ohne Cookie, da keines geladen."

#~ msgid "Failed to load client configuration file.\n"
#~ msgstr "Laden der Client-Konfigurationsdatei fehlgeschlagen.\n"

#~ msgid "Failed to read environment configuration data.\n"
#~ msgstr "Lesen the Umgebungsdaten fehlgeschlagen.\n"

#~ msgid "Telephony Duplex (HSP/HFP)"
#~ msgstr "Telephony Duplex (HSP/HFP)"

#, fuzzy
#~ msgid "Digital Passthrough (S/PDIF)"
#~ msgstr "Digital Stereo (HDMI)"

#, fuzzy
#~ msgid "Digital Passthrough  (IEC958)"
#~ msgstr "Digital Stereo (IEC958)"

#~ msgid "[%s:%u] rlimit not supported on this platform."
#~ msgstr "[%s:%u] rlimit auf dieser Plattform nicht unterstützt."

#~ msgid "XOpenDisplay() failed"
#~ msgstr "XOpenDisplay() fehlgeschlagen"

#~ msgid ""
#~ "Source Output #%u\n"
#~ "\tDriver: %s\n"
#~ "\tOwner Module: %s\n"
#~ "\tClient: %s\n"
#~ "\tSource: %u\n"
#~ "\tSample Specification: %s\n"
#~ "\tChannel Map: %s\n"
#~ "\tBuffer Latency: %0.0f usec\n"
#~ "\tSource Latency: %0.0f usec\n"
#~ "\tResample method: %s\n"
#~ "\tProperties:\n"
#~ "\t\t%s\n"
#~ msgstr ""
#~ "Quell-Ausgabe #%u\n"
#~ "\tTreiber: %s\n"
#~ "\tOwner-Modul: %s\n"
#~ "\tClient: %s\n"
#~ "\tQuelle: %u\n"
#~ "\tSample-Spezifizierung: %s\n"
#~ "\tKanalzuordnung: %s\n"
#~ "\tPufferlatenz: %0.0f usec\n"
#~ "\tQuelllatenz: %0.0f usec\n"
#~ "\tResample-Methode: %s\n"
#~ "\tEigenschaften:\n"
#~ "\t\t%s\n"

#, fuzzy
#~ msgid ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "%s [options] subscribe\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ msgstr ""
#~ "%s [options] stat\n"
#~ "%s [options] list\n"
#~ "%s [options] exit\n"
#~ "%s [options] upload-sample FILENAME [NAME]\n"
#~ "%s [options] play-sample NAME [SINK]\n"
#~ "%s [options] remove-sample NAME\n"
#~ "%s [options] move-sink-input SINKINPUT SINK\n"
#~ "%s [options] move-source-output SOURCEOUTPUT SOURCE\n"
#~ "%s [options] load-module NAME [ARGS ...]\n"
#~ "%s [options] unload-module MODULE\n"
#~ "%s [options] suspend-sink SINK 1|0\n"
#~ "%s [options] suspend-source SOURCE 1|0\n"
#~ "%s [options] set-card-profile CARD PROFILE\n"
#~ "%s [options] set-sink-port SINK PORT\n"
#~ "%s [options] set-source-port SOURCE PORT\n"
#~ "%s [options] set-sink-volume SINK VOLUME\n"
#~ "%s [options] set-source-volume SOURCE VOLUME\n"
#~ "%s [options] set-sink-input-volume SINKINPUT VOLUME\n"
#~ "%s [options] set-sink-mute SINK 1|0\n"
#~ "%s [options] set-source-mute SOURCE 1|0\n"
#~ "%s [options] set-sink-input-mute SINKINPUT 1|0\n"
#~ "\n"
#~ "  -h, --help                            Diese Hilfe anzeigen\n"
#~ "      --version                         Version anzeigen\n"
#~ "\n"
#~ "  -s, --server=SERVER                   Name des Zielservers\n"
#~ "  -n, --client-name=NAME                Rufname des Clients auf dem "
#~ "Server\n"

#, fuzzy
#~ msgid "%s+%s"
#~ msgstr "%s %s"

#, fuzzy
#~ msgid "%s / %s"
#~ msgstr "%s %s"

#, fuzzy
#~ msgid "Digital Surround 4.0 (IEC958)"
#~ msgstr "Digital Surround 4.0 (IEC958/AC3)"

#~ msgid "Low Frequency Emmiter"
#~ msgstr "Niedrigfrequenzemitter"

#~ msgid "Invalid client name '%s'\n"
#~ msgstr "Ungültiger Client-Name '%s'\n"

#~ msgid "Failed to determine sample specification from file.\n"
#~ msgstr "Beziehen der Sample-Informationen der Datei fehlgeschlagen.\n"

#~ msgid "select(): %s"
#~ msgstr "select(): %s"

#~ msgid "Cannot connect to system bus: %s"
#~ msgstr "Kann nicht mit dem System-Bus verbinden: %s"

#~ msgid "Cannot get caller from PID: %s"
#~ msgstr "Kann Caller von PID nicht beziehen: %s"

#~ msgid "Cannot set UID on caller object."
#~ msgstr "Kann UID für Caller-Objekt nicht setzen."

#~ msgid "Failed to get CK session."
#~ msgstr "Kann CK-Session nicht beziehen."

#~ msgid "Cannot set UID on session object."
#~ msgstr "Kann UID für Session-Objekt nicht setzen."

#~ msgid "Cannot allocate PolKitAction."
#~ msgstr "Konnte PolKitAction nicht zuordnen."

#~ msgid "Cannot set action_id"
#~ msgstr "Kann action_id nicht setzen"

#~ msgid "Cannot allocate PolKitContext."
#~ msgstr "Konnte PolKitContext nicht zuordnen."

#~ msgid "Cannot initialize PolKitContext: %s"
#~ msgstr "Konnte PolKitContext nicht initialisieren: %s"

#~ msgid "Could not determine whether caller is authorized: %s"
#~ msgstr "Autorisierung des Callers konnte nicht sichergestellt werden: %s"

#~ msgid "Cannot obtain auth: %s"
#~ msgstr "Keine Authorisierung erhalten: %s"

#~ msgid "PolicyKit responded with '%s'"
#~ msgstr "PolicyKit antwortete mit '%s'"

#, fuzzy
#~ msgid ""
#~ "High-priority scheduling (negative Unix nice level) for the PulseAudio "
#~ "daemon"
#~ msgstr ""
#~ "Hochprioritäts-Terminierung () (negative Unix nice level) für den "
#~ "PulseAudio-Dienst"

#~ msgid "Real-time scheduling for the PulseAudio daemon"
#~ msgstr "Echtzeit-Terminierung des PulseAudio-Daemon"

#~ msgid ""
#~ "System policy prevents PulseAudio from acquiring high-priority scheduling."
#~ msgstr ""
#~ "System-Richtlinien verhindert PulseAudio beim Erlangen des high-priority "
#~ "scheduling."

#~ msgid ""
#~ "System policy prevents PulseAudio from acquiring real-time scheduling."
#~ msgstr ""
#~ "System-Richtlinien verhindert PulseAudio beim Erlangen der Echtzeit-"
#~ "Terminierung."

#~ msgid "read() failed: %s\n"
#~ msgstr "read() fehlgeschlagen: %s\n"

#~ msgid "pa_context_connect() failed: %s\n"
#~ msgstr "pa_context_connect() fehlgeschlagen: %s\n"

#~ msgid "We're in the group '%s', allowing high-priority scheduling."
#~ msgstr ""
#~ "Wir befinden uns in der Gruppe '%s', was Scheduling höchster Priorität "
#~ "ermöglicht."

#~ msgid "We're in the group '%s', allowing real-time scheduling."
#~ msgstr ""
#~ "Wir befinden uns in der Gruppe '%s', was Echtzeit-Scheduling ermöglicht."

#~ msgid "PolicyKit grants us acquire-high-priority privilege."
#~ msgstr "Richtlinien gewähren das Recht aquire-high-priority."

#~ msgid "PolicyKit refuses acquire-high-priority privilege."
#~ msgstr "Richtlinien verweigern das Recht acquire-high-priority."

#~ msgid "PolicyKit grants us acquire-real-time privilege."
#~ msgstr "Richtlinien gewähren das Recht aquire-real-time."

#~ msgid "PolicyKit refuses acquire-real-time privilege."
#~ msgstr "Richtlinien verweigern das Recht acquire-real-time."

#, fuzzy
#~ msgid ""
#~ "Called SUID root and real-time and/or high-priority scheduling was "
#~ "requested in the configuration. However, we lack the necessary "
#~ "privileges:\n"
#~ "We are not in group '%s', PolicyKit refuse to grant us the requested "
#~ "privileges and we have no increase RLIMIT_NICE/RLIMIT_RTPRIO resource "
#~ "limits.\n"
#~ "For enabling real-time/high-priority scheduling please acquire the "
#~ "appropriate PolicyKit privileges, or become a member of '%s', or increase "
#~ "the RLIMIT_NICE/RLIMIT_RTPRIO resource limits for this user."
#~ msgstr ""
#~ "' und PolicyKit verweigern diese Rechte. Verwerfe SUID wieder.\n"
#~ "Erlangen Sie die den Richtlinien entsprechenden Rechte, um Echtzeit-"
#~ "Scheduling zu aktivieren oder werden Sie Mitglied der Gruppe '"

#~ msgid ""
#~ "High-priority scheduling enabled in configuration but not allowed by "
#~ "policy."
#~ msgstr "Scheduling höchster Priorität konfiguriert, jedoch nicht erlaubt."

#~ msgid "Successfully increased RLIMIT_RTPRIO"
#~ msgstr "RLIMIT_RTPRIO erfolgreich erhöht"

#~ msgid "RLIMIT_RTPRIO failed: %s"
#~ msgstr "RLIMIT_RTPRIO fehlgeschlagen: %s"

#~ msgid "Giving up CAP_NICE"
#~ msgstr "Verwerfe CAP_NICE"

#~ msgid ""
#~ "Real-time scheduling enabled in configuration but not allowed by policy."
#~ msgstr "Echtzeit-Scheduling konfiguriert, jedoch nicht erlaubt."

#~ msgid "Limited capabilities successfully to CAP_SYS_NICE."
#~ msgstr "Fähigkeiten erfolgreich auf CAP_SYS_NICE reduziert."

#~ msgid "time_new() failed.\n"
#~ msgstr "time_new() fehlgeschlagen.\n"

#~ msgid "Output %s + Input %s"
#~ msgstr "Ausgabe %s + Eingabe %s"

#~ msgid "Stream successfully created\n"
#~ msgstr "Stream erfolgreich erzeugt\n"

#~ msgid "Stream errror: %s\n"
#~ msgstr "Stream-Fehler: %s\n"

#~ msgid "Connection established.\n"
#~ msgstr "Verbindung hergestellt.\n"

#~ msgid ""
#~ "%s [options] [FILE]\n"
#~ "\n"
#~ "  -h, --help                            Show this help\n"
#~ "      --version                         Show version\n"
#~ "\n"
#~ "  -v, --verbose                         Enable verbose operation\n"
#~ "\n"
#~ "  -s, --server=SERVER                   The name of the server to connect "
#~ "to\n"
#~ "  -d, --device=DEVICE                   The name of the sink to connect "
#~ "to\n"
#~ "  -n, --client-name=NAME                How to call this client on the "
#~ "server\n"
#~ "      --stream-name=NAME                How to call this stream on the "
#~ "server\n"
#~ "      --volume=VOLUME                   Specify the initial (linear) "
#~ "volume in range 0...65536\n"
#~ "      --channel-map=CHANNELMAP          Set the channel map to the use\n"
#~ msgstr ""
#~ "%s [options] [FILE]\n"
#~ "\n"
#~ "  -h, --help                            Zeige diese Hilfe\n"
#~ "      --version                         Zeige Version\n"
#~ "\n"
#~ "  -v, --verbose                         Ausführliche Meldungen\n"
#~ "\n"
#~ "  -s, --server=SERVER                   Name des Zielservers\n"
#~ "  -d, --device=DEVICE                   Name des Ziel-Sink\n"
#~ "  -n, --client-name=NAME                Rufname des Clients auf dem "
#~ "Server\n"
#~ "      --stream-name=NAME                Rufname des Streams auf dem "
#~ "Server\n"
#~ "      --volume=VOLUME                   Initiale (lineare) Lautstärke "
#~ "zwischen 0...65536\n"
#~ "      --channel-map=CHANNELMAP          Diese Kanalzuordnung nutzen\n"

#~ msgid ""
#~ "paplay %s\n"
#~ "Compiled with libpulse %s\n"
#~ "Linked with libpulse %s\n"
#~ msgstr ""
#~ "paplay %s\n"
#~ "Kompliert mit libpulse %s\n"
#~ "Gelinkt mit libpulse %s\n"

#~ msgid "Invalid channel map\n"
#~ msgstr "Ungültige Kanal-Zuweisung\n"

#~ msgid "Channel map doesn't match file.\n"
#~ msgstr "Kanal-Zuweisung stimmt mit Datei nicht überein.\n"

#~ msgid "Using sample spec '%s'\n"
#~ msgstr "Sampling-Angabe '%s' wird benutzt\n"

#, fuzzy
#~ msgid ""
#~ "Called SUID root and real-time and/or high-priority scheduling was "
#~ "requested in the configuration. However, we lack the necessary "
#~ "privileges:\n"
#~ "We are not in group '"
#~ msgstr ""
#~ "Konfiguration fordert Aufruf der SUID root und Echtzeit-Scheduling "
#~ "höchster Priorität. Allerdings fehlen die nötigen Rechte:\n"
#~ "Wir befinden uns nicht in der Gruppe '"

#~ msgid "--log-time boolean argument"
#~ msgstr "--log-time erfordert bool'schen Wert"

#~ msgid ""
#~ "', or increase the RLIMIT_NICE/RLIMIT_RTPRIO resource limits for this "
#~ "user."
#~ msgstr ""
#~ "' oder erhöhen sie die RLIMIT_NICE/RLIMIT_RTPRIO-Ressourcenbegrenzungen "
#~ "für diesen Nutzer."

#~ msgid "Default sink name (%s) does not exist in name register."
#~ msgstr "Vorgabename für Sink (%s) existiert nicht im Namensregister."

#~ msgid "Buffer overrun, dropping incoming data\n"
#~ msgstr "Pufferüberlauf, verwerfe eingehende Daten\n"

#~ msgid "pa_stream_drop() failed: %s\n"
#~ msgstr "pa_stream_drop() fehlgeschlagen: %s\n"

#~ msgid "muted"
#~ msgstr "stumm"

#~ msgid ""
#~ "*** Autoload Entry #%u ***\n"
#~ "Name: %s\n"
#~ "Type: %s\n"
#~ "Module: %s\n"
#~ "Argument: %s\n"
#~ msgstr ""
#~ "*** Autoload-Eintrag #%u ***\n"
#~ "Name: %s\n"
#~ "Typ: %s\n"
#~ "Modul: %s\n"
#~ "Argument: %s\n"

#~ msgid "socketpair(): %s"
#~ msgstr "socketpair(): %s"
