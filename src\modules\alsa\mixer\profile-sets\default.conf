# This file is part of PulseAudio.
#
# PulseAudio is free software; you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of the
# License, or (at your option) any later version.
#
# PulseAudio is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with PulseAudio; if not, see <http://www.gnu.org/licenses/>.

; Default profile definitions for the ALSA backend of PulseAudio. This
; is used as fallback for all cards that have no special mapping
; assigned (and should be good enough for the vast majority of
; cards). If you want to assign a different profile set than this one
; to a device, either set the udev property PULSE_PROFILE_SET for the
; card, or use the "profile_set" module argument when loading
; module-alsa-card.
;
; So what is this about? Simply, what we do here is map ALSA devices
; to how they are exposed in PA. We say which ALSA device string to
; use to open a device, which channel mapping to use then, and which
; mixer path to use. This is encoded in a 'mapping'. Multiple of these
; mappings can be bound together in a 'profile' which is then directly
; exposed in the UI as a card profile. Each mapping assigned to a
; profile will result in one sink/source to be created if the profile
; is selected for the card.
;
; Additionally, the path set configuration files can describe the
; decibel values assigned to the steps of the volume elements. This
; can be used to work around situations when the alsa driver doesn't
; provide any decibel information, or when the information is
; incorrect.


; [General]
; auto-profiles = no | yes                  # Instead of defining all profiles manually, autogenerate
;                                           # them by combining every input mapping with every output mapping.
;
; [Mapping id]
; device-strings = ...                      # ALSA device string. %f will be replaced by the card identifier.
; channel-map = ...                         # Channel mapping to use for this device
; description = ...                         # Description for the mapping. Note that it's better to set the description
;                                           # in the well_known_descriptions table in alsa-mixer.c than with this
;                                           # option, because the descriptions in alsa-mixer.c are translatable.
; description-key = ...                     # A custom key for the well_known_descriptions table (by default the mapping
;                                           # name is used).
; paths-input = ...                         # A list of mixer paths to use. Every path in this list will be probed.
;                                           # If multiple are found to be working they will be available as device ports
; paths-output = ...
; element-input = ...                       # Instead of configuring a full mixer path simply configure a single
;                                           # mixer element for volume/mute handling. The value can be an element
;                                           # name, or name and index separated by a comma.
; element-output = ...
; priority = ...
; direction = any | input | output          # Only useful for?
;
; exact-channels = yes | no                 # If no, and the exact number of channels is not supported,
;                                           # allow device to be opened with another channel count
; fallback = no | yes                       # This mapping will only be considered if all non-fallback mappings fail
; intended-roles = ...                      # Set the device.intended_roles property for the sink/source.
;
; [Profile id]
; input-mappings = ...                      # Lists mappings for sources on this profile, those mapping must be
;                                           # defined in this file too
; output-mappings = ...                     # Lists mappings for sinks on this profile, those mappings must be
;                                           # defined in this file too
; description = ...
; priority = ...                            # Numeric value to deduce priority for this profile
; skip-probe = no | yes                     # Skip probing for availability? If this is yes then this profile
;                                           # will be assumed as working without probing. Makes initialization
;                                           # a bit faster but only works if the card is really known well.
;
; fallback = no | yes                       # This profile will only be considered if all non-fallback profiles fail
; [DecibelFix element]                      # Decibel fixes can be used to work around missing or incorrect dB
;                                           # information from alsa. A decibel fix is a table that maps volume steps
;                                           # to decibel values for one volume element. The "element" part in the
;                                           # section title is the name of the volume element (or name and index
;                                           # separated by a comma).
;                                           #
;                                           # NOTE: This feature is meant just as a help for figuring out the correct
;                                           # decibel values. PulseAudio is not the correct place to maintain the
;                                           # decibel mappings!
;                                           #
;                                           # If you need this feature, then you should make sure that when you have
;                                           # the correct values figured out, the alsa driver developers get informed
;                                           # too, so that they can fix the driver.
;
; db-values = ...                           # The option value consists of pairs of step numbers and decibel values.
;                                           # The pairs are separated with whitespace, and steps are separated from
;                                           # the corresponding decibel values with a colon. The values must be in an
;                                           # increasing order. Here's an example of a valid string:
;                                           #
;                                           #     "0:-40.50  1:-38.70  3:-33.00  11:0"
;                                           #
;                                           # The lowest step imposes a lower limit for hardware volume and the
;                                           # highest step correspondingly imposes a higher limit. That means that
;                                           # that the mixer will never be set outside those values - the rest of the
;                                           # volume scale is done using software volume.
;                                           #
;                                           # As can be seen in the example, you don't need to specify a dB value for
;                                           # each step. The dB values for skipped steps will be linearly interpolated
;                                           # using the nearest steps that are given.

[General]
auto-profiles = yes

[Mapping analog-stereo]
device-strings = front:%f
channel-map = left,right
paths-output = analog-output analog-output-lineout analog-output-speaker analog-output-headphones analog-output-headphones-2
paths-input = analog-input-front-mic analog-input-rear-mic analog-input-internal-mic analog-input-dock-mic analog-input analog-input-mic analog-input-linein analog-input-aux analog-input-video analog-input-tvtuner analog-input-fm analog-input-mic-line analog-input-headphone-mic analog-input-headset-mic
priority = 15

# If everything else fails, try to use hw:0 as a stereo device...
[Mapping stereo-fallback]
device-strings = hw:%f
fallback = yes
channel-map = front-left,front-right
paths-output = analog-output analog-output-lineout analog-output-speaker analog-output-headphones analog-output-headphones-2
paths-input = analog-input-front-mic analog-input-rear-mic analog-input-internal-mic analog-input-dock-mic analog-input analog-input-mic analog-input-linein analog-input-aux analog-input-video analog-input-tvtuner analog-input-fm analog-input-mic-line analog-input-headphone-mic analog-input-headset-mic
priority = 1

# ...and if even that fails, try to use hw:0 as a mono device.
[Mapping mono-fallback]
device-strings = hw:%f
fallback = yes
channel-map = mono
paths-output = analog-output analog-output-lineout analog-output-speaker analog-output-headphones analog-output-headphones-2 analog-output-mono
paths-input = analog-input-front-mic analog-input-rear-mic analog-input-internal-mic analog-input-dock-mic analog-input analog-input-mic analog-input-linein analog-input-aux analog-input-video analog-input-tvtuner analog-input-fm analog-input-mic-line analog-input-headset-mic
priority = 1

[Mapping analog-surround-21]
device-strings = surround21:%f
channel-map = front-left,front-right,lfe
paths-output = analog-output analog-output-lineout analog-output-speaker
priority = 13
direction = output

[Mapping analog-surround-40]
device-strings = surround40:%f
channel-map = front-left,front-right,rear-left,rear-right
paths-output = analog-output analog-output-lineout analog-output-speaker
priority = 12
direction = output

[Mapping analog-surround-41]
device-strings = surround41:%f
channel-map = front-left,front-right,rear-left,rear-right,lfe
paths-output = analog-output analog-output-lineout analog-output-speaker
priority = 13
direction = output

[Mapping analog-surround-50]
device-strings = surround50:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center
paths-output = analog-output analog-output-lineout analog-output-speaker
priority = 12
direction = output

[Mapping analog-surround-51]
device-strings = surround51:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
paths-output = analog-output analog-output-lineout analog-output-speaker
priority = 13
direction = output

[Mapping analog-surround-71]
device-strings = surround71:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
description = Analog Surround 7.1
paths-output = analog-output analog-output-lineout analog-output-speaker
priority = 12
direction = output

[Mapping iec958-stereo]
device-strings = iec958:%f
channel-map = left,right
paths-input = iec958-stereo-input
paths-output = iec958-stereo-output
priority = 5

[Mapping iec958-ac3-surround-40]
device-strings = a52:%f
channel-map = front-left,front-right,rear-left,rear-right
paths-output = iec958-stereo-output
priority = 2
direction = output

[Mapping iec958-ac3-surround-51]
device-strings = a52:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
paths-output = iec958-stereo-output
priority = 3
direction = output

[Mapping iec958-dts-surround-51]
device-strings = dca:%f
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
paths-output = iec958-stereo-output
priority = 3
direction = output

[Mapping hdmi-stereo]
description = Digital Stereo (HDMI)
device-strings = hdmi:%f
paths-output = hdmi-output-0
channel-map = left,right
priority = 9
direction = output

[Mapping hdmi-surround]
description = Digital Surround 5.1 (HDMI)
device-strings = hdmi:%f
paths-output = hdmi-output-0
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 8
direction = output

[Mapping hdmi-surround71]
description = Digital Surround 7.1 (HDMI)
device-strings = hdmi:%f
paths-output = hdmi-output-0
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 8
direction = output

[Mapping hdmi-dts-surround]
description = Digital Surround 5.1 (HDMI/DTS)
device-strings = dcahdmi:%f
paths-output = hdmi-output-0
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra1]
description = Digital Stereo (HDMI 2)
device-strings = hdmi:%f,1
paths-output = hdmi-output-1
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra1]
description = Digital Surround 5.1 (HDMI 2)
device-strings = hdmi:%f,1
paths-output = hdmi-output-1
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra1]
description = Digital Surround 7.1 (HDMI 2)
device-strings = hdmi:%f,1
paths-output = hdmi-output-1
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra1]
description = Digital Surround 5.1 (HDMI 2/DTS)
device-strings = dcahdmi:%f,1
paths-output = hdmi-output-1
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra2]
description = Digital Stereo (HDMI 3)
device-strings = hdmi:%f,2
paths-output = hdmi-output-2
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra2]
description = Digital Surround 5.1 (HDMI 3)
device-strings = hdmi:%f,2
paths-output = hdmi-output-2
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra2]
description = Digital Surround 7.1 (HDMI 3)
device-strings = hdmi:%f,2
paths-output = hdmi-output-2
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra2]
description = Digital Surround 5.1 (HDMI 3/DTS)
device-strings = dcahdmi:%f,2
paths-output = hdmi-output-2
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra3]
description = Digital Stereo (HDMI 4)
device-strings = hdmi:%f,3
paths-output = hdmi-output-3
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra3]
description = Digital Surround 5.1 (HDMI 4)
device-strings = hdmi:%f,3
paths-output = hdmi-output-3
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra3]
description = Digital Surround 7.1 (HDMI 4)
device-strings = hdmi:%f,3
paths-output = hdmi-output-3
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra3]
description = Digital Surround 5.1 (HDMI 4/DTS)
device-strings = dcahdmi:%f,3
paths-output = hdmi-output-3
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra4]
description = Digital Stereo (HDMI 5)
device-strings = hdmi:%f,4
paths-output = hdmi-output-4
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra4]
description = Digital Surround 5.1 (HDMI 5)
device-strings = hdmi:%f,4
paths-output = hdmi-output-4
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra4]
description = Digital Surround 7.1 (HDMI 5)
device-strings = hdmi:%f,4
paths-output = hdmi-output-4
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra4]
description = Digital Surround 5.1 (HDMI 5/DTS)
device-strings = dcahdmi:%f,4
paths-output = hdmi-output-4
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra5]
description = Digital Stereo (HDMI 6)
device-strings = hdmi:%f,5
paths-output = hdmi-output-5
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra5]
description = Digital Surround 5.1 (HDMI 6)
device-strings = hdmi:%f,5
paths-output = hdmi-output-5
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra5]
description = Digital Surround 7.1 (HDMI 6)
device-strings = hdmi:%f,5
paths-output = hdmi-output-5
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra5]
description = Digital Surround 5.1 (HDMI 6/DTS)
device-strings = dcahdmi:%f,5
paths-output = hdmi-output-5
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra6]
description = Digital Stereo (HDMI 7)
device-strings = hdmi:%f,6
paths-output = hdmi-output-6
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra6]
description = Digital Surround 5.1 (HDMI 7)
device-strings = hdmi:%f,6
paths-output = hdmi-output-6
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra6]
description = Digital Surround 7.1 (HDMI 7)
device-strings = hdmi:%f,6
paths-output = hdmi-output-6
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra6]
description = Digital Surround 5.1 (HDMI 7/DTS)
device-strings = dcahdmi:%f,6
paths-output = hdmi-output-6
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra7]
description = Digital Stereo (HDMI 8)
device-strings = hdmi:%f,7
paths-output = hdmi-output-7
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra7]
description = Digital Surround 5.1 (HDMI 8)
device-strings = hdmi:%f,7
paths-output = hdmi-output-7
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra7]
description = Digital Surround 7.1 (HDMI 8)
device-strings = hdmi:%f,7
paths-output = hdmi-output-7
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra7]
description = Digital Surround 5.1 (HDMI 8/DTS)
device-strings = dcahdmi:%f,7
paths-output = hdmi-output-7
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra8]
description = Digital Stereo (HDMI 9)
device-strings = hdmi:%f,8
paths-output = hdmi-output-8
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra8]
description = Digital Surround 5.1 (HDMI 9)
device-strings = hdmi:%f,8
paths-output = hdmi-output-8
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra8]
description = Digital Surround 7.1 (HDMI 9)
device-strings = hdmi:%f,8
paths-output = hdmi-output-8
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra8]
description = Digital Surround 5.1 (HDMI 9/DTS)
device-strings = dcahdmi:%f,8
paths-output = hdmi-output-8
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra9]
description = Digital Stereo (HDMI 10)
device-strings = hdmi:%f,9
paths-output = hdmi-output-9
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra9]
description = Digital Surround 5.1 (HDMI 10)
device-strings = hdmi:%f,9
paths-output = hdmi-output-9
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra9]
description = Digital Surround 7.1 (HDMI 10)
device-strings = hdmi:%f,9
paths-output = hdmi-output-9
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra9]
description = Digital Surround 5.1 (HDMI 10/DTS)
device-strings = dcahdmi:%f,9
paths-output = hdmi-output-9
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-stereo-extra10]
description = Digital Stereo (HDMI 11)
device-strings = hdmi:%f,10
paths-output = hdmi-output-10
channel-map = left,right
priority = 7
direction = output

[Mapping hdmi-surround-extra10]
description = Digital Surround 5.1 (HDMI 11)
device-strings = hdmi:%f,10
paths-output = hdmi-output-10
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping hdmi-surround71-extra10]
description = Digital Surround 7.1 (HDMI 11)
device-strings = hdmi:%f,10
paths-output = hdmi-output-10
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe,side-left,side-right
priority = 6
direction = output

[Mapping hdmi-dts-surround-extra10]
description = Digital Surround 5.1 (HDMI 11/DTS)
device-strings = dcahdmi:%f,10
paths-output = hdmi-output-10
channel-map = front-left,front-right,rear-left,rear-right,front-center,lfe
priority = 6
direction = output

[Mapping multichannel-output]
device-strings = hw:%f
channel-map = left,right,rear-left,rear-right
exact-channels = false
fallback = yes
priority = 1
direction = output

[Mapping multichannel-input]
device-strings = hw:%f
channel-map = left,right,rear-left,rear-right
exact-channels = false
fallback = yes
priority = 1
direction = input

; An example for defining multiple-sink profiles
#[Profile output:analog-stereo+output:iec958-stereo+input:analog-stereo]
#description = Foobar
#output-mappings = analog-stereo iec958-stereo
#input-mappings = analog-stereo
