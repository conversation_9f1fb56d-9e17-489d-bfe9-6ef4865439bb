# The build has two stages. The 'container' stage is used to build a Docker
# container and push it to the project's container registry on fd.o GitLab.
# This step is only run when the tag for the container changes, else it is
# effectively a no-op. All of this infrastructure is inherited from the
# freedesktop/ci-templates repository which is the recommended way to set up CI
# infrastructure on fd.o GitLab.
#
# Once the container stage is done, we move on to the 'build' stage where we
# run meson build. Currently, tests are also run as part of the build stage as
# there doesn't seem to be significant value to splitting the stages at the
# moment.

stages:
  - container
  - build

variables:
  # Update this tag when you want to trigger a rebuild the container in which
  # CI runs, for example when adding new packages to FDO_DISTRIBUTION_PACKAGES.
  # The tag is an arbitrary string that identifies the exact container
  # contents.
  FDO_DISTRIBUTION_TAG: '2023-08-13-00'
  FDO_DISTRIBUTION_VERSION: '20.04'
  FDO_UPSTREAM_REPO: 'pulseaudio/pulseaudio'

include:
  # We pull templates from master to avoid the overhead of periodically
  # scanning for changes upstream. This does means builds might occasionally
  # break due to upstream changing things, so if you see unexpected build
  # failures, this might be one cause.
  - project: 'freedesktop/ci-templates'
    ref: 'master'
    file: '/templates/ubuntu.yml'

build-container:
  extends: .fdo.container-build@ubuntu
  stage: container
  variables:
    GIT_STRATEGY: none # no need to pull the whole tree for rebuilding the image

    # Remember to update FDO_DISTRIBUTION_TAG when modifying this package list!
    # Otherwise the changes won't have effect since an old container image will
    # be used.
    FDO_DISTRIBUTION_PACKAGES: >-
      autopoint
      bash-completion
      check
      curl
      dbus-x11
      doxygen
      g++
      gcc
      gettext
      git-core
      libasound2-dev
      libasyncns-dev
      libavahi-client-dev
      libbluetooth-dev
      libcap-dev
      libfftw3-dev
      libglib2.0-dev
      libgtk-3-dev
      libice-dev
      libjack-dev
      liblircclient-dev
      libltdl-dev
      liborc-0.4-dev
      libsbc-dev
      libsndfile1-dev
      libsoxr-dev
      libspeexdsp-dev
      libssl-dev
      libsystemd-dev
      libtdb-dev
      libudev-dev
      libwebrtc-audio-processing-dev
      libwrap0-dev
      libx11-xcb-dev
      libxcb1-dev
      libxml-parser-perl
      libxml2-utils
      libxtst-dev
      m4
      ninja-build
      pkg-config
      python3-setuptools
      systemd
      wget

build-meson:
  extends: .fdo.distribution-image@ubuntu
  stage: build
  script:
    # Install meson (higher than our min version to support our wrap file)
    - wget -q https://github.com/mesonbuild/meson/releases/download/0.63.2/meson-0.63.2.tar.gz
    - tar -xf meson-0.63.2.tar.gz
    - cd meson-0.63.2
    - python3 setup.py install
    - cd ..
    # Do the actual build
    - meson build -Dwebrtc-aec=enabled
    - cd build
    - ninja
    - ulimit -c 0 # don't dump core files on tests that are supposed to assert
    - ninja test
    - ninja test-daemon
    - ninja dist
  artifacts:
    paths:
      - build/
